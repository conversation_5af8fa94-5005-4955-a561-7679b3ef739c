# 小说云 - 多源小说搜索与下载系统

小说云是一个基于Web的小说搜索和下载系统，支持多个小说源（番茄小说和七猫小说），并将下载的小说按照章节顺序保存在SQLite数据库中。

## 项目需求

### 核心功能
1. **小说搜索与下载**
   - 支持七猫小说和番茄小说两个来源
   - 能够通过ID直接下载小说
   - 支持搜索小说功能

2. **数据存储**
   - 使用SQLite数据库存储小说内容
   - 按照novel ID建表
   - 章节按照下载顺序存储
   - 保存小说的基本信息（标题、作者等）

3. **用户界面**
   - 提供简洁美观的Web界面
   - 支持查看已下载的小说列表
   - 支持阅读小说章节内容
   - 支持章节导航（上一章/下一章）

## 功能特点

- **多源搜索**：同时搜索番茄小说和七猫小说，显示哪个源返回了结果
- **统一下载**：从任意支持的源下载小说
- **SQLite存储**：将小说按照novel ID建表，章节按照请求返回顺序保存
- **美观界面**：精心设计的Web界面，支持响应式布局
- **实时进度**：下载过程中实时显示进度
- **阅读功能**：在线阅读已下载的小说，支持多种阅读模式和字体大小调整

## 安装说明

### 环境要求
- Python 3.9
- Windows

### 安装步骤

1. 克隆项目到本地
   ```
   git clone https://github.com/yourusername/novelcloud.git
   cd novelcloud
   ```

2. 安装依赖包
   ```
   pip install -r requirements.txt
   ```

3. 运行应用
   ```
   python app.py
   ```

4. 在浏览器中访问
   ```
   http://localhost:5000
   ```

### Windows特别说明

如果在Windows系统下运行，确保已安装Python并添加到系统PATH中。可以通过以下命令检查Python版本:

```
python --version
```

### 数据存储

小说数据保存在`novels.db`文件中，第一次运行时会自动创建。

## 使用说明

1. **搜索小说**:
   - 在首页上方的搜索框中输入小说名称或作者
   - 或点击"搜索小说"按钮进入专门的搜索页面

2. **下载小说**:
   - 在搜索结果中找到想要下载的小说，点击"下载"按钮
   - 下载过程中会显示实时进度

3. **阅读小说**:
   - 在首页查看已下载的小说列表
   - 点击小说进入详情页，可以查看章节列表
   - 点击章节开始阅读
   - 阅读时可以通过左右方向键或屏幕底部的按钮切换章节

4. **阅读设置**:
   - 阅读页面右侧提供字体大小调整和阅读模式切换功能
   - 支持白色、暗色和护眼模式

本项目基于以下开源项目：
- [fanqienovel-downloader](https://github.com/ying-ck/fanqienovel-downloader)
- [7mao-novel-downloader](https://github.com/xing-yv/7mao-novel-downloader)

感谢这些项目的作者提供的代码和灵感。

## 项目进度

- [x] 项目需求分析
- [x] 数据库设计
- [x] 七猫小说下载功能整合
- [x] 番茄小说下载功能整合
- [x] Web界面实现
- [x] 阅读功能实现
- [x] 搜索功能实现
- [x] 直接通过ID下载功能
- [x] 文档完善
- [ ] 测试与优化

## 免责声明

本项目仅供学习和研究使用，请勿用于任何商业用途。使用本项目下载的小说，请在24小时内删除。请尊重版权，支持正版。
