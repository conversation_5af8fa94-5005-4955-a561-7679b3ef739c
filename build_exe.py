import os
import sys
from pathlib import Path
import PyInstaller.__main__

def build_exe():
    """构建可执行文件"""
    
    # 获取项目根目录
    project_root = Path(__file__).parent
    
    # 主要入口文件
    main_script = str(project_root / "main.py")
    
    # 收集所有需要包含的数据文件
    datas = []
    
    # 添加模板文件夹
    templates_dir = project_root / "templates"
    if templates_dir.exists():
        datas.append((str(templates_dir), "templates"))
    
    # 添加静态文件夹
    static_dir = project_root / "static"
    if static_dir.exists():
        datas.append((str(static_dir), "static"))
    
    # 添加配置文件
    config_files = ["requirements.txt", "conf.py", "config.py"]
    for config_file in config_files:
        config_path = project_root / config_file
        if config_path.exists():
            datas.append((str(config_path), "."))
    
    # 添加数据库文件
    db_file = project_root / "novels.db"
    if db_file.exists():
        datas.append((str(db_file), "."))
    
    # 添加cookies文件夹
    cookies_dir = project_root / "cookies"
    if cookies_dir.exists():
        datas.append((str(cookies_dir), "cookies"))
    
    # 添加social文件夹下的cookies目录
    social_cookies_dir = project_root / "social" / "cookies"
    if social_cookies_dir.exists():
        datas.append((str(social_cookies_dir), "social/cookies"))
    
    # 添加downloads文件夹
    downloads_dir = project_root / "downloads"
    if downloads_dir.exists():
        datas.append((str(downloads_dir), "downloads"))
    
    # 添加uploaded_videos文件夹
    uploaded_videos_dir = project_root / "uploaded_videos"
    if uploaded_videos_dir.exists():
        datas.append((str(uploaded_videos_dir), "uploaded_videos"))
    
    # 添加logs文件夹
    logs_dir = project_root / "logs"
    if logs_dir.exists():
        datas.append((str(logs_dir), "logs"))
    
    # 添加social/logs文件夹
    social_logs_dir = project_root / "social" / "logs"
    if social_logs_dir.exists():
        datas.append((str(social_logs_dir), "social/logs"))
    
    # 添加app模块的所有子模块
    hidden_imports = [
        "app.core",
        "app.core.bk_asr",
        "app.core.entities",
        "app.core.storage",
        "app.core.subtitle_processor",
        "app.core.task_factory",
        "app.core.utils",
        "app.localtrancribe",
        "app.thread",
        "app.view",
        "novel_sources",
        "novel_sources.fanqie",
        "novel_sources.qimao",
        "novel_sources.fanqie_api",
        "novel_sources.swiftcat_downloader_new",
        "social",
        "social.uploader",
        "social.uploader.douyin_uploader",
        "social.uploader.bilibili_uploader",
        "social.uploader.tencent_uploader",
        "social.utils",
        "edge_tts",
        "cv2",
        "PIL",
        "pydub",
        "hashlib",
        "tempfile",
        "asyncio",
        "sqlite3",
        "logging",
        "shutil",
        "uuid",
        "queue",
        "re",
        "subprocess",
        "sys",
        "json",
        "time",
        "datetime",
        "urllib.parse",
        "glob",
        # 添加关键的网络和解析库
        "requests",
        "requests.adapters",
        "requests.auth",
        "requests.cookies",
        "requests.models",
        "requests.sessions",
        "requests.structures",
        "requests.utils",
        "urllib3",
        "urllib3.util",
        "urllib3.util.retry",
        "urllib3.poolmanager",
        "lxml",
        "lxml.etree",
        "lxml.html",
        "tqdm",
        "flask",
        "werkzeug",
        "jinja2",
        "itsdangerous",
        "click",
        "certifi",
        "charset_normalizer",
        "idna",
        "markupsafe",
        "blinker",
        "colorama",
    ]
    
    # 构建PyInstaller命令参数
    args = [
        main_script,
        "--name=小说云",
        # "--windowed",  # 暂时注释掉，以便看到错误信息
        "--onefile",   # 打包成单个文件
        "--clean",
        "--noconfirm",
    ]
    
    # 添加数据文件
    data_mappings = [
        (templates_dir, "templates"),
        (static_dir, "static"),
    ]

    for source_dir, target_dir in data_mappings:
        if source_dir and source_dir.exists():
            separator = ";" if os.name == 'nt' else ":"
            args.extend(["--add-data", f"{source_dir}{separator}{target_dir}"])
    
    # 添加其他已经检查过存在性的目录
    for data_path, target_path in datas:
        separator = ";" if os.name == 'nt' else ":"
        args.extend(["--add-data", f"{data_path}{separator}{target_path}"])
    
    # 添加隐藏导入
    for hidden_import in hidden_imports:
        args.extend(["--hidden-import", hidden_import])
    
    # 添加额外的PyInstaller选项
    favicon_path = project_root / "static" / "favicon.ico"
    if favicon_path.exists():
        args.extend(["--icon", str(favicon_path)])
    
    args.extend([
        "--distpath", str(project_root / "dist"),
        "--workpath", str(project_root / "build"),
        "--specpath", str(project_root),
    ])
    
    print("开始构建可执行文件...")
    print("命令参数:", args)
    
    try:
        PyInstaller.__main__.run(args)
        print("构建完成！")
        print(f"可执行文件位置: {project_root / 'dist' / '小说云.exe'}")
    except Exception as e:
        print(f"构建失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    build_exe()