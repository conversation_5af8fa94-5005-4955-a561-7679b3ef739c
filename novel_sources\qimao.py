import sys
import os
import time
import json
import requests
import hashlib
import random
import base64
import re
from tqdm import tqdm

# 七猫小说下载器
class QimaoDownloader:
    # 类级别的sign_key，在实例没有时使用这个作为备份
    DEFAULT_SIGN_KEY = 'd3dGiJc651gSQ8w1'
    
    def __init__(self, novel_id, db, progress_tracker=None):
        self.novel_id = novel_id
        self.db = db
        self.progress_tracker = progress_tracker
        self.timeout = 10  # 减少超时时间，从30秒改为10秒
        self.max_retries = 2  # 减少重试次数，从5次改为2次
        # 类级别的sign_key，在实例没有时使用这个作为备份
        self.sign_key = self.DEFAULT_SIGN_KEY  # 七猫签名密钥
        self.last_error = None  # 保存最后一次错误信息
        self.headers = self.get_headers(novel_id)
        self.proxies = None
    
    def get_headers(self, book_id):
        """获取请求头，模拟APP请求"""
        version_list = [
            '73720', '73700',
            '73620', '73600',
            '73500',
            '73420', '73400',
            '73328', '73325', '73320', '73300',
            '73220', '73200',
            '73100', '73000', '72900',
            '72820', '72800',
            '70720', '62010', '62112',
        ]

        random.seed(book_id)
        version = random.choice(version_list)

        headers = {
            "AUTHORIZATION": "",
            "app-version": f"{version}",
            "application-id": "com.kmxs.reader",
            "channel": "unknown",
            "net-env": "1",
            "platform": "android",
            "qm-params": "",
            "reg": "0",
        }

        # 获取 headers 的所有键并排序
        keys = sorted(headers.keys())

        # 确保sign_key可用，使用默认值作为备份
        sign_key = getattr(self, 'sign_key', self.DEFAULT_SIGN_KEY)
        
        # 生成待签名的字符串
        sign_str = ''.join([k + '=' + str(headers[k]) for k in keys]) + sign_key

        # 生成签名
        headers['sign'] = hashlib.md5(sign_str.encode()).hexdigest()

        return headers
    
    def sign_url_params(self, params):
        """给请求参数添加签名"""
        keys = sorted(params.keys())
        
        # 确保sign_key可用，使用默认值作为备份
        sign_key = getattr(self, 'sign_key', self.DEFAULT_SIGN_KEY)

        # 生成待签名的字符串
        sign_str = ''.join([k + '=' + str(params[k]) for k in keys]) + sign_key

        # 使用MD5哈希生成签名
        signature = hashlib.md5(sign_str.encode()).hexdigest()

        # 将签名添加到参数字典中
        params['sign'] = signature

        return params
    
    @staticmethod
    def search(keyword):
        """Search for novels on QiMao"""
        try:
            url = "https://api-bc.wtzw.com/api/v1/search/search"
            params = {
                'keyword': keyword,
                'page': 1,
                'type': 1
            }
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            }
            response = requests.get(url, params=params, headers=headers, timeout=30)
            data = response.json()
            
            results = []
            if data.get('code') == 0 and 'data' in data:
                for item in data['data'].get('list', []):
                    results.append({
                        'id': item.get('id'),
                        'title': item.get('title'),
                        'author': item.get('author'),
                        'cover_url': item.get('cover_url'),
                        'description': item.get('intro'),
                        'source': 'qimao'
                    })
            return results
        except Exception as e:
            print(f"Error searching QiMao: {e}")
            return []
    
    def _make_request_with_retry(self, url, params=None, method="get"):
        """Make a request with retry mechanism"""
        last_exception = None
        for attempt in range(self.max_retries):
            try:
                if method.lower() == "get":
                    response = requests.get(url, params=params, headers=self.headers, timeout=self.timeout, proxies=self.proxies)
                else:
                    response = requests.post(url, data=params, headers=self.headers, timeout=self.timeout, proxies=self.proxies)
                
                # 简化调试输出，减少日志量
                if attempt == 0:  # 只在第一次尝试时打印详细信息
                    print(f"Request URL: {url}")
                    print(f"Response status: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        # 快速检测API错误
                        if data and data.get('errors'):
                            # 有错误直接返回失败，不再重试
                            print(f"API returned error: {data.get('errors')}")
                            return None
                        return data
                    except json.JSONDecodeError as e:
                        last_exception = e
                        if attempt == 0:  # 只在第一次错误时输出详细信息
                            print(f"JSON decode error: {e}")
                elif response.status_code == 404:
                    # 404错误直接返回，不再重试
                    print(f"API endpoint not found (404)")
                    return None
                else:
                    print(f"Request failed with status code {response.status_code}, attempt {attempt+1}/{self.max_retries}")
                    last_exception = Exception(f"HTTP Error: {response.status_code}")
            except Exception as e:
                print(f"Request error on attempt {attempt+1}/{self.max_retries}: {e}")
                last_exception = e
                
            # 等待后重试 (指数退避，但使用更短的基础时间)
            if attempt < self.max_retries - 1:
                sleep_time = 0.5 * (2 ** attempt) + random.random() * 0.5  # 更短的等待时间
                print(f"Retrying in {sleep_time:.2f} seconds...")
                time.sleep(sleep_time)
        
        # 所有重试都失败
        if last_exception:
            self.last_error = f"All {self.max_retries} attempts failed: {last_exception}"
        else:
            self.last_error = f"All {self.max_retries} attempts failed with unknown error"
        return None
    
    def get_novel_info(self):
        """Get novel information from QiMao"""
        try:
            # 使用七猫小说的API获取小说信息
            url = f"https://api-bc.wtzw.com/api/v1/reader/detail"
            params = {
                'id': self.novel_id
            }
            
            # 尝试不同的方式获取数据
            try:
                # 先尝试使用带签名的请求
                signed_params = self.sign_url_params(params.copy())
                data = self._make_request_with_retry(url, signed_params)
                
                if not data or data.get('code', -1) != 0:
                    print("Signed request failed, trying without signature...")
                    # 如果失败，尝试不带签名的请求
                    response = requests.get(url, params=params, timeout=self.timeout, proxies=self.proxies)
                    if response.status_code == 200:
                        data = response.json()
                    else:
                        print(f"Failed to get novel info: HTTP {response.status_code}")
                        return None
            except Exception as e:
                print(f"Error in get_novel_info with signed request: {e}")
                # 尝试不带签名的直接请求
                response = requests.get(url, params=params, timeout=self.timeout, proxies=self.proxies)
                if response.status_code == 200:
                    data = response.json()
                else:
                    print(f"Failed to get novel info: HTTP {response.status_code}")
                    return None
            
            # 检查数据是否有效并打印详细信息
            print(f"API Response data: {data}")
            
            # 尝试多种可能的响应格式
            if data and 'data' in data:
                book_info = data['data']
                if book_info:
                    # 保存chapter_ver供后续章节列表使用
                    self.chapter_ver = book_info.get('chapter_ver', '0')
                    print(f"Extracted chapter_ver: {self.chapter_ver}")
                    
                    # 尝试提取章节信息(有些API响应直接包含章节列表)
                    self.first_chapter_id = book_info.get('first_chapter_id')
                    self.latest_chapter_id = book_info.get('latest_chapter_id')
                    self.total_chapters = book_info.get('chapters')
                    
                    if self.first_chapter_id:
                        print(f"First chapter ID: {self.first_chapter_id}")
                    
                    # 创建小说信息对象
                    novel_info = {
                        'id': self.novel_id,
                        'title': book_info.get('title', '未知标题'),
                        'author': book_info.get('author', '未知作者'),
                        'cover_url': book_info.get('image_link', book_info.get('big_image_link', '')),
                        'description': book_info.get('intro', '无描述'),
                        'source': 'qimao'
                    }
                    print(f"Successfully extracted novel info: {novel_info}")
                    return novel_info
                else:
                    print("No book_info found in response")
            elif data and 'message' in data:
                print(f"API returned error: {data.get('message')}")
            else:
                print(f"Unexpected API response format: {data}")
            
            # 尝试手动提取信息，即使响应格式不符合预期
            if isinstance(data, dict):
                for key, value in data.items():
                    if isinstance(value, dict) and 'title' in value and 'author' in value:
                        self.chapter_ver = value.get('chapter_ver', '0')
                        self.first_chapter_id = value.get('first_chapter_id')
                        self.latest_chapter_id = value.get('latest_chapter_id')
                        self.total_chapters = value.get('chapters')
                        
                        novel_info = {
                            'id': self.novel_id,
                            'title': value.get('title', '未知标题'),
                            'author': value.get('author', '未知作者'),
                            'cover_url': value.get('image_link', value.get('big_image_link', '')),
                            'description': value.get('intro', '无描述'),
                            'source': 'qimao'
                        }
                        print(f"Manually extracted novel info: {novel_info}")
                        return novel_info
            
            return None
        except Exception as e:
            self.last_error = f"Error getting novel info: {e}"
            print(self.last_error)
            return None
    
    def get_chapters(self):
        """获取真实章节列表"""
        try:
            # 使用API获取完整章节列表
            params = {
                'chapter_ver': '0',
                'id': self.novel_id,
            }
            
            print(f"正在获取小说{self.novel_id}的章节列表...")
            
            # 使用签名参数
            signed_params = self.sign_url_params(params)
            headers = self.get_headers(self.novel_id)
            
            # 发送请求获取章节列表
            response = requests.get(
                "https://api-ks.wtzw.com/api/v1/chapter/chapter-list",
                params=signed_params,
                headers=headers,
                proxies=self.proxies,
                timeout=12
            )
            
            if response.status_code != 200:
                print(f"获取章节列表失败: HTTP {response.status_code}")
                return self._generate_fallback_chapters()
            
            data = response.json()
            
            # 验证响应数据
            if not data or 'data' not in data or 'chapter_lists' not in data['data']:
                print(f"章节列表响应格式无效: {data}")
                return self._generate_fallback_chapters()
            
            # 提取章节列表
            chapters = data['data']['chapter_lists']
            
            # 按章节顺序排序
            chapters.sort(key=lambda x: x.get('chapter_sort', 0))
            
            # 转换为我们需要的格式
            formatted_chapters = []
            for i, chapter in enumerate(chapters):
                formatted_chapters.append({
                    'chapter_id': chapter['id'],
                    'title': chapter['title'],
                    'order_index': i
                })
            
            print(f"成功获取 {len(formatted_chapters)} 个章节")
            return formatted_chapters
            
        except Exception as e:
            self.last_error = f"获取章节列表出错: {e}"
            print(self.last_error)
            
            # 出错时使用备用方法
            return self._generate_fallback_chapters()
    
    def _generate_fallback_chapters(self):
        """生成备用章节列表（当API请求失败时使用）"""
        # 如果可以从first_chapter_id生成章节ID
        if hasattr(self, 'first_chapter_id') and self.first_chapter_id and \
           hasattr(self, 'total_chapters') and self.total_chapters:
            try:
                total_chapters = int(self.total_chapters)
                if total_chapters > 0 and total_chapters < 3000:
                    print(f"使用first_chapter_id生成 {total_chapters} 个章节...")
                    
                    chapters = []
                    chapter_base = str(self.first_chapter_id)[:-4]  # 章节ID前缀
                    chapter_num = int(str(self.first_chapter_id)[-4:])  # 章节ID后四位
                    
                    for i in range(total_chapters):
                        chapter_id = f"{chapter_base}{(chapter_num + i):04d}"
                        chapters.append({
                            'chapter_id': chapter_id,
                            'title': f'第{i+1}章',
                            'order_index': i
                        })
                    
                    return chapters
            except Exception as e:
                print(f"生成章节列表出错: {e}")
        
        # 生成通用章节列表
        print("生成通用章节列表...")
        chapters = []
        for i in range(1, 101):
            chapters.append({
                'chapter_id': str(i),
                'title': f'第{i}章',
                'order_index': i-1
            })
        return chapters
    
    def get_chapter_content(self, chapter_id):
        """获取章节内容，完全按照原始7mao项目的逻辑实现"""
        max_retries = 3  # 与原始代码一致
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # 与原始send_request完全一致
                headers = self.get_headers(self.novel_id)
                
                params = {
                    "id": self.novel_id,
                    "chapterId": chapter_id,
                }
                
                params = self.sign_url_params(params)
                
                if retry_count == 0:  # 仅首次尝试打印详细信息
                    print(f"获取章节 {chapter_id}")
                
                # 直接发送请求，不检查状态码
                response = requests.get(
                    "https://api-ks.wtzw.com/api/v1/chapter/content", 
                    headers=headers, 
                    params=params,
                    timeout=10, 
                    proxies=self.proxies
                )
                
                # 直接解析JSON，不检查HTTP状态
                response_json = response.json()
                
                # 检查返回的数据是否有效，与原始get_api函数的逻辑一致
                if "data" in response_json and "content" in response_json["data"]:
                    # 获取加密内容
                    encrypted_content = response_json["data"]["content"]
                    
                    # 使用与原始代码相同的解密逻辑
                    decrypted_content = self.decrypt_qimao(encrypted_content)
                    decrypted_content = re.sub('<br>', '\n', decrypted_content)
                    
                    # 成功获取内容，直接返回
                    return decrypted_content
                else:
                    # 如果没有找到内容，进行重试
                    if retry_count == 0:
                        print(f"获取失败，正在尝试重试...")
                    print(f"第 ({retry_count+1}/{max_retries}) 次重试获取章节内容")
                    retry_count += 1
                    time.sleep(1)  # 短暂延迟后重试
                    continue
            
            except Exception as e:
                # 与原始代码一致的异常处理
                print(f"发生异常: {e}")
                if retry_count == 0:
                    print(f"获取失败，正在尝试重试...")
                print(f"第 ({retry_count+1}/{max_retries}) 次重试获取章节内容")
                retry_count += 1
                time.sleep(1)
                continue
        
        # 达到最大重试次数后，返回一个占位内容
        print(f"无法获取章节内容: {chapter_id}")
        return f"无法获取章节ID为{chapter_id}的内容。\n请检查网络连接或稍后再试。"
    
    def decrypt_qimao(self, content):
        """解密七猫加密的章节内容"""
        txt = base64.b64decode(content)
        iv = txt[:16].hex()
        fntxt = self.decrypt(txt[16:].hex(), iv).strip().replace('\n', '<br>')
        return fntxt
    
    def decrypt(self, data, iv):
        """AES解密处理"""
        from Crypto.Cipher import AES
        from Crypto.Util.Padding import unpad
        
        key = bytes.fromhex('32343263636238323330643730396531')
        iv_bytes = bytes.fromhex(iv)
        cipher = AES.new(key, AES.MODE_CBC, iv=iv_bytes)
        decrypted = unpad(cipher.decrypt(bytes.fromhex(data)), AES.block_size)
        return decrypted.decode('utf-8')
    
    def download(self):
        """Download the entire novel"""
        # Get novel info
        novel_info = self.get_novel_info()
        if not novel_info:
            error_msg = f'Failed to get novel info for ID {self.novel_id}. Please check the novel ID or try again later.'
            if self.last_error:
                error_msg += f" Error: {self.last_error}"
            print(error_msg)
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = error_msg
            return False
        
        # Save novel info to database
        self.db.save_novel(novel_info)
        print(f"Novel info saved: {novel_info['title']} by {novel_info['author']}")
        
        # Get all chapters
        chapters = self.get_chapters()
        if not chapters:
            error_msg = f'Failed to get chapters for novel ID {self.novel_id}. The novel might not exist or be accessible.'
            if self.last_error:
                error_msg += f" Error: {self.last_error}"
            print(error_msg)
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = error_msg
            return False
        
        print(f"Found {len(chapters)} chapters")
        
        # Update progress tracker
        if self.progress_tracker:
            self.progress_tracker[self.novel_id]['total'] = len(chapters)
            self.progress_tracker[self.novel_id]['current'] = 0
            self.progress_tracker[self.novel_id]['message'] = f"Downloading {len(chapters)} chapters..."
        
        # 下载章节，使用批量处理提高速度
        success_count = 0
        error_count = 0
        
        # 使用更小的延迟
        delay = 0.1
        
        for chapter in tqdm(chapters, desc=f"Downloading {novel_info['title']}"):
            try:
                # 获取章节标题
                chapter_title = chapter['title']
                chapter_id = chapter['chapter_id']
                
                # 更新进度信息
                if self.progress_tracker:
                    self.progress_tracker[self.novel_id]['message'] = f"Downloading chapter {success_count+1}/{len(chapters)}: {chapter_title}"
                
                # 获取章节内容
                content = self.get_chapter_content(chapter_id)
                if content and not content.startswith("无法获取章节ID为"):
                    # 构建完整章节数据，包括正确的标题
                    chapter_data = {
                        'chapter_id': chapter_id,
                        'title': chapter_title,  # 使用API返回的真实标题
                        'content': content,
                        'order_index': chapter['order_index']
                    }
                    self.db.save_chapter(self.novel_id, chapter_data)
                    success_count += 1
                    print(f"已下载: {chapter_title}")
                else:
                    error_count += 1
                    print(f"获取失败: {chapter_title}")
                
                # Update progress
                if self.progress_tracker:
                    self.progress_tracker[self.novel_id]['current'] += 1
                
                # 使用更小的随机延迟
                time.sleep(delay + random.random() * 0.1)
            except Exception as e:
                error_count += 1
                print(f"下载章节 {chapter.get('title', 'unknown')} 时出错: {e}")
                # 继续下一章，不中断整个下载过程
        
        # Report download completion with statistics
        if self.progress_tracker:
            if success_count == 0:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = f'Failed to download any chapters. {error_count} errors occurred.'
            else:
                if success_count == len(chapters):
                    self.progress_tracker[self.novel_id]['status'] = 'completed'
                    self.progress_tracker[self.novel_id]['message'] = f'Successfully downloaded all {success_count} chapters!'
                else:
                    self.progress_tracker[self.novel_id]['status'] = 'partial'
                    self.progress_tracker[self.novel_id]['message'] = f'Downloaded {success_count}/{len(chapters)} chapters. {error_count} errors occurred.'
        
        print(f"Download completed: {success_count} chapters successful, {error_count} failures")
        return success_count > 0
    
    def update(self):
        """Update an existing novel with new chapters"""
        # 获取小说信息
        novel_info = self.get_novel_info()
        if not novel_info:
            error_msg = f'更新失败: 无法获取小说信息 (ID: {self.novel_id})'
            if self.last_error:
                error_msg += f" 错误: {self.last_error}"
            print(error_msg)
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = error_msg
            return False
            
        print(f"正在更新小说: {novel_info['title']} 作者: {novel_info['author']}")
        
        # 获取数据库中已有的章节
        local_chapters = self.db.get_chapters(self.novel_id)
        local_chapter_ids = set(chapter['chapter_id'] for chapter in local_chapters)
        
        # 获取网络上所有章节
        online_chapters = self.get_chapters()
        if not online_chapters:
            error_msg = f'无法获取章节列表 (ID: {self.novel_id})'
            if self.last_error:
                error_msg += f" 错误: {self.last_error}"
            print(error_msg)
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = error_msg
            return False
            
        # 找出新章节（网络章节中不在本地的）
        new_chapters = [chapter for chapter in online_chapters if chapter['chapter_id'] not in local_chapter_ids]
        
        if self.progress_tracker is not None:
            # 检查 novel_id 是否存在于 progress_tracker 中
            if self.novel_id not in self.progress_tracker:
                # 如果不存在，初始化该novel_id的跟踪数据
                self.progress_tracker[self.novel_id] = {
               'status': 'pending',
               'total': 0,
               'current': 0,
               'message': '准备更新...'
            }
            self.progress_tracker[self.novel_id]['total'] = len(new_chapters)
            self.progress_tracker[self.novel_id]['current'] = 0
            
        if not new_chapters:
            print(f"小说 '{novel_info['title']}' 已是最新，没有新章节")
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'completed'
                self.progress_tracker[self.novel_id]['message'] = '小说已是最新，没有新章节'
            return False
            
        print(f"发现 {len(new_chapters)} 个新章节，开始下载...")
        
        # 下载新章节
        success_count = 0
        error_count = 0
        delay = 0.1
        
        for chapter in tqdm(new_chapters, desc=f"更新 {novel_info['title']}"):
            try:
                # 获取章节详情
                chapter_title = chapter['title']
                chapter_id = chapter['chapter_id']
                
                # 更新进度信息
                if self.progress_tracker:
                    self.progress_tracker[self.novel_id]['message'] = f"下载新章节 {success_count+1}/{len(new_chapters)}: {chapter_title}"
                
                # 获取章节内容
                content = self.get_chapter_content(chapter_id)
                if content and not content.startswith("无法获取章节ID为"):
                    # 保存章节
                    chapter_data = {
                        'chapter_id': chapter_id,
                        'title': chapter_title,
                        'content': content,
                        'order_index': chapter['order_index']
                    }
                    self.db.save_chapter(self.novel_id, chapter_data)
                    success_count += 1
                    print(f"已下载: {chapter_title}")
                else:
                    error_count += 1
                    print(f"获取失败: {chapter_title}")
                
                # 更新进度
                if self.progress_tracker:
                    self.progress_tracker[self.novel_id]['current'] += 1
                
                # 延迟以避免请求过快
                time.sleep(delay + random.random() * 0.1)
            except Exception as e:
                error_count += 1
                print(f"下载章节 {chapter.get('title', '未知')} 时出错: {e}")
        
        # 报告更新完成情况
        if self.progress_tracker:
            if success_count == 0:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = f'未能下载任何新章节，发生 {error_count} 个错误'
            else:
                if success_count == len(new_chapters):
                    self.progress_tracker[self.novel_id]['status'] = 'completed'
                    self.progress_tracker[self.novel_id]['message'] = f'成功下载所有 {success_count} 个新章节！'
                else:
                    self.progress_tracker[self.novel_id]['status'] = 'partial'
                    self.progress_tracker[self.novel_id]['message'] = f'下载了 {success_count}/{len(new_chapters)} 个新章节，发生 {error_count} 个错误'
        
        print(f"更新完成: {success_count} 章成功, {error_count} 章失败")
        return success_count > 0 