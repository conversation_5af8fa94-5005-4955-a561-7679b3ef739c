/* Global Styles */
body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif !important;
    background-color: #f5f7fa !important;
    color: #333 !important;
    line-height: 1.6 !important;
    padding-bottom: 70px !important;
}

.navbar {
    background: linear-gradient(135deg, #4e54c8, #8f94fb) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    padding: 0.8rem !important;
}

.navbar-brand {
    font-weight: bold !important;
    font-size: 1.5rem !important;
    letter-spacing: 0.5px !important;
}

.nav-link {
    font-weight: 500 !important;
    padding: 0.5rem 1rem !important;
    transition: all 0.3s !important;
}

.nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1) !important;
    transform: translateY(-2px) !important;
}

/* Novel Card Styles */
.novel-card {
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100% !important;
}

.novel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

.novel-cover-container {
    height: 220px !important;
    overflow: hidden !important;
    position: relative !important;
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    background-color: #f0f0f0 !important;
}

.novel-cover {
    object-fit: cover !important;
    width: 100% !important;
    height: 100% !important;
    transition: transform 0.5s !important;
}

.novel-card:hover .novel-cover {
    transform: scale(1.05) !important;
}

.no-cover {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(120deg, #e0e0e0, #f5f5f5) !important;
    color: #999 !important;
    font-size: 3rem !important;
}

.no-cover-small {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100px;
    background-color: #e9ecef;
    color: #6c757d;
    font-size: 2rem;
}

.novel-search-cover {
    height: 100px;
    object-fit: cover;
}

.novel-search-card {
    border-left: 4px solid transparent !important;
    transition: all 0.3s !important;
}

.novel-search-card:hover {
    border-left-color: #4e54c8 !important;
}

/* Novel Detail Page */
.novel-detail-cover-container {
    width: 100%;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.novel-detail-cover {
    width: 100%;
    object-fit: cover;
}

.no-cover-detail {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 300px;
    background-color: #e9ecef;
    color: #6c757d;
    font-size: 4rem;
}

/* Chapter Card */
.chapter-card {
    background-color: #f9fafc !important;
    transition: all 0.2s !important;
    border-radius: 8px !important;
}

.chapter-card:hover {
    background-color: #eef1f8 !important;
    color: #4e54c8 !important;
}

/* Reading Page */
.chapter-content {
    font-size: 1.15rem !important;
    line-height: 1.8 !important;
    letter-spacing: 0.3px !important;
    text-align: justify !important;
    white-space: pre-wrap !important;
    padding: 1rem !important;
}

.chapter-nav {
    position: sticky !important;
    bottom: 20px !important;
    z-index: 100 !important;
}

.reading-settings {
    position: fixed !important;
    right: 20px !important;
    top: 100px !important;
    z-index: 101 !important;
}

/* Progress bar */
.download-progress {
    height: 5px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .novel-cover-container {
        height: 180px !important;
    }
    
    .no-cover-detail {
        height: 200px;
    }
    
    .chapter-nav {
        width: 90% !important;
        margin: 0 auto !important;
    }
}

@media (max-width: 576px) {
    .novel-cover-container {
        height: 150px;
    }
}

/* Search Form */
.search-form input {
    border-radius: 30px 0 0 30px !important;
    border: none !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06) !important;
    padding: 0.75rem 1.5rem !important;
}

.search-form button {
    border-radius: 0 30px 30px 0 !important;
    padding: 0.75rem 1.5rem !important;
}

/* Simple Navbar */
.simple-navbar {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 1rem !important;
    background-color: #4e54c8 !important;
    color: white !important;
}

.simple-navbar a {
    color: white !important;
    text-decoration: none !important;
    font-weight: bold !important;
    margin-right: 1rem !important;
}

.simple-search-form {
    margin: 2rem 0 !important;
    display: flex !important;
    max-width: 600px !important;
}

.simple-search-form input {
    flex: 1 !important;
    padding: 0.5rem !important;
    border: 1px solid #ddd !important;
    border-right: none !important;
}

.simple-search-form button {
    padding: 0.5rem 1rem !important;
    background-color: #4e54c8 !important;
    color: white !important;
    border: none !important;
    cursor: pointer !important;
}

h1, h2, h3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
    font-weight: bold !important;
}

ul {
    margin-left: 1.5rem !important;
}

li {
    margin-bottom: 0.5rem !important;
}

/* 强制应用 */
* {
    box-sizing: border-box !important;
} 