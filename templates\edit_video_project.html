{% extends "base.html" %}

{% block title %}编辑视频项目 - 小说云{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/novel_to_video.css') }}">
{% endblock %}

{% block content %}
<div class="container">
    <div class="card shadow-sm mb-4">
        <div class="card-header">
            <h1 class="h3 mb-0">{{ project.title }}</h1>
        </div>
        <div class="card-body">
            <ul class="nav nav-tabs" id="projectTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link {{ 'active' if active_tab == 'chapters' }}" id="chapters-tab" data-bs-toggle="tab" data-bs-target="#chapters" type="button" role="tab" aria-controls="chapters" aria-selected="{{ 'true' if active_tab == 'chapters' else 'false' }}">章节选择</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link {{ 'active' if active_tab == 'voice' }}" id="voice-tab" data-bs-toggle="tab" data-bs-target="#voice" type="button" role="tab" aria-controls="voice" aria-selected="{{ 'true' if active_tab == 'voice' else 'false' }}">语音设置</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link {{ 'active' if active_tab == 'render' }}" id="render-tab" data-bs-toggle="tab" data-bs-target="#render" type="button" role="tab" aria-controls="render" aria-selected="{{ 'true' if active_tab == 'render' else 'false' }}">视频渲染</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link {{ 'active' if active_tab == 'subtitle' }}" id="subtitle-tab" data-bs-toggle="tab" data-bs-target="#subtitle" type="button" role="tab" aria-controls="subtitle" aria-selected="{{ 'true' if active_tab == 'subtitle' else 'false' }}">字幕设置</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link {{ 'active' if active_tab == 'music' }}" id="music-tab" data-bs-toggle="tab" data-bs-target="#music" type="button" role="tab" aria-controls="music" aria-selected="{{ 'true' if active_tab == 'music' else 'false' }}">背景音乐</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link {{ 'active' if active_tab == 'social_media' }}" id="social-media-tab" data-bs-toggle="tab" data-bs-target="#social_media" type="button" role="tab" aria-controls="social_media" aria-selected="{{ 'true' if active_tab == 'social_media' else 'false' }}">社交媒体上传</button>
                </li>
            </ul>
            <div class="tab-content mt-3" id="projectTabsContent">
                <!-- 章节选择 -->
                <div class="tab-pane fade {{ 'show active' if active_tab == 'chapters' }}" id="chapters" role="tabpanel" aria-labelledby="chapters-tab">
                    {% if project.novel_id %}
                    <div class="mb-3">
                        <h4 class="h5">选择章节</h4>
                        <p class="text-muted">从《{{ novel.title }}》中选择要包含在视频中的章节</p>
                        
                        <form id="chaptersForm" action="{{ url_for('update_project_chapters', project_id=project.id) }}" method="post">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th width="50px">
                                                <div class="form-check">
                                                    <input class="form-check-input" type="checkbox" id="selectAllChapters">
                                                </div>
                                            </th>
                                            <th>章节名称</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for chapter in novel_chapters %}
                                        <tr>
                                            <td>
                                                <div class="form-check">
                                                    <input class="form-check-input chapter-checkbox" type="checkbox" name="chapter_ids" value="{{ chapter.chapter_id }}" 
                                                           {{ 'checked' if chapter.chapter_id in selected_chapter_ids }}>
                                                </div>
                                            </td>
                                            <td>{{ chapter.title }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            <button type="submit" class="btn btn-primary">保存章节选择</button>
                        </form>
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>此项目未关联小说，无法选择章节
                    </div>
                    {% endif %}
                </div>
                
                <!-- 语音设置 -->
                <div class="tab-pane fade {{ 'show active' if active_tab == 'voice' }}" id="voice" role="tabpanel" aria-labelledby="voice-tab">
                    <form id="voiceForm" action="{{ url_for('update_video_voice', project_id=project.id) }}" method="post">
                        <div class="mb-3">
                            <label for="voiceSelect" class="form-label">选择语音</label>
                            <select class="form-select" id="voiceSelect" name="voice_id">
                                {% for voice in tts_voices %}
                                <option value="{{ voice.voice_id }}" {{ 'selected' if settings and settings.voice_id == voice.voice_id }}>
                                    {{ voice.display_name }} ({{ voice.language }}, {{ voice.gender }})
                                </option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="voiceRate" class="form-label">语速 ({{ settings.voice_rate if settings else 1.0 }})</label>
                            <input type="range" class="form-range" id="voiceRate" name="voice_rate" min="0.5" max="2.0" step="0.1" 
                                  value="{{ settings.voice_rate if settings else 1.0 }}">
                        </div>
                        <div class="mb-3">
                            <label for="voiceVolume" class="form-label">音量 ({{ settings.voice_volume if settings else 1.0 }})</label>
                            <input type="range" class="form-range" id="voiceVolume" name="voice_volume" min="0.1" max="2.0" step="0.1"
                                  value="{{ settings.voice_volume if settings else 1.0 }}">
                        </div>
                        <button type="submit" class="btn btn-primary">保存语音设置</button>
                    </form>
                </div>
                
                <!-- 字幕设置 -->
                <div class="tab-pane fade {{ 'show active' if active_tab == 'subtitle' }}" id="subtitle" role="tabpanel" aria-labelledby="subtitle-tab">
                    <div class="row">
                        <div class="col-md-6">
                            <form id="subtitleForm" action="{{ url_for('update_video_subtitle', project_id=project.id) }}" method="post">
                                <div class="mb-3">
                                    <label for="subtitleFont" class="form-label">字幕字体</label>
                                    <input type="text" class="form-control" id="subtitleFont" name="subtitle_font" value="{{ settings.subtitle_font if settings else 'SimHei' }}">
                                </div>
                                <div class="mb-3">
                                    <label for="subtitleSize" class="form-label">字幕大小</label>
                                    <input type="number" class="form-control" id="subtitleSize" name="subtitle_size" value="{{ settings.subtitle_size if settings else 40 }}">
                                </div>
                                <div class="mb-3">
                                    <label for="subtitleColor" class="form-label">字幕颜色</label>
                                    <input type="color" class="form-control" id="subtitleColor" name="subtitle_color" value="{{ settings.subtitle_color if settings else '#FFFFFF' }}">
                                </div>
                                <div class="mb-3">
                                    <label for="subtitlePosition" class="form-label">字幕位置</label>
                                    <select class="form-select" id="subtitlePosition" name="subtitle_position" onchange="toggleCustomPosition('subtitle')">
                                        <option value="bottom" {{ 'selected' if settings and settings.subtitle_position == 'bottom' }}>底部</option>
                                        <option value="middle" {{ 'selected' if settings and settings.subtitle_position == 'middle' }}>中间</option>
                                        <option value="top" {{ 'selected' if settings and settings.subtitle_position == 'top' }}>顶部</option>
                                        <option value="custom" {{ 'selected' if settings and settings.subtitle_position == 'custom' }}>自定义坐标</option>
                                    </select>
                                </div>
                                
                                <div id="subtitleCustomPosition" class="mb-3 row" style="display: {{ 'flex' if settings and settings.subtitle_position == 'custom' else 'none' }}">
                                    <div class="col-md-6">
                                        <label for="subtitleX" class="form-label">X坐标</label>
                                        <input type="number" class="form-control" id="subtitleX" name="subtitle_x" value="{{ settings.subtitle_x if settings and settings.subtitle_x is not none else (settings.video_width//2 if settings and settings.video_width is not none else 640) }}">
                                        <small class="text-muted">0为左边缘，{{ settings.video_width if settings and settings.video_width else 1280 }}为右边缘</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="subtitleY" class="form-label">Y坐标</label>
                                        <input type="number" class="form-control" id="subtitleY" name="subtitle_y" value="{{ settings.subtitle_y if settings and settings.subtitle_y is not none else (settings.video_height-50 if settings and settings.video_height is not none else 670) }}">
                                        <small class="text-muted">0为顶部边缘，{{ settings.video_height if settings and settings.video_height else 720 }}为底部边缘</small>
                                    </div>
                                </div>
                            
                                <div class="mb-3">
                                    <label for="titleText" class="form-label">视频标题</label>
                                    <input type="text" class="form-control" id="titleText" name="title_text" value="{{ settings.title_text if settings else project.title }}">
                                </div>
                                <div class="mb-3">
                                    <label for="titleFont" class="form-label">标题字体</label>
                                    <input type="text" class="form-control" id="titleFont" name="title_font" value="{{ settings.title_font if settings else 'SimHei' }}">
                                </div>
                                <div class="mb-3">
                                    <label for="titleSize" class="form-label">标题大小</label>
                                    <input type="number" class="form-control" id="titleSize" name="title_size" value="{{ settings.title_size if settings else 60 }}">
                                </div>
                                <div class="mb-3">
                                    <label for="titleColor" class="form-label">标题颜色</label>
                                    <input type="color" class="form-control" id="titleColor" name="title_color" value="{{ settings.title_color if settings else '#FFFFFF' }}">
                                </div>
                                <div class="mb-3">
                                    <label for="titlePosition" class="form-label">标题位置</label>
                                    <select class="form-select" id="titlePosition" name="title_position" onchange="toggleCustomPosition('title')">
                                        <option value="bottom" {{ 'selected' if settings and settings.title_position == 'bottom' }}>底部</option>
                                        <option value="middle" {{ 'selected' if settings and settings.title_position == 'middle' }}>中间</option>
                                        <option value="top" {{ 'selected' if settings and settings.title_position == 'top' }}>顶部 (推荐)</option>
                                        <option value="custom" {{ 'selected' if settings and settings.title_position == 'custom' }}>自定义坐标</option>
                                    </select>
                                </div>
                                
                                <div id="titleCustomPosition" class="mb-3 row" style="display: {{ 'flex' if settings and settings.title_position == 'custom' else 'none' }}">
                                    <div class="col-md-6">
                                        <label for="titleX" class="form-label">X坐标</label>
                                        <input type="number" class="form-control" id="titleX" name="title_x" value="{{ settings.title_x if settings and settings.title_x is not none else (settings.video_width//2 if settings and settings.video_width is not none else 640) }}">
                                        <small class="text-muted">0为左边缘，{{ settings.video_width if settings and settings.video_width else 1280 }}为右边缘</small>
                                    </div>
                                    <div class="col-md-6">
                                        <label for="titleY" class="form-label">Y坐标</label>
                                        <input type="number" class="form-control" id="titleY" name="title_y" value="{{ settings.title_y if settings and settings.title_y is not none else 50 }}">
                                        <small class="text-muted">0为顶部边缘，{{ settings.video_height if settings and settings.video_height else 720 }}为底部边缘</small>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">保存字幕设置</button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <div class="card mb-4">
                                <div class="card-header">实时预览</div>
                                <div class="card-body">
                                    <div id="previewContainer" style="position: relative; background-color: #000; overflow: hidden; width: 100%; height: 300px;">
                                        {% if settings and settings.bg_video_thumbnail %}
                                        <!-- 背景视频第一帧 -->
                                        <img src="{{ url_for('static', filename=settings.bg_video_thumbnail) }}" 
                                             style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; z-index: 0;">
                                        {% endif %}
                                        
                                        <!-- 标题预览 -->
                                        <div id="titlePreview" style="position: absolute; width: 100%; text-align: center; color: {{ settings.title_color if settings else '#FFFFFF' }}; font-size: {{ settings.title_size if settings else 60 }}px; font-family: {{ settings.title_font if settings else 'SimHei' }}; top: {{ settings.title_y if settings and settings.title_position == 'custom' else '30' }}px; left: 0; z-index: 10;">
                                            {{ settings.title_text if settings else project.title }}
                                        </div>
                                        
                                        <!-- 字幕预览 -->
                                        <div id="subtitlePreview" style="position: absolute; width: 100%; text-align: center; color: {{ settings.subtitle_color if settings else '#FFFFFF' }}; font-size: {{ settings.subtitle_size if settings else 40 }}px; font-family: {{ settings.subtitle_font if settings else 'SimHei' }}; bottom: {{ '30px' if not settings or settings.subtitle_position == 'bottom' else 'auto' }}; top: {{ settings.subtitle_y if settings and settings.subtitle_position == 'custom' else 'auto' }}px; left: 0; z-index: 10;">
                                            示例字幕文本
                                        </div>
                                    </div>
                                    <div class="mt-2 text-center">
                                        <small class="text-muted">以上为效果预览，非实际视频</small>
                                    </div>
                                </div>
                            </div>

                            <div class="card">
                                <div class="card-header">文本替换规则</div>
                                <div class="card-body">
                                    <p class="text-muted">添加文本替换规则，用于修正错别字或特殊术语</p>
                                    <div class="table-responsive">
                                        <table class="table table-sm">
                                            <thead>
                                                <tr>
                                                    <th>原文本</th>
                                                    <th>替换为</th>
                                                    <th width="50px">操作</th>
                                                </tr>
                                            </thead>
                                            <tbody id="replacementRules">
                                                {% for rule in replacement_rules %}
                                                <tr>
                                                    <td>{{ rule.original_text }}</td>
                                                    <td>{{ rule.replacement_text }}</td>
                                                    <td>
                                                        <button class="btn btn-sm btn-outline-danger" onclick="deleteRule({{ rule.id }})">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                    <form id="addRuleForm" action="{{ url_for('add_text_replacement',project_id=project.id) }}" method="post" class="mt-3">
                                        <div class="row g-2">
                                            <div class="col">
                                                <input type="text" class="form-control form-control-sm" name="original_text" placeholder="原文本" required>
                                            </div>
                                            <div class="col">
                                                <input type="text" class="form-control form-control-sm" name="replacement_text" placeholder="替换为" required>
                                            </div>
                                            <div class="col-auto">
                                                <button type="submit" class="btn btn-sm btn-primary">添加</button>
                                            </div>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 背景音乐 -->
                <div class="tab-pane fade {{ 'show active' if active_tab == 'music' }}" id="music" role="tabpanel" aria-labelledby="music-tab">
                    <form id="musicForm" action="{{ url_for('update_video_music', project_id=project.id) }}" method="post" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="bgMusic" class="form-label">上传背景音乐</label>
                            <input type="file" class="form-control" id="bgMusic" name="bg_music" accept="audio/*">
                            {% if settings and settings.bg_music_path %}
                            <div class="mt-2">
                                <p>当前背景音乐: {{ settings.bg_music_path.split('/')[-1] }}</p>
                                <audio controls class="w-100">
                                    <source src="{{ url_for('static', filename='uploads/' + settings.bg_music_path.split('/')[-1]) }}" type="audio/mpeg">
                                    您的浏览器不支持音频元素
                                </audio>
                            </div>
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            <label for="bgMusicVolume" class="form-label">背景音乐音量 ({{ settings.bg_music_volume if settings else 0.5 }})</label>
                            <input type="range" class="form-range" id="bgMusicVolume" name="bg_music_volume" min="0" max="1" step="0.1"
                                  value="{{ settings.bg_music_volume if settings else 0.5 }}">
                        </div>
                        <button type="submit" class="btn btn-primary">保存音乐设置</button>
                    </form>
                </div>
                
                <!-- 社交媒体上传 -->
                <div class="tab-pane fade {{ 'show active' if active_tab == 'social_media' }}" id="social_media" role="tabpanel" aria-labelledby="social-media-tab">
                    <div class="row">
                        <div class="col-md-6">
                            <form id="socialMediaForm" action="{{ url_for('update_social_media', project_id=project.id) }}" method="post" enctype="multipart/form-data">
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">抖音上传设置</h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- 抖音登录按钮 -->
                                        <div class="d-flex align-items-center mb-3">
                                            <button type="button" id="douyinLoginBtn" class="btn btn-primary me-2">登录抖音</button>
                                            <span id="douyinLoginStatus" class="text-success">已登录</span>
                                        </div>
                                        
                                        <!-- 启用上传开关 -->
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="enableUpload" name="enable_upload" value="1"
                                                   {{ 'checked' if social_media_settings and social_media_settings.enable_upload == 1 }}>
                                            <label class="form-check-label" for="enableUpload">启用自动上传到抖音</label>
                                        </div>
                                        
                                        <!-- 视频标题 -->
                                        <div class="mb-3">
                                            <label for="videoTitle" class="form-label">视频标题</label>
                                            <input type="text" class="form-control" id="videoTitle" name="title" 
                                                   value="{{ social_media_settings.title if social_media_settings else project.title }}">
                                            <small class="text-muted">显示在抖音视频上方</small>
                                        </div>
                                        
                                        <!-- 视频描述 -->
                                        <div class="mb-3">
                                            <label for="videoDescription" class="form-label">视频描述</label>
                                            <textarea class="form-control" id="videoDescription" name="description" rows="4">{{ social_media_settings.description if social_media_settings else '' }}</textarea>
                                            <small class="text-muted">视频详细描述，用于给用户提供更多视频内容信息</small>
                                        </div>
                                        
                                        <!-- 视频标签 -->
                                        <div class="mb-3">
                                            <label for="videoTags" class="form-label">视频标签</label>
                                            <input type="text" class="form-control" id="videoTags" name="tags" 
                                                   value="{{ social_media_settings.tags if social_media_settings else '' }}">
                                            <small class="text-muted">以逗号分隔的标签列表，用于抖音推荐和搜索</small>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="card-title mb-0">封面设置</h5>
                                    </div>
                                    <div class="card-body">
                                        <!-- 自定义封面图片 -->
                                        <div class="mb-3">
                                            <label for="customCover" class="form-label">上传自定义封面图片</label>
                                            <input type="file" class="form-control" id="customCover" name="custom_cover" accept="image/*">
                                            {% if social_media_settings and social_media_settings.custom_cover_path %}
                                            <div class="mt-2">
                                                <p>当前封面图片:</p>
                                                <img src="{{ url_for('static', filename='uploads/' + social_media_settings.custom_cover_path.split('/')[-1]) }}" 
                                                     class="img-fluid" style="max-height: 200px;" alt="封面预览">
                                                <button type="button" class="btn btn-sm btn-outline-danger mt-2" 
                                                        onclick="if(confirm('确定要移除封面图片吗？')) document.getElementById('remove_cover').value = '1';">
                                                    <i class="bi bi-x-circle"></i> 移除封面
                                                </button>
                                            </div>
                                            {% endif %}
                                            <input type="hidden" id="remove_cover" name="remove_cover" value="0">
                                        </div>
                                        
                                        <!-- 封面标题设置 -->
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="titleOnCover" name="title_on_cover" value="1"
                                                       {{ 'checked' if not social_media_settings or social_media_settings.title_on_cover == 1 }}>
                                                <label class="form-check-label" for="titleOnCover">在封面上显示标题</label>
                                            </div>
                                        </div>
                                        
                                        <div id="coverTitleSettings" class="{{ 'd-none' if social_media_settings and social_media_settings.title_on_cover == 0 }}">
                                            <div class="mb-3">
                                                <label for="coverTitleFont" class="form-label">标题字体</label>
                                                <input type="text" class="form-control" id="coverTitleFont" name="title_font" 
                                                       value="{{ social_media_settings.title_font if social_media_settings else 'SimHei' }}">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="coverTitleSize" class="form-label">标题大小</label>
                                                <input type="number" class="form-control" id="coverTitleSize" name="title_size" 
                                                       value="{{ social_media_settings.title_size if social_media_settings else 40 }}">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="coverTitleColor" class="form-label">标题颜色</label>
                                                <input type="color" class="form-control" id="coverTitleColor" name="title_color" 
                                                       value="{{ social_media_settings.title_color if social_media_settings else '#FFFFFF' }}">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="coverTitlePosition" class="form-label">标题位置</label>
                                                <select class="form-select" id="coverTitlePosition" name="title_position" onchange="toggleCoverCustomPosition('title')">
                                                    <option value="top" {{ 'selected' if not social_media_settings or social_media_settings.title_position == 'top' }}>顶部</option>
                                                    <option value="middle" {{ 'selected' if social_media_settings and social_media_settings.title_position == 'middle' }}>中间</option>
                                                    <option value="bottom" {{ 'selected' if social_media_settings and social_media_settings.title_position == 'bottom' }}>底部</option>
                                                    <option value="custom" {{ 'selected' if social_media_settings and social_media_settings.title_position == 'custom' }}>自定义坐标</option>
                                                </select>
                                            </div>
                                            
                                            <div id="coverTitleCustomPosition" class="mb-3 row" style="display: {{ 'flex' if social_media_settings and social_media_settings.title_position == 'custom' else 'none' }}">
                                                <div class="col-md-6">
                                                    <label for="coverTitleX" class="form-label">X坐标</label>
                                                    <input type="number" class="form-control" id="coverTitleX" name="title_x" 
                                                           value="{{ social_media_settings.title_x if social_media_settings and social_media_settings.title_x is not none else (video_width//2) }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="coverTitleY" class="form-label">Y坐标</label>
                                                    <input type="number" class="form-control" id="coverTitleY" name="title_y" 
                                                           value="{{ social_media_settings.title_y if social_media_settings and social_media_settings.title_y is not none else 50 }}">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- 章节信息显示设置 -->
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="showChapterInfo" name="show_chapter_info" value="1"
                                                       {{ 'checked' if social_media_settings and social_media_settings.show_chapter_info == 1 }}>
                                                <label class="form-check-label" for="showChapterInfo">在封面上显示章节信息</label>
                                            </div>
                                        </div>
                                        
                                        <div id="chapterInfoSettings" class="{{ 'd-none' if not social_media_settings or social_media_settings.show_chapter_info == 0 }}">
                                            <div class="mb-3">
                                                <label for="chapterFont" class="form-label">章节字体</label>
                                                <input type="text" class="form-control" id="chapterFont" name="chapter_font" 
                                                       value="{{ social_media_settings.chapter_font if social_media_settings else 'SimHei' }}">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="chapterInfoText" class="form-label">章节信息文本</label>
                                                <input type="text" class="form-control" id="chapterInfoText" name="chapter_info_text" 
                                                       value="{{ social_media_settings.chapter_info_text if social_media_settings else (project_chapters[0].title if project_chapters else '第一章') }}">
                                                <small class="text-muted">自定义在封面上显示的章节信息，留空将显示第一个章节标题</small>
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="chapterSize" class="form-label">章节大小</label>
                                                <input type="number" class="form-control" id="chapterSize" name="chapter_size" 
                                                       value="{{ social_media_settings.chapter_size if social_media_settings else 30 }}">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="chapterColor" class="form-label">章节颜色</label>
                                                <input type="color" class="form-control" id="chapterColor" name="chapter_color" 
                                                       value="{{ social_media_settings.chapter_color if social_media_settings else '#FFFFFF' }}">
                                            </div>
                                            
                                            <div class="mb-3">
                                                <label for="chapterPosition" class="form-label">章节位置</label>
                                                <select class="form-select" id="chapterPosition" name="chapter_position" onchange="toggleCoverCustomPosition('chapter')">
                                                    <option value="top" {{ 'selected' if social_media_settings and social_media_settings.chapter_position == 'top' }}>顶部</option>
                                                    <option value="middle" {{ 'selected' if social_media_settings and social_media_settings.chapter_position == 'middle' }}>中间</option>
                                                    <option value="bottom" {{ 'selected' if not social_media_settings or social_media_settings.chapter_position == 'bottom' }}>底部</option>
                                                    <option value="custom" {{ 'selected' if social_media_settings and social_media_settings.chapter_position == 'custom' }}>自定义坐标</option>
                                                </select>
                                            </div>
                                            
                                            <div id="chapterCustomPosition" class="mb-3 row" style="display: {{ 'flex' if social_media_settings and social_media_settings.chapter_position == 'custom' else 'none' }}">
                                                <div class="col-md-6">
                                                    <label for="chapterX" class="form-label">X坐标</label>
                                                    <input type="number" class="form-control" id="chapterX" name="chapter_x" 
                                                           value="{{ social_media_settings.chapter_x if social_media_settings and social_media_settings.chapter_x is not none else (video_width//2) }}">
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="chapterY" class="form-label">Y坐标</label>
                                                    <input type="number" class="form-control" id="chapterY" name="chapter_y" 
                                                           value="{{ social_media_settings.chapter_y if social_media_settings and social_media_settings.chapter_y is not none else (video_height - 50) }}">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <button type="submit" class="btn btn-primary">保存社交媒体设置</button>
                            </form>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">封面预览</h5>
                                </div>
                                <div class="card-body">
                                    <div id="coverPreviewContainer" style="position: relative; background-color: #000; overflow: hidden; width: {{ video_width }}px; height: {{ video_height }}px; max-width: 100%; transform-origin: top left;">
                                        <!-- 预览图将通过JavaScript动态加载 -->
                                        <div class="d-flex justify-content-center align-items-center h-100">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-2 text-center">
                                        <small class="text-muted">以上为封面效果预览，非实际视频</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">上传历史</h5>
                                </div>
                                <div class="card-body">
                                    {% if upload_history %}
                                    <div class="list-group">
                                        {% for upload in upload_history %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ upload.created_at }}</h6>
                                                <span class="badge bg-{{ 'success' if upload.status == 'completed' else 'warning' if upload.status == 'in_progress' else 'danger' }}">
                                                    {{ '完成' if upload.status == 'completed' else '处理中' if upload.status == 'in_progress' else '错误' }}
                                                </span>
                                            </div>
                                            {% if upload.status == 'in_progress' %}
                                            <div class="progress mt-2" style="height: 10px;">
                                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" 
                                                     style="width: {{ upload.progress * 100 }}%" id="upload-progress-bar-{{ upload.id }}"></div>
                                            </div>
                                            <p class="text-muted mb-0" id="upload-progress-{{ upload.id }}">{{ (upload.progress * 100)|round|int }}%</p>
                                            {% elif upload.status == 'completed' and upload.url %}
                                            <p class="mb-1">
                                                <a href="{{ upload.url }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                    <i class="bi bi-link-45deg me-1"></i>查看视频
                                                </a>
                                            </p>
                                            {% elif upload.status == 'error' %}
                                            <p class="text-danger mb-0">{{ upload.error_message }}</p>
                                            {% endif %}
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">没有上传历史记录</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 视频渲染 -->
                <div class="tab-pane fade {{ 'show active' if active_tab == 'render' }}" id="render" role="tabpanel" aria-labelledby="render-tab">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="card mb-3">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">渲染设置</h5>
                                </div>
                                <div class="card-body">
                                    <form id="renderSettingsForm" action="{{ url_for('update_render_settings', project_id=project.id) }}" method="post" enctype="multipart/form-data">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <label for="videoWidth" class="form-label">视频宽度</label>
                                                <input type="number" class="form-control" id="videoWidth" name="video_width" 
                                                      value="{{ settings.video_width if settings else 1280 }}">
                                            </div>
                                            <div class="col-md-6">
                                                <label for="videoHeight" class="form-label">视频高度</label>
                                                <input type="number" class="form-control" id="videoHeight" name="video_height"
                                                      value="{{ settings.video_height if settings else 720 }}">
                                            </div>
                                        </div>
                                        
                                        <!-- 背景视频选择 -->
                                        <div class="mb-3">
                                            <label class="form-label">背景视频</label>
                                            <div class="card">
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                                            <label for="bg_video_select" class="form-label mb-0">选择背景视频</label>
                                                            <a href="{{ url_for('video_upload') }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                                <i class="bi bi-plus-circle"></i> 管理视频
                                                            </a>
                                                        </div>
                                                        <select class="form-select" id="bg_video_select" name="bg_video_id">
                                                            <option value="">选择已上传的视频...</option>
                                                            {% for video in uploaded_videos %}
                                                            <option value="{{ video.id }}" 
                                                                    data-path="{{ video.file_path }}"
                                                                    data-thumbnail="{{ video.thumbnail_path }}"
                                                                    {{ 'selected' if settings and settings.bg_video_path == video.file_path }}>
                                                                {{ video.name }} 
                                                                {% if video.duration %}({{ "%.1f"|format(video.duration) }}秒){% endif %}
                                                                {% if video.width and video.height %}[{{ video.width }}x{{ video.height }}]{% endif %}
                                                            </option>
                                                            {% endfor %}
                                                        </select>
                                                        <small class="text-muted">从已上传的视频中选择，或点击"管理视频"上传新视频</small>
                                                    </div>
                                                    
                                                    <!-- <div class="mb-3">
                                                        <label for="bg_video_file" class="form-label">或上传新视频</label>
                                                        <input type="file" class="form-control" id="bg_video_file" name="bg_video" accept="video/mp4,video/webm,video/avi,image/jpeg,image/png,image/gif,image/webp">
                                                        <small class="text-muted">支持 MP4, WebM, AVI 视频格式以及 JPG, PNG, GIF, WebP 图片格式</small>
                                                    </div> -->
                                                    
                                                    {% if settings and settings.bg_video_path %}
                                                        <div class="mb-3">
                                                            <label class="form-label">当前背景视频</label>
                                                            <div class="d-flex align-items-center">
                                                                <span class="me-2">{{ settings.bg_video_path.split('/')[-1] }}</span>
                                                                <button type="button" class="btn btn-sm btn-outline-danger" 
                                                                    onclick="if(confirm('确定要移除背景视频吗？')) document.getElementById('remove_bg_video').value = '1';">
                                                                    <i class="bi bi-x-circle"></i> 移除
                                                                </button>
                                                            </div>
                                                            {% if settings.bg_video_thumbnail %}
                                                            <div class="mt-2">
                                                                <img src="{{ url_for('static', filename='uploads/' + settings.bg_video_thumbnail.split('/')[-1]) }}" 
                                                                     class="img-thumbnail" style="max-height: 100px;" alt="背景视频预览">
                                                            </div>
                                                            {% endif %}
                                                        </div>
                                                    {% else %}
                                                        <div class="alert alert-info">
                                                            <i class="bi bi-info-circle me-2"></i>未设置背景视频，将使用纯色背景
                                                        </div>
                                                    {% endif %}
                                                    <input type="hidden" id="remove_bg_video" name="remove_bg_video" value="0">
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="form-check form-switch mb-3">
                                            <input class="form-check-input" type="checkbox" id="useGpu" name="use_gpu" value="1"
                                                   {{ 'checked' if not settings or settings.use_gpu == 1 }}>
                                            <label class="form-check-label" for="useGpu">使用GPU加速 (自动检测AMD/NVIDIA)</label>
                                        </div>
                                        
                                        <div class="mb-3">
                                            <label for="subtitleApi" class="form-label">字幕生成API</label>
                                            <select class="form-select" id="subtitleApi" name="subtitle_api">
                                                <option value="jianying" {{ 'selected' if not settings or settings.subtitle_api == 'jianying' }}>剪映API</option>
                                                <option value="bcut" {{ 'selected' if settings and settings.subtitle_api == 'bcut' }}>必剪API (B站)</option>
                                                <option value="local" {{ 'selected' if settings and settings.subtitle_api == 'local' }}>本地API (本地部署)</option>
                                            </select>
                                            <small class="text-muted">选择用于生成字幕的语音识别API</small>
                                        </div>
                                        
                                        <button type="submit" class="btn btn-primary">保存渲染设置</button>
                                    </form>
                                </div>
                            </div>

                        </div>
                        
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title mb-0">渲染历史</h5>
                                </div>
                                <div class="card-body">
                                    {% if render_history %}
                                    <div class="list-group">
                                        {% for render in render_history %}
                                        <div class="list-group-item">
                                            <div class="d-flex w-100 justify-content-between">
                                                <h6 class="mb-1">{{ render.created_at }}</h6>
                                                <span class="badge bg-{{ 'success' if render.status == 'completed' else 'warning' if render.status == 'in_progress' else 'danger' }}">
                                                    {{ '完成' if render.status == 'completed' else '处理中' if render.status == 'in_progress' else '错误' }}
                                                </span>
                                            </div>
                                            {% if render.status == 'in_progress' %}
                                            <div class="progress mt-2" style="height: 10px;">
                                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" 
                                                     style="width: {{ render.progress * 100 }}%" id="progress-bar-{{ render.id }}"></div>
                                            </div>
                                            <p class="text-muted mb-0" id="progress-{{ render.id }}">{{ (render.progress * 100)|round|int }}%</p>
                                            {% elif render.status == 'completed' and render.output_path %}
                                            <p class="mb-1">
                                                <a href="{{ url_for('download_video', render_id=render.id) }}" class="btn btn-sm btn-outline-primary">
                                                    <i class="bi bi-download me-1"></i>下载视频
                                                </a>
                                            </p>
                                            {% elif render.status == 'error' %}
                                            <p class="text-danger mb-0">{{ render.error_message }}</p>
                                            {% endif %}
                                        </div>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <p class="text-muted">没有渲染历史记录</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 章节全选功能
    document.addEventListener('DOMContentLoaded', function() {
        const selectAllCheckbox = document.getElementById('selectAllChapters');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const checkboxes = document.querySelectorAll('.chapter-checkbox');
                checkboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
            });
        }
        
        // 初始化位置设置控件
        initializePositionSettings();
        
        // 初始化预览事件
        initializePreviewEvents();
        
        // 初始化预览大小
        updatePreviewSize();
        
        // 初始化背景视频选择监听器
        initializeBackgroundVideoListener();
        
        // 如果当前是渲染标签页且有正在进行的渲染任务，则轮询进度
        {% if active_tab == 'render' and render_history and render_history[0].status == 'in_progress' %}
        pollRenderProgress({{ render_history[0].id }});
        {% endif %}
        
        // 抖音登录按钮事件处理
        const douyinLoginBtn = document.getElementById('douyinLoginBtn');
        const douyinLoginStatus = document.getElementById('douyinLoginStatus');
        
        if (douyinLoginBtn) {
            douyinLoginBtn.addEventListener('click', function() {
                // 禁用按钮，防止重复点击
                douyinLoginBtn.disabled = true;
                douyinLoginBtn.innerText = '登录中...';
                
                // 获取当前项目ID
                const projectId = {{ project.id|tojson }};
                fetch('/douyin_login/' + projectId, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    // 根据返回结果更新状态
                    if (data.success) {
                        douyinLoginStatus.textContent = '已登录';
                        douyinLoginStatus.className = 'text-success';
                    } else {
                        douyinLoginStatus.textContent = '未登录';
                        douyinLoginStatus.className = 'text-danger';
                    }
                })
                .catch(error => {
                    console.error('登录抖音出错:', error);
                    douyinLoginStatus.textContent = '登录失败';
                    douyinLoginStatus.className = 'text-danger';
                })
                .finally(() => {
                    // 恢复按钮状态
                    douyinLoginBtn.disabled = false;
                    douyinLoginBtn.innerText = '登录抖音';
                });
            });
        }
        
        // 初始化封面预览，使用后端生成
        generateCoverPreview();
        
        // 社交媒体tab页切换事件监听
        const socialMediaTab = document.getElementById('social-media-tab');
        if (socialMediaTab) {
            socialMediaTab.addEventListener('shown.bs.tab', function() {
                // 当切换到社交媒体标签页时，生成封面预览
                generateCoverPreview();
            });
        }
    });
    
    // 初始化位置设置控件
    function initializePositionSettings() {
        // 检查当前字幕位置设置
        const subtitlePosition = document.getElementById('subtitlePosition');
        if (subtitlePosition) {
            toggleCustomPosition('subtitle');
        }
        
        // 检查当前标题位置设置
        const titlePosition = document.getElementById('titlePosition');
        if (titlePosition) {
            toggleCustomPosition('title');
        }
    }
    
    // 根据选择显示或隐藏自定义坐标输入框
    function toggleCustomPosition(type) {
        const positionSelect = document.getElementById(`${type}Position`);
        const customPositionDiv = document.getElementById(`${type}CustomPosition`);
        
        if (positionSelect && customPositionDiv) {
            if (positionSelect.value === 'custom') {
                customPositionDiv.style.display = 'flex';
            } else {
                customPositionDiv.style.display = 'none';
            }
        }
    }
    
    // 更新预览尺寸函数
    function updatePreviewSize() {
        const videoWidth = document.getElementById('videoWidth');
        const videoHeight = document.getElementById('videoHeight');
        const previewContainer = document.getElementById('previewContainer');
        
        if (videoWidth && videoHeight && previewContainer) {
            const width = parseInt(videoWidth.value) || 1280;
            const height = parseInt(videoHeight.value) || 720;
            
            // 设置容器尺寸
            previewContainer.style.width = width + 'px';
            previewContainer.style.height = height + 'px';
            
            // 计算缩放比例以适应最大高度400px
            const scale = height > 400 ? 400 / height : 1.0;
            previewContainer.style.transform = `scale(${scale})`;
            previewContainer.style.transformOrigin = 'top left';
            previewContainer.style.maxWidth = '100%';
        }
    }
    
    // 更新标题预览
    function updateTitlePreview() {
        const titlePreview = document.getElementById('titlePreview');
        const titleText = document.getElementById('titleText');
        const titleColor = document.getElementById('titleColor');
        const titleSize = document.getElementById('titleSize');
        const titleFont = document.getElementById('titleFont');
        const titlePosition = document.getElementById('titlePosition');
        const titleX = document.getElementById('titleX');
        const titleY = document.getElementById('titleY');
        const previewContainer = document.getElementById('previewContainer');
        
        if (titlePreview) {
            // 更新标题文本
            if (titleText) titlePreview.textContent = titleText.value;
            
            // 更新标题格式
            if (titleColor) titlePreview.style.color = titleColor.value;
            if (titleSize) titlePreview.style.fontSize = `${titleSize.value}px`;
            if (titleFont) titlePreview.style.fontFamily = titleFont.value;
            
            // 重置标题位置
            titlePreview.style.top = 'auto';
            titlePreview.style.bottom = 'auto';
            titlePreview.style.left = '0';
            titlePreview.style.textAlign = 'center';
            
            // 根据选择更新标题位置
            if (titlePosition) {
                const position = titlePosition.value;
                
                if (position === 'custom' && titleX && titleY) {
                    // 固定位置
                    titlePreview.style.left = `${titleX.value}px`;
                    titlePreview.style.top = `${titleY.value}px`;
                    titlePreview.style.textAlign = 'left';
                    titlePreview.style.width = 'auto';
                } else if (position === 'top') {
                    titlePreview.style.top = '30px';
                } else if (position === 'middle') {
                    titlePreview.style.top = '50%';
                    titlePreview.style.transform = 'translateY(-50%)';
                } else if (position === 'bottom') {
                    titlePreview.style.bottom = '30px';
                }
            }
        }
    }
    
    // 更新字幕预览
    function updateSubtitlePreview() {
        const subtitlePreview = document.getElementById('subtitlePreview');
        const subtitleFont = document.getElementById('subtitleFont');
        const subtitleSize = document.getElementById('subtitleSize');
        const subtitleColor = document.getElementById('subtitleColor');
        const subtitlePosition = document.getElementById('subtitlePosition');
        const subtitleX = document.getElementById('subtitleX');
        const subtitleY = document.getElementById('subtitleY');
        
        if (subtitlePreview) {
            // 更新字幕格式
            if (subtitleFont) subtitlePreview.style.fontFamily = subtitleFont.value;
            if (subtitleSize) subtitlePreview.style.fontSize = `${subtitleSize.value}px`;
            if (subtitleColor) subtitlePreview.style.color = subtitleColor.value;
            
            // 重置字幕位置
            subtitlePreview.style.top = 'auto';
            subtitlePreview.style.bottom = 'auto';
            subtitlePreview.style.left = '0';
            subtitlePreview.style.textAlign = 'center';
            subtitlePreview.style.transform = 'none';
            
            // 根据选择更新字幕位置
            if (subtitlePosition) {
                const position = subtitlePosition.value;
                
                if (position === 'custom' && subtitleX && subtitleY) {
                    // 固定位置
                    subtitlePreview.style.left = `${subtitleX.value}px`;
                    subtitlePreview.style.top = `${subtitleY.value}px`;
                    subtitlePreview.style.textAlign = 'left';
                    subtitlePreview.style.width = 'auto';
                } else if (position === 'top') {
                    subtitlePreview.style.top = '70px';
                } else if (position === 'middle') {
                    subtitlePreview.style.top = '50%';
                    subtitlePreview.style.transform = 'translateY(-50%)';
                } else if (position === 'bottom') {
                    subtitlePreview.style.bottom = '30px';
                }
            }
        }
    }
    
    // 在initializePreviewEvents函数末尾添加视频尺寸控件的事件监听
    function initializePreviewEvents() {
        // 标题相关控件
        const titleText = document.getElementById('titleText');
        const titleColor = document.getElementById('titleColor');
        const titleSize = document.getElementById('titleSize');
        const titleFont = document.getElementById('titleFont');
        const titlePosition = document.getElementById('titlePosition');
        const titleX = document.getElementById('titleX');
        const titleY = document.getElementById('titleY');
        
        // 字幕相关控件
        const subtitleFont = document.getElementById('subtitleFont');
        const subtitleSize = document.getElementById('subtitleSize');
        const subtitleColor = document.getElementById('subtitleColor');
        const subtitlePosition = document.getElementById('subtitlePosition');
        const subtitleX = document.getElementById('subtitleX');
        const subtitleY = document.getElementById('subtitleY');
        
        // 视频尺寸控件
        const videoWidth = document.getElementById('videoWidth');
        const videoHeight = document.getElementById('videoHeight');
        
        // 预览元素
        const titlePreview = document.getElementById('titlePreview');
        const subtitlePreview = document.getElementById('subtitlePreview');
        
        // 为标题相关控件添加事件监听
        if (titleText) titleText.addEventListener('input', updateTitlePreview);
        if (titleColor) titleColor.addEventListener('input', updateTitlePreview);
        if (titleSize) titleSize.addEventListener('input', updateTitlePreview);
        if (titleFont) titleFont.addEventListener('input', updateTitlePreview);
        if (titlePosition) titlePosition.addEventListener('change', updateTitlePreview);
        if (titleX) titleX.addEventListener('input', updateTitlePreview);
        if (titleY) titleY.addEventListener('input', updateTitlePreview);
        
        // 为字幕相关控件添加事件监听
        if (subtitleFont) subtitleFont.addEventListener('input', updateSubtitlePreview);
        if (subtitleSize) subtitleSize.addEventListener('input', updateSubtitlePreview);
        if (subtitleColor) subtitleColor.addEventListener('input', updateSubtitlePreview);
        if (subtitlePosition) subtitlePosition.addEventListener('change', updateSubtitlePreview);
        if (subtitleX) subtitleX.addEventListener('input', updateSubtitlePreview);
        if (subtitleY) subtitleY.addEventListener('input', updateSubtitlePreview);
        
        // 为视频尺寸控件添加事件监听
        if (videoWidth) videoWidth.addEventListener('input', updatePreviewSize);
        if (videoHeight) videoHeight.addEventListener('input', updatePreviewSize);
    }
    
    function startRender() {
        if(confirm('确定要开始渲染视频吗？这可能需要一些时间。')) {
            document.getElementById('startRenderBtn').disabled = true;
            
            fetch(`/render_video/{{ project.id }}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'started') {
                    // 轮询渲染进度
                    pollRenderProgress(data.render_id);
                } else {
                    alert('启动渲染失败: ' + data.message);
                    document.getElementById('startRenderBtn').disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发生错误，请重试');
                document.getElementById('startRenderBtn').disabled = false;
            });
        }
    }
    
    function pollRenderProgress(renderId) {
        const progressInterval = setInterval(() => {
            fetch(`/render_progress/${renderId}`)
            .then(response => response.json())
            .then(data => {
                const progressElement = document.getElementById(`progress-${renderId}`);
                const progressBarElement = document.querySelector(`#progress-bar-${renderId}`);
                
                if (progressElement) {
                    const progressPercent = Math.round(data.progress * 100);
                    progressElement.textContent = `${progressPercent}%`;
                    
                    // 同时更新进度条
                    if (progressBarElement) {
                        progressBarElement.style.width = `${progressPercent}%`;
                    }
                }
                
                if (data.status !== 'in_progress') {
                    clearInterval(progressInterval);
                    // 如果完成或出错，重新加载页面显示最新状态
                    window.location.reload();
                }
            })
            .catch(error => {
                console.error('Error checking progress:', error);
            });
        }, 1000);
    }
    
    function deleteRule(ruleId) {
        if(confirm('确定要删除这条替换规则吗？')) {
            fetch(`/delete_text_replacement/${ruleId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    window.location.reload();
                } else {
                    alert('删除失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发生错误，请重试');
            });
        }
    }
    
    // 社交媒体上传 - 封面自定义位置切换
    function toggleCoverCustomPosition(type) {
        const positionSelect = document.getElementById(`${type}Position`);
        const customPositionDiv = document.getElementById(`${type}CustomPosition`);
        
        if (positionSelect && customPositionDiv) {
            if (positionSelect.value === 'custom') {
                customPositionDiv.style.display = 'flex';
            } else {
                customPositionDiv.style.display = 'none';
            }
        }
        
        // 当位置设置改变时，更新预览
        generateCoverPreview();
    }
    
    // 社交媒体上传 - 显示/隐藏标题和章节设置
    document.addEventListener('DOMContentLoaded', function() {
        // 获取元素
        const titleOnCover = document.getElementById('titleOnCover');
        const coverTitleSettings = document.getElementById('coverTitleSettings');
        const showChapterInfo = document.getElementById('showChapterInfo');
        const chapterInfoSettings = document.getElementById('chapterInfoSettings');
        
        // 添加标题显示/隐藏事件监听
        if (titleOnCover && coverTitleSettings) {
            titleOnCover.addEventListener('change', function() {
                coverTitleSettings.classList.toggle('d-none', !this.checked);
                generateCoverPreview(); // 更新预览
            });
        }
        
        // 添加章节信息显示/隐藏事件监听
        if (showChapterInfo && chapterInfoSettings) {
            showChapterInfo.addEventListener('change', function() {
                chapterInfoSettings.classList.toggle('d-none', !this.checked);
                generateCoverPreview(); // 更新预览
            });
        }
        
        // 初始化社交媒体上传封面预览事件
        initializeCoverPreview();
        
        // 初始化社交媒体上传自定义位置设置
        initializeCoverPositionSettings();
    });
    
    // 使用后端API生成封面预览图
    function generateCoverPreview() {
        // 检查是否在社交媒体标签页
        if (!document.getElementById('coverPreviewContainer')) {
            return;
        }
        
        // 获取所有表单元素值
        const formData = new FormData();
        formData.append('project_id', '{{ project.id }}');
        
        // 获取标题设置
        const videoTitle = document.getElementById('videoTitle');
        if (videoTitle) formData.append('title', videoTitle.value);
        
        const titleOnCover = document.getElementById('titleOnCover');
        if (titleOnCover) formData.append('title_on_cover', titleOnCover.checked ? '1' : '0');
        
        const coverTitleFont = document.getElementById('coverTitleFont');
        if (coverTitleFont) formData.append('title_font', coverTitleFont.value);
        
        const coverTitleSize = document.getElementById('coverTitleSize');
        if (coverTitleSize) formData.append('title_size', coverTitleSize.value);
        
        const coverTitleColor = document.getElementById('coverTitleColor');
        if (coverTitleColor) formData.append('title_color', coverTitleColor.value);
        
        const coverTitlePosition = document.getElementById('coverTitlePosition');
        if (coverTitlePosition) formData.append('title_position', coverTitlePosition.value);
        
        const coverTitleX = document.getElementById('coverTitleX');
        if (coverTitleX) formData.append('title_x', coverTitleX.value);
        
        const coverTitleY = document.getElementById('coverTitleY');
        if (coverTitleY) formData.append('title_y', coverTitleY.value);
        
        // 获取章节设置
        const showChapterInfo = document.getElementById('showChapterInfo');
        if (showChapterInfo) formData.append('show_chapter_info', showChapterInfo.checked ? '1' : '0');
        
        const chapterInfoText = document.getElementById('chapterInfoText');
        if (chapterInfoText) formData.append('chapter_info_text', chapterInfoText.value);
        
        const chapterFont = document.getElementById('chapterFont');
        if (chapterFont) formData.append('chapter_font', chapterFont.value);
        
        const chapterSize = document.getElementById('chapterSize');
        if (chapterSize) formData.append('chapter_size', chapterSize.value);
        
        const chapterColor = document.getElementById('chapterColor');
        if (chapterColor) formData.append('chapter_color', chapterColor.value);
        
        const chapterPosition = document.getElementById('chapterPosition');
        if (chapterPosition) formData.append('chapter_position', chapterPosition.value);
        
        const chapterX = document.getElementById('chapterX');
        if (chapterX) formData.append('chapter_x', chapterX.value);
        
        const chapterY = document.getElementById('chapterY');
        if (chapterY) formData.append('chapter_y', chapterY.value);
        
        // 添加自定义封面路径（如果有）
        {% if social_media_settings and social_media_settings.custom_cover_path %}
        formData.append('existing_cover_path', '{{ social_media_settings.custom_cover_path }}');
        {% endif %}
        
        // 检查自定义封面文件
        const customCover = document.getElementById('customCover');
        if (customCover && customCover.files.length > 0) {
            formData.append('custom_cover', customCover.files[0]);
        }
        
        // 显示加载状态
        const previewContainer = document.getElementById('coverPreviewContainer');
        if (previewContainer) {
            previewContainer.innerHTML = '<div class="d-flex justify-content-center align-items-center" style="height: 100%;"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
        }
        
        // 发送到后端生成预览
        fetch('/generate_cover_preview', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.preview_url) {
                // 更新预览图
                const previewContainer = document.getElementById('coverPreviewContainer');
                if (previewContainer) {
                    // 创建新图像并设置加载事件
                    const img = new Image();
                    img.onload = function() {
                        // 清空容器
                        previewContainer.innerHTML = '';
                        
                        // 添加图像并设置样式
                        img.style.width = '100%';
                        img.style.height = '100%';
                        img.style.objectFit = 'contain';
                        previewContainer.appendChild(img);
                        
                        // 调整预览容器大小
                        const scale = previewContainer.offsetHeight > 400 ? 400 / previewContainer.offsetHeight : 1.0;
                        previewContainer.style.transform = `scale(${scale})`;
                    };
                    
                    // 添加时间戳避免缓存
                    img.src = data.preview_url + '?t=' + new Date().getTime();
                }
            } else {
                console.error('Failed to generate preview:', data.error);
            }
        })
        .catch(error => {
            console.error('Error generating preview:', error);
        });
    }
    
    // 初始化封面预览
    function initializeCoverPreview() {
        // 标题相关控件
        const videoTitle = document.getElementById('videoTitle');
        const coverTitleFont = document.getElementById('coverTitleFont');
        const coverTitleSize = document.getElementById('coverTitleSize');
        const coverTitleColor = document.getElementById('coverTitleColor');
        const coverTitlePosition = document.getElementById('coverTitlePosition');
        const coverTitleX = document.getElementById('coverTitleX');
        const coverTitleY = document.getElementById('coverTitleY');
        
        // 章节相关控件
        const chapterInfoText = document.getElementById('chapterInfoText');
        const chapterFont = document.getElementById('chapterFont');
        const chapterSize = document.getElementById('chapterSize');
        const chapterColor = document.getElementById('chapterColor');
        const chapterPosition = document.getElementById('chapterPosition');
        const chapterX = document.getElementById('chapterX');
        const chapterY = document.getElementById('chapterY');
        
        // 为标题相关控件添加事件监听
        if (videoTitle) videoTitle.addEventListener('input', generateCoverPreview);
        if (coverTitleFont) coverTitleFont.addEventListener('input', generateCoverPreview);
        if (coverTitleSize) coverTitleSize.addEventListener('input', generateCoverPreview);
        if (coverTitleColor) coverTitleColor.addEventListener('input', generateCoverPreview);
        if (coverTitlePosition) coverTitlePosition.addEventListener('change', generateCoverPreview);
        if (coverTitleX) coverTitleX.addEventListener('input', generateCoverPreview);
        if (coverTitleY) coverTitleY.addEventListener('input', generateCoverPreview);
        
        // 为章节样式相关控件添加事件监听
        if (chapterInfoText) chapterInfoText.addEventListener('input', generateCoverPreview);
        if (chapterFont) chapterFont.addEventListener('input', generateCoverPreview);
        if (chapterSize) chapterSize.addEventListener('input', generateCoverPreview);
        if (chapterColor) chapterColor.addEventListener('input', generateCoverPreview);
        if (chapterPosition) chapterPosition.addEventListener('change', generateCoverPreview);
        if (chapterX) chapterX.addEventListener('input', generateCoverPreview);
        if (chapterY) chapterY.addEventListener('input', generateCoverPreview);
        
        // 添加封面上传文件的监听
        const customCover = document.getElementById('customCover');
        if (customCover) {
            customCover.addEventListener('change', function() {
                generateCoverPreview();
            });
        }
    }
    
    // 初始化社交媒体上传自定义位置设置
    function initializeCoverPositionSettings() {
        // 初始化标题位置
        const coverTitlePosition = document.getElementById('coverTitlePosition');
        if (coverTitlePosition) {
            toggleCoverCustomPosition('title');
        }
        
        // 初始化章节位置
        const chapterPosition = document.getElementById('chapterPosition');
        if (chapterPosition) {
            toggleCoverCustomPosition('chapter');
        }
    }
    
    // 处理背景视频选择变化
    function updatePreviewBackground() {
        const bgVideoSelect = document.getElementById('bg_video_select');
        const previewContainer = document.getElementById('previewContainer');
        
        if (!bgVideoSelect || !previewContainer) return;
        
        const selectedOption = bgVideoSelect.options[bgVideoSelect.selectedIndex];
        
        // 移除现有的背景图片
        const existingBg = previewContainer.querySelector('.preview-background');
        if (existingBg) {
            existingBg.remove();
        }
        
        if (selectedOption.value && selectedOption.dataset.thumbnail) {
            // 添加新的背景图片
            const bgImg = document.createElement('img');
            bgImg.className = 'preview-background';
            // 使用正确的路径访问上传的视频缩略图
            bgImg.src = `/uploaded_videos/${selectedOption.dataset.thumbnail.split('/').pop()}`;
            bgImg.style.cssText = 'position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; z-index: 0;';
            
            // 插入到容器的开头
            previewContainer.insertBefore(bgImg, previewContainer.firstChild);
        }
    }
    
    // 初始化背景视频选择监听器
    function initializeBackgroundVideoListener() {
        const bgVideoSelect = document.getElementById('bg_video_select');
        if (bgVideoSelect) {
            bgVideoSelect.addEventListener('change', function() {
                updatePreviewBackground();
                // 同时更新字幕设置标签页的预览
                updateSubtitlePreviewBackground();
            });
            // 初始化时也更新一次
            updatePreviewBackground();
            updateSubtitlePreviewBackground();
        }
    }
    
    // 更新字幕设置标签页的预览背景
    function updateSubtitlePreviewBackground() {
        const bgVideoSelect = document.getElementById('bg_video_select');
        const previewContainer = document.getElementById('previewContainer');
        
        if (!bgVideoSelect || !previewContainer) return;
        
        const selectedOption = bgVideoSelect.options[bgVideoSelect.selectedIndex];
        
        // 移除现有的背景图片
        const existingBg = previewContainer.querySelector('.subtitle-preview-background');
        if (existingBg) {
            existingBg.remove();
        }
        
        if (selectedOption.value && selectedOption.dataset.thumbnail) {
            // 添加新的背景图片
            const bgImg = document.createElement('img');
            bgImg.className = 'subtitle-preview-background';
            // 使用正确的路径访问上传的视频缩略图
            bgImg.src = `/uploaded_videos/${selectedOption.dataset.thumbnail.split('/').pop()}`;
            bgImg.style.cssText = 'position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: cover; z-index: 0;';
            
            // 插入到容器的开头
            previewContainer.insertBefore(bgImg, previewContainer.firstChild);
        }
    }
</script>
{% endblock %} 