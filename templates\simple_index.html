<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小说云 - 多源小说搜索与下载系统</title>
    <!-- 使用本地资源文件 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/css/bootstrap-icons.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        /* 内联关键CSS，确保基本样式即使其他CSS加载失败也能显示 */
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background-color: #f5f7fa;
            padding-bottom: 70px;
        }
        .navbar {
            background: linear-gradient(135deg, #4e54c8, #8f94fb);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 0.8rem;
        }
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }
        .welcome-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
        }
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s;
            overflow: hidden;
            margin-bottom: 20px;
        }
        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transform: translateY(-5px);
        }
        .novel-cover-container {
            height: 220px;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #f0f0f0;
        }
        .no-cover {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100%;
            background: linear-gradient(120deg, #e0e0e0, #f5f5f5);
            color: #999;
            font-size: 3rem;
        }
        .btn {
            border-radius: 6px;
            padding: 0.5rem 1.2rem;
            font-weight: 500;
            transition: all 0.3s;
        }
        .btn-light {
            background-color: rgba(255, 255, 255, 0.9);
            border: none;
        }
        .btn-outline-primary {
            border-color: #4e54c8;
            color: #4e54c8;
        }
        .btn-outline-primary:hover {
            background-color: #4e54c8;
            color: white;
        }
        .text-truncate {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        footer {
            background-color: #f8f9fa;
            padding: 1rem 0;
            margin-top: 3rem;
            text-align: center;
        }
        /* 字体图标备用样式 */
        .icon-placeholder {
            display: inline-block;
            width: 1em;
            height: 1em;
            vertical-align: -0.125em;
            margin-right: 0.25em;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="/">
                <span class="icon-placeholder">📚</span>小说云
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/search">搜索</a>
                    </li>
                </ul>
                <form class="d-flex search-form" action="/search" method="post">
                    <input class="form-control" type="search" name="keyword" placeholder="输入小说名或作者" aria-label="Search">
                    <button class="btn btn-light" type="submit">搜索</button>
                </form>
            </div>
        </div>
    </nav>

    <div class="container mt-4 mb-5">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm welcome-card">
                    <div class="card-body">
                        <h1 class="display-5 mb-3">欢迎使用小说云</h1>
                        <p class="lead">小说云是一个多源小说搜索与下载系统，支持番茄小说和七猫小说。</p>
                        <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                            <a href="/search" class="btn btn-light btn-lg px-4 me-md-2">
                                <span class="icon-placeholder">🔍</span>搜索小说
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <h2 class="mb-4"><span class="icon-placeholder">📚</span>已下载的小说</h2>

        <div class="alert alert-info">
            <span class="icon-placeholder">ℹ️</span>还没有下载过小说，请先 <a href="/search" class="alert-link">搜索小说</a> 并下载
        </div>
        
        <!-- 示例小说卡片 -->
        <div class="row row-cols-1 row-cols-md-3 row-cols-lg-4 g-4">
            <div class="col">
                <div class="card h-100 shadow-sm novel-card">
                    <div class="novel-cover-container">
                        <div class="no-cover">
                            <span class="icon-placeholder">📖</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title text-truncate">示例小说标题</h5>
                        <p class="card-text text-muted mb-1">作者: 示例作者</p>
                        <p class="card-text text-muted mb-2">
                            <span class="badge bg-success">
                                番茄小说
                            </span>
                        </p>
                        <a href="#" class="btn btn-outline-primary stretched-link">阅读</a>
                    </div>
                </div>
            </div>
            <div class="col">
                <div class="card h-100 shadow-sm novel-card">
                    <div class="novel-cover-container">
                        <div class="no-cover">
                            <span class="icon-placeholder">📖</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title text-truncate">另一个示例小说标题</h5>
                        <p class="card-text text-muted mb-1">作者: 示例作者二</p>
                        <p class="card-text text-muted mb-2">
                            <span class="badge bg-info">
                                七猫小说
                            </span>
                        </p>
                        <a href="#" class="btn btn-outline-primary stretched-link">阅读</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <footer class="mt-auto py-3 bg-light">
        <div class="container text-center">
           <span class="text-muted">小说云 &copy; 2025 - 2026</span>
        </div>
    </footer>

    <!-- 使用本地JavaScript文件 -->
    <script src="{{ url_for('static', filename='vendor/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='vendor/js/jquery.min.js') }}"></script>
    <script>
        // 内联基本JavaScript功能，以便在外部JS加载失败时仍能工作
        document.addEventListener('DOMContentLoaded', function() {
            // 添加导航栏活动状态
            const navLinks = document.querySelectorAll('.nav-link');
            const currentLocation = window.location.pathname;
            
            navLinks.forEach(link => {
                if ((currentLocation === '/' && link.getAttribute('href') === '/') || 
                    (currentLocation !== '/' && link.getAttribute('href') !== '/' && currentLocation.includes(link.getAttribute('href')))) {
                    link.classList.add('active');
                } else {
                    link.classList.remove('active');
                }
            });
            
            // 卡片悬停效果
            const cards = document.querySelectorAll('.card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 8px 25px rgba(0, 0, 0, 0.1)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = '';
                    this.style.boxShadow = '';
                });
            });
        });
    </script>
</body>
</html> 