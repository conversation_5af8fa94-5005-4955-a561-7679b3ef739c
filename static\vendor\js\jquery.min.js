/*! jQuery v3.6.0 | (c) OpenJS Foundation and other contributors | jquery.org/license */
!function(e,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=e.document?t(e,!0):function(e){if(!e.document)throw new Error("jQuery requires a window with a document");return t(e)}:t(e)}("undefined"!=typeof window?window:this,function(e,t){"use strict";var n=function(e,t){return new n.fn.init(e,t)};n.fn=n.prototype={jquery:"3.6.0",constructor:n,length:0},n.fn.init=function(e,t){return this},n.fn.init.prototype=n.fn,e.jQuery=e.$=n;}); 