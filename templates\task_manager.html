{% extends "base.html" %}

{% block title %}任务管理 - 小说云{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/novel_to_video.css') }}">
<style>
    .task-card {
        transition: all 0.3s ease;
        margin-bottom: 1rem;
    }
    
    .task-card:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .task-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .progress {
        height: 8px;
        border-radius: 4px;
    }
    
    .priority-badge {
        padding: 3px 8px;
        border-radius: 12px;
        font-size: 0.8em;
    }
    
    .priority-1 { background-color: #dc3545; }
    .priority-2 { background-color: #fd7e14; }
    .priority-3 { background-color: #ffc107; }
    .priority-4 { background-color: #20c997; }
    .priority-5 { background-color: #0d6efd; }
    
    .task-action-buttons button {
        margin-left: 5px;
    }
    
    .task-filters {
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0"><i class="bi bi-list-task me-2"></i>任务管理</h1>
                    <div>
                        <a href="{{ url_for('novel_to_video') }}" class="btn btn-outline-primary me-2">
                            <i class="bi bi-plus-circle me-1"></i>创建新项目
                        </a>
                        <button id="refreshBtn" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-clockwise me-1"></i>刷新
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="task-filters">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary active" onclick="filterTasks('all')">所有任务</button>
                            <button type="button" class="btn btn-outline-primary" onclick="filterTasks('pending')">等待中</button>
                            <button type="button" class="btn btn-outline-primary" onclick="filterTasks('processing')">处理中</button>
                            <button type="button" class="btn btn-outline-primary" onclick="filterTasks('completed')">已完成</button>
                            <button type="button" class="btn btn-outline-primary" onclick="filterTasks('failed')">失败</button>
                        </div>
                    </div>
                    
                    <div id="tasksList">
                        {% if tasks %}
                            {% for task in tasks %}
                            <div class="card task-card task-status-{{ task.status }}" data-task-id="{{ task.id }}">
                                <div class="card-header task-header">
                                    <div>
                                        <h5 class="mb-0">{{ task.title }}</h5>
                                        <small class="text-muted">项目: {{ task.project_title }}</small>
                                        {% if task.chapter_range %}
                                        <br><small class="text-info"><i class="bi bi-book me-1"></i>章节: {{ task.chapter_range }}</small>
                                        {% endif %}
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <span class="badge me-2 priority-badge priority-{{ task.priority }}">优先级: {{ task.priority }}</span>
                                        <span class="badge bg-{{ 'success' if task.status == 'completed' else 'warning' if task.status == 'processing' else 'danger' if task.status == 'failed' else 'secondary' }}">
                                            {{ '已完成' if task.status == 'completed' else '处理中' if task.status == 'processing' else '失败' if task.status == 'failed' else '等待中' }}
                                        </span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <div class="progress">
                                            <div class="progress-bar progress-bar-striped {{ 'progress-bar-animated' if task.status == 'processing' else '' }}" 
                                                 role="progressbar" 
                                                 style="width: {{ task.progress * 100 }}%" 
                                                 aria-valuenow="{{ task.progress * 100 }}" 
                                                 aria-valuemin="0" 
                                                 aria-valuemax="100"></div>
                                        </div>
                                        <div class="d-flex justify-content-between mt-1">
                                            <small class="text-muted">进度: {{ (task.progress * 100)|int }}%</small>
                                            <small class="text-muted">
                                                创建于: {{ task.created_at }}
                                                {% if task.started_at %}
                                                | 开始于: {{ task.started_at }}
                                                {% endif %}
                                                {% if task.completed_at %}
                                                | 完成于: {{ task.completed_at }}
                                                {% endif %}
                                            </small>
                                        </div>
                                    </div>
                                    
                                    {% if task.error_message %}
                                    <div class="alert alert-danger">
                                        <i class="bi bi-exclamation-triangle me-2"></i>错误信息: {{ task.error_message }}
                                    </div>
                                    {% endif %}
                                    
                                    <div class="task-action-buttons text-end">
                                        {% if task.status == 'pending' or task.status == 'processing' %}
                                            <div class="btn-group dropend me-2">
                                                <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                    优先级
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#" onclick="updatePriority({{ task.id }}, 1)">1 (最高)</a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="updatePriority({{ task.id }}, 2)">2</a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="updatePriority({{ task.id }}, 3)">3</a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="updatePriority({{ task.id }}, 4)">4</a></li>
                                                    <li><a class="dropdown-item" href="#" onclick="updatePriority({{ task.id }}, 5)">5 (最低)</a></li>
                                                </ul>
                                            </div>
                                        {% endif %}
                                        
                                        {% if task.status == 'processing' %}
                                        <button type="button" class="btn btn-sm btn-warning" onclick="pauseTask({{ task.id }})">
                                            <i class="bi bi-pause-fill me-1"></i>暂停
                                        </button>
                                        {% elif task.status == 'paused' %}
                                        <button type="button" class="btn btn-sm btn-success" onclick="resumeTask({{ task.id }})">
                                            <i class="bi bi-play-fill me-1"></i>继续
                                        </button>
                                        {% endif %}
                                        
                                        <!-- 删除按钮始终显示，无论任务状态如何 -->
                                        <button type="button" class="btn btn-sm btn-danger" onclick="deleteTask({{ task.id }})">
                                            <i class="bi bi-trash me-1"></i>删除
                                        </button>
                                        
                                        {% if task.status == 'completed' and task.output_path %}
                                        <a href="{{ url_for('download_task_video', task_id=task.id) }}" class="btn btn-sm btn-primary">
                                            <i class="bi bi-download me-1"></i>下载视频
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle me-2"></i>没有任何渲染任务。点击"创建新项目"开始创建视频。
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 自动刷新定时器
    let refreshTimer;
    const REFRESH_INTERVAL = 3000; // 3秒刷新一次
    
    // 页面加载完成后启动自动刷新
    document.addEventListener('DOMContentLoaded', function() {
        startAutoRefresh();
        
        // 手动刷新按钮
        document.getElementById('refreshBtn').addEventListener('click', function() {
            refreshTaskStatus(); // 立即刷新一次
        });
        
        // 给每个任务卡片添加 data-task-id 属性
        document.querySelectorAll('.task-card').forEach(card => {
            const taskId = card.getAttribute('data-task-id');
            if (!taskId) {
                // 如果没有id属性，尝试从内部元素(如按钮的onclick)提取ID
                const actionButton = card.querySelector('button[onclick*="Task("]');
                if (actionButton) {
                    const onclick = actionButton.getAttribute('onclick');
                    const match = onclick.match(/Task\((\d+)\)/);
                    if (match && match[1]) {
                        card.setAttribute('data-task-id', match[1]);
                    }
                }
            }
        });
    });
    
    // 启动自动刷新
    function startAutoRefresh() {
        // 清除可能存在的旧定时器
        if (refreshTimer) {
            clearInterval(refreshTimer);
        }
        
        // 设置新定时器
        refreshTimer = setInterval(function() {
            if (document.hasFocus()) {
                refreshTaskStatus();
            }
        }, REFRESH_INTERVAL);
        
        console.log('自动刷新已启动，间隔：', REFRESH_INTERVAL, 'ms');
    }
    
    // 只刷新任务状态，不重载整个页面
    function refreshTaskStatus() {
        console.log('正在刷新任务状态...');
        
        fetch('{{ url_for("get_tasks_status") }}')
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络响应不正常');
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    console.log('获取到最新任务数据:', data.tasks.length, '个任务');
                    updateTasksDisplay(data.tasks);
                } else {
                    console.error('服务器返回错误:', data.message);
                }
            })
            .catch(error => {
                console.error('刷新任务时出错:', error);
            });
    }
    
    // 更新任务显示
    function updateTasksDisplay(tasks) {
        if (!tasks || tasks.length === 0) {
            console.log('没有任务需要更新');
            return;
        }
        
        tasks.forEach(task => {
            // 查找任务卡片
            const taskCard = document.querySelector(`.task-card[data-task-id="${task.id}"]`);
            if (!taskCard) {
                console.log(`未找到任务 ID ${task.id} 的卡片元素，尝试从按钮中查找...`);
                // 尝试通过按钮的onclick属性查找卡片
                const actionButton = document.querySelector(`button[onclick*="Task(${task.id})"]`);
                if (actionButton) {
                    const card = actionButton.closest('.task-card');
                    if (card) {
                        card.setAttribute('data-task-id', task.id);
                        updateTaskCard(card, task);
                        return;
                    }
                }
                return;
            }
            
            updateTaskCard(taskCard, task);
        });
    }
    
    // 更新单个任务卡片
    function updateTaskCard(taskCard, task) {
        console.log(`更新任务 ${task.id}, 进度: ${Math.round(task.progress * 100)}%, 状态: ${task.status}`);
        
        // 更新章节范围信息
        const taskHeader = taskCard.querySelector('.task-header > div:first-child');
        if (taskHeader && task.chapter_range) {
            // 查找现有的章节信息
            let chapterInfo = taskHeader.querySelector('.text-info');
            if (!chapterInfo) {
                // 如果不存在章节信息，创建一个
                const br = document.createElement('br');
                chapterInfo = document.createElement('small');
                chapterInfo.className = 'text-info';
                chapterInfo.innerHTML = `<i class="bi bi-book me-1"></i>章节: ${task.chapter_range}`;
                taskHeader.appendChild(br);
                taskHeader.appendChild(chapterInfo);
            } else {
                // 更新现有章节信息
                chapterInfo.innerHTML = `<i class="bi bi-book me-1"></i>章节: ${task.chapter_range}`;
            }
        }
        
        // 更新进度条
        const progressBar = taskCard.querySelector('.progress-bar');
        if (progressBar) {
            const progressValue = task.progress * 100;
            progressBar.style.width = `${progressValue}%`;
            progressBar.setAttribute('aria-valuenow', progressValue);
            
            // 根据状态添加或移除动画
            if (task.status === 'processing') {
                progressBar.classList.add('progress-bar-animated');
            } else {
                progressBar.classList.remove('progress-bar-animated');
            }
        }
        
        // 更新进度文本
        const progressText = taskCard.querySelector('.progress + div small:first-child');
        if (progressText) {
            progressText.textContent = `进度: ${Math.round(task.progress * 100)}%`;
        }
        
        // 更新状态标签
        const statusBadge = taskCard.querySelector('.badge:not(.priority-badge)');
        if (statusBadge) {
            // 移除所有可能的状态类
            statusBadge.classList.remove('bg-success', 'bg-warning', 'bg-danger', 'bg-secondary');
            
            // 添加新的状态类和文本
            if (task.status === 'completed') {
                statusBadge.classList.add('bg-success');
                statusBadge.textContent = '已完成';
            } else if (task.status === 'processing') {
                statusBadge.classList.add('bg-warning');
                statusBadge.textContent = '处理中';
            } else if (task.status === 'failed') {
                statusBadge.classList.add('bg-danger');
                statusBadge.textContent = '失败';
            } else if (task.status === 'paused') {
                statusBadge.classList.add('bg-info');
                statusBadge.textContent = '已暂停';
            } else {
                statusBadge.classList.add('bg-secondary');
                statusBadge.textContent = '等待中';
            }
        }
        
        // 更新错误消息
        if (task.error_message) {
            let errorDiv = taskCard.querySelector('.alert-danger');
            if (!errorDiv) {
                // 如果不存在错误提示，创建一个
                errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger';
                errorDiv.innerHTML = `<i class="bi bi-exclamation-triangle me-2"></i>错误信息: ${task.error_message}`;
                
                // 插入到操作按钮之前
                const actionButtons = taskCard.querySelector('.task-action-buttons');
                if (actionButtons) {
                    actionButtons.parentNode.insertBefore(errorDiv, actionButtons);
                }
            } else {
                // 更新现有错误信息
                errorDiv.innerHTML = `<i class="bi bi-exclamation-triangle me-2"></i>错误信息: ${task.error_message}`;
            }
        }
        
        // 更新完成时间/开始时间信息
        const timeInfo = taskCard.querySelector('.d-flex.justify-content-between small.text-muted:last-child');
        if (timeInfo) {
            let timeText = `创建于: ${task.created_at}`;
            
            if (task.started_at) {
                timeText += ` | 开始于: ${task.started_at}`;
            }
            
            if (task.completed_at) {
                timeText += ` | 完成于: ${task.completed_at}`;
            }
            
            timeInfo.textContent = timeText;
        }
        
        // 更新任务卡片的过滤类
        taskCard.className = taskCard.className.replace(/task-status-\w+/g, '');
        taskCard.classList.add(`task-status-${task.status}`);
        
        // 更新操作按钮区域
        updateActionButtons(taskCard, task);
    }
    
    // 更新操作按钮
    function updateActionButtons(taskCard, task) {
        const actionButtonsDiv = taskCard.querySelector('.task-action-buttons');
        if (!actionButtonsDiv) return;
        
        // 保留删除按钮，因为它始终存在
        const deleteButton = actionButtonsDiv.querySelector('button.btn-danger');
        
        // 根据任务状态更新按钮
        // 检查当前状态
        if (task.status === 'processing') {
            // 如果没有暂停按钮，添加一个
            if (!actionButtonsDiv.querySelector('button[onclick*="pauseTask"]')) {
                // 删除可能存在的继续按钮
                const resumeButton = actionButtonsDiv.querySelector('button[onclick*="resumeTask"]');
                if (resumeButton) resumeButton.remove();
                
                // 创建暂停按钮
                const pauseButton = document.createElement('button');
                pauseButton.type = 'button';
                pauseButton.className = 'btn btn-sm btn-warning';
                pauseButton.setAttribute('onclick', `pauseTask(${task.id})`);
                pauseButton.innerHTML = '<i class="bi bi-pause-fill me-1"></i>暂停';
                
                // 如果有下载按钮，在下载按钮前插入
                const downloadButton = actionButtonsDiv.querySelector('a.btn-primary');
                if (downloadButton) {
                    actionButtonsDiv.insertBefore(pauseButton, downloadButton);
                } else {
                    // 否则在删除按钮前插入
                    actionButtonsDiv.insertBefore(pauseButton, deleteButton);
                }
            }
        } else if (task.status === 'paused') {
            // 如果没有继续按钮，添加一个
            if (!actionButtonsDiv.querySelector('button[onclick*="resumeTask"]')) {
                // 删除可能存在的暂停按钮
                const pauseButton = actionButtonsDiv.querySelector('button[onclick*="pauseTask"]');
                if (pauseButton) pauseButton.remove();
                
                // 创建继续按钮
                const resumeButton = document.createElement('button');
                resumeButton.type = 'button';
                resumeButton.className = 'btn btn-sm btn-success';
                resumeButton.setAttribute('onclick', `resumeTask(${task.id})`);
                resumeButton.innerHTML = '<i class="bi bi-play-fill me-1"></i>继续';
                
                // 如果有下载按钮，在下载按钮前插入
                const downloadButton = actionButtonsDiv.querySelector('a.btn-primary');
                if (downloadButton) {
                    actionButtonsDiv.insertBefore(resumeButton, downloadButton);
                } else {
                    // 否则在删除按钮前插入
                    actionButtonsDiv.insertBefore(resumeButton, deleteButton);
                }
            }
        } else {
            // 对于其他状态，移除暂停和继续按钮
            const pauseButton = actionButtonsDiv.querySelector('button[onclick*="pauseTask"]');
            if (pauseButton) pauseButton.remove();
            
            const resumeButton = actionButtonsDiv.querySelector('button[onclick*="resumeTask"]');
            if (resumeButton) resumeButton.remove();
        }
        
        // 处理下载按钮
        const existingDownloadButton = actionButtonsDiv.querySelector('a[href*="download_task_video"]');
        
        if (task.status === 'completed' && task.output_path) {
            // 任务完成且有输出路径，添加/保留下载按钮
            if (!existingDownloadButton) {
                const downloadButton = document.createElement('a');
                downloadButton.href = `{{ url_for('download_task_video', task_id=0) }}`.replace('0', task.id);
                downloadButton.className = 'btn btn-sm btn-primary';
                downloadButton.innerHTML = '<i class="bi bi-download me-1"></i>下载视频';
                actionButtonsDiv.appendChild(downloadButton);
            }
        } else {
            // 任务未完成或无输出路径，移除下载按钮
            if (existingDownloadButton) {
                existingDownloadButton.remove();
            }
        }
    }
    
    // 任务过滤器
    function filterTasks(status) {
        // 更新按钮状态
        const buttons = document.querySelectorAll('.task-filters .btn');
        buttons.forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');
        
        // 过滤任务
        const taskCards = document.querySelectorAll('.task-card');
        if (status === 'all') {
            taskCards.forEach(card => card.style.display = 'block');
        } else {
            taskCards.forEach(card => {
                if (card.classList.contains(`task-status-${status}`)) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }
    }
    
    // 暂停任务
    function pauseTask(taskId) {
        if (confirm('确定要暂停此任务吗？')) {
            fetch(`{{ url_for("pause_render_task", task_id=0) }}`.replace('0', taskId), {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    window.location.reload();
                } else {
                    alert('暂停任务失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error pausing task:', error);
                alert('发生错误，请重试');
            });
        }
    }
    
    // 继续任务
    function resumeTask(taskId) {
        if (confirm('确定要继续此任务吗？')) {
            fetch(`{{ url_for("resume_render_task", task_id=0) }}`.replace('0', taskId), {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    window.location.reload();
                } else {
                    alert('继续任务失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error resuming task:', error);
                alert('发生错误，请重试');
            });
        }
    }
    
    // 删除任务
    function deleteTask(taskId) {
        if (confirm('确定要删除此任务吗？此操作不可撤销。')) {
            fetch(`{{ url_for("delete_render_task", task_id=0) }}`.replace('0', taskId), {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    window.location.reload();
                } else {
                    alert('删除任务失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error deleting task:', error);
                alert('发生错误，请重试');
            });
        }
    }
    
    // 更新任务优先级
    function updatePriority(taskId, priority) {
        fetch(`{{ url_for("update_task_priority", task_id=0) }}`.replace('0', taskId), {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `priority=${priority}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                window.location.reload();
            } else {
                alert('更新优先级失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error updating priority:', error);
            alert('发生错误，请重试');
        });
    }
</script>
{% endblock %} 