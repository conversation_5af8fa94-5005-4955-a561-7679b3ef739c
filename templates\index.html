{% extends "base.html" %}

{% block title %}首页 - 小说云{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm">
            <div class="card-body">
                <h1 class="display-6 mb-3">欢迎使用小说云</h1>
                <p class="lead">小说云是一个多源小说搜索与下载系统，支持番茄小说和七猫小说。</p>
                <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                    <a href="{{ url_for('search') }}" class="btn btn-primary btn-lg px-4 me-md-2">
                        <i class="bi bi-search me-2"></i>搜索小说
                    </a>
                    <button class="btn btn-outline-secondary btn-lg px-4" data-bs-toggle="modal" data-bs-target="#fanqieCookieModal">
                        <i class="bi bi-gear me-2"></i>番茄Cookie配置 <span class="badge rounded-pill bg-{{ 'success' if cookie_status == '已配置' else 'secondary' }}">{{ cookie_status }}</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 番茄Cookie配置模态框 -->
<div class="modal fade" id="fanqieCookieModal" tabindex="-1" aria-labelledby="fanqieCookieModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fanqieCookieModalLabel">番茄小说Cookie配置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <p class="mb-0"><i class="bi bi-info-circle me-2"></i>配置番茄小说Cookie可以提高下载成功率和稳定性。请按照以下步骤操作：</p>
                    <ol class="mt-2 mb-0">
                        <li>使用Chrome或Edge浏览器，登录 <a href="https://fanqienovel.com" target="_blank">番茄小说网页版</a></li>
                        <li>按F12打开开发者工具，切换到"应用"或"Application"选项卡</li>
                        <li>在左侧找到"Cookie" > "https://fanqienovel.com"，查看Cookie列表</li>
                        <li>安装Cookie导出插件，例如<a href="https://chromewebstore.google.com/detail/cookie-editor/hlkenndednhfkekhgcdicdfddnkalmdm" target="_blank">Cookie Editor</a></li>
                        <li>使用插件导出Cookie为JSON格式，粘贴到下方文本框</li>
                    </ol>
                </div>
                
                <form id="cookieForm">
                    <div class="mb-3">
                        <label for="cookieJson" class="form-label">Cookie JSON数据</label>
                        <textarea class="form-control" id="cookieJson" rows="10" placeholder='[{"name": "cookie名称", "value": "cookie值", ...}, ...]'></textarea>
                    </div>
                    <div id="cookieStatus" class="mb-3 d-none">
                        <!-- 状态信息将显示在这里 -->
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="saveCookieBtn">保存配置</button>
            </div>
        </div>
    </div>
</div>

<h2 class="mb-4">已下载的小说</h2>

{% if novels %}
<div class="row row-cols-1 row-cols-md-3 row-cols-lg-4 g-4">
    {% for novel in novels %}
    <div class="col">
        <div class="card h-100 shadow-sm novel-card">
            <div class="novel-cover-container">
                {% if novel.cover_url %}
                <img src="{{ novel.cover_url }}" class="card-img-top novel-cover" alt="{{ novel.title }}">
                {% else %}
                <div class="no-cover">
                    <i class="bi bi-book"></i>
                </div>
                {% endif %}
            </div>
            <div class="card-body">
                <h5 class="card-title text-truncate">{{ novel.title }}</h5>
                <p class="card-text text-muted mb-1">作者: {{ novel.author }}</p>
                <p class="card-text text-muted mb-2">
                    <span class="badge rounded-pill bg-{{ 'success' if novel.source == 'fanqie' else 'info' }}">
                        {{ '番茄小说' if novel.source == 'fanqie' else '七猫小说' }}
                    </span>
                </p>
                <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="btn btn-outline-primary stretched-link">阅读</a>
            </div>
        </div>
    </div>
    {% endfor %}
</div>
{% else %}
<div class="alert alert-info">
    <i class="bi bi-info-circle me-2"></i>还没有下载过小说，请先 <a href="{{ url_for('search') }}" class="alert-link">搜索小说</a> 并下载
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 加载当前配置
    function loadCurrentCookie() {
        $.get('/fanqie_cookie', function(data) {
            if (data.has_cookie) {
                $('#cookieJson').val(data.cookie);
                $('#cookieStatus').removeClass('d-none alert-danger').addClass('alert alert-success');
                $('#cookieStatus').html('<i class="bi bi-check-circle me-2"></i>已配置Cookie');
            } else {
                $('#cookieStatus').removeClass('d-none alert-success').addClass('alert alert-secondary');
                $('#cookieStatus').html('<i class="bi bi-info-circle me-2"></i>未配置Cookie');
            }
        });
    }
    
    // 初始加载配置
    loadCurrentCookie();
    
    // 保存配置
    $('#saveCookieBtn').click(function() {
        var cookieJson = $('#cookieJson').val();
        if (!cookieJson) {
            $('#cookieStatus').removeClass('d-none alert-success alert-secondary').addClass('alert alert-danger');
            $('#cookieStatus').html('<i class="bi bi-exclamation-triangle me-2"></i>请输入Cookie数据');
            return;
        }
        
        // 发送请求
        $.ajax({
            url: '/fanqie_cookie',
            type: 'POST',
            data: {
                cookie_json: cookieJson
            },
            success: function(response) {
                $('#cookieStatus').removeClass('d-none alert-danger alert-secondary').addClass('alert alert-success');
                $('#cookieStatus').html('<i class="bi bi-check-circle me-2"></i>' + response.message);
                
                // 延迟更新状态标签
                setTimeout(function() {
                    location.reload();  // 刷新页面以更新状态标签
                }, 1000);
            },
            error: function(xhr) {
                var message = '保存失败';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    message = xhr.responseJSON.message;
                }
                $('#cookieStatus').removeClass('d-none alert-success alert-secondary').addClass('alert alert-danger');
                $('#cookieStatus').html('<i class="bi bi-exclamation-triangle me-2"></i>' + message);
            }
        });
    });
});
</script>
{% endblock %} 