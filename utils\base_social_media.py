"""
Base utilities for social media uploaders
"""

def set_init_script(driver=None, js_script=None):
    """
    Set initialization script for browser automation.
    
    Args:
        driver: The Selenium WebDriver instance
        js_script: JavaScript code to execute
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        if driver and js_script:
            driver.execute_script(js_script)
            return True
        return False
    except Exception as e:
        print(f"Error executing init script: {e}")
        return False 