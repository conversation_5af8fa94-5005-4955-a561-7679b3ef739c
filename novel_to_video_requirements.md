# 小说转视频功能需求文档

## 1. 项目概述

开发一个功能模块，将已下载的小说章节转换为带字幕的视频文件。该功能将允许用户选择特定章节，使用语音合成技术生成音频，添加字幕和背景音乐，最终生成完整视频。

## 2. 功能需求

### 2.1 章节选择
- 支持从已下载小说中选择单个或多个章节进行处理
- 提供章节预览和批量选择功能

### 2.2 语音合成
- 使用 edge_tts 将文本转换为语音
- 支持调整语音参数（语速、音调等）
- 支持多种语音选择

### 2.3 字幕生成
- 将生成的语音转换为 ASS 格式字幕
- 字幕时间轴与音频同步
- 支持自定义字幕样式（字体、大小、颜色）
- 支持调整字幕在屏幕上的位置

### 2.4 视频标题
- 支持添加自定义视频标题
- 允许编辑标题文字、字体和颜色
- 可调整标题在视频中的位置和显示时长

### 2.5 背景音乐
- 支持添加背景音乐
- 允许调整背景音乐音量
- 支持音乐循环播放或裁剪以匹配视频长度

### 2.6 视频合成
- 使用 ffmpeg 将字幕、音频和背景音乐合成为视频
- 视频长度基于生成的语音音频长度
- 显示合成进度
- 支持多线程处理提高合成速度
- 支持 GPU 加速（自动检测并支持 AMD/NVIDIA 显卡）

### 2.7 文本替换功能
- 维护一个文本替换列表，用于修正已知的错别字或特殊术语
- 在字幕生成前自动应用替换规则
- 提供界面添加、编辑和删除替换规则

### 2.8 任务管理系统
- 支持创建多个视频合成任务并同时处理
- 提供任务列表页面查看所有任务及其状态
- 允许手动暂停、重启和删除任务
- 显示任务进度、状态和预计完成时间
- 支持任务优先级设置
- 提供任务完成后的通知提醒

## 3. 技术规格

### 3.1 使用技术
- edge_tts：文本到语音转换
- ffmpeg：音视频处理和合成
- SQLite：本地数据存储
- GPU 加速框架：支持 AMD 和 NVIDIA 显卡

### 3.2 数据存储
所有设置和配置将存储在 SQLite 数据库中，包括：
- 文本替换规则
- 字幕样式设置
- 语音合成参数
- 视频合成配置
- 处理历史记录
- 任务队列与状态

### 3.3 性能要求
- 支持多线程处理提高转换速度
- 利用 GPU 加速视频编码（自动检测系统显卡类型）
- 提供实时进度显示

## 4. 用户界面

### 4.1 章节选择界面
- 显示小说信息和可用章节列表
- 提供章节预览和多选功能

### 4.2 配置界面
- 语音合成参数配置
- 字幕样式和位置设置
- 视频标题编辑
- 背景音乐选择

### 4.3 处理界面
- 显示处理进度和状态
- 提供取消选项
- 完成后提供预览功能

### 4.4 任务管理界面
- 显示所有任务列表及其状态
- 提供任务控制按钮（暂停/继续、删除）
- 显示任务详细信息（章节、设置等）
- 支持按状态筛选任务

## 5. 工作流程

1. 用户选择已下载的小说和特定章节
2. 配置语音合成、字幕样式和视频标题
3. 添加背景音乐（可选）
4. 启动视频生成流程：
   - 文本预处理（应用替换规则）
   - 文本转语音
   - 生成 ASS 字幕
   - 合成最终视频
5. 显示处理进度
6. 完成后提供视频文件访问和预览选项

## 6. 未来扩展

- 支持批量处理多个小说
- 添加更多字幕特效
- 支持自定义视频分辨率和格式
- 集成更多 TTS 引擎
- 添加自定义视频背景 