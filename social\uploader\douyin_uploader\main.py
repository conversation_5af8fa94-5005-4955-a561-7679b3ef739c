# -*- coding: utf-8 -*-
from datetime import datetime

from playwright.async_api import <PERSON><PERSON>, async_playwright, Page
import os
import asyncio

from social.conf import LOCAL_CHROME_PATH
from social.utils.base_social_media import set_init_script
from social.utils.log import douyin_logger


async def cookie_auth(account_file):
    async with async_playwright() as playwright:
        browser = await playwright.chromium.launch(headless=True)
        context = await browser.new_context(storage_state=account_file)
        context = await set_init_script(context)
        # 创建一个新的页面
        page = await context.new_page()
        # 访问指定的 URL
        await page.goto("https://creator.douyin.com/creator-micro/content/upload")
        try:
            await page.wait_for_url("https://creator.douyin.com/creator-micro/content/upload", timeout=5000)
        except:
            print("[+] 等待5秒 cookie 失效")
            await context.close()
            await browser.close()
            return False
        # 2024.06.17 抖音创作者中心改版
        if await page.get_by_text('手机号登录').count():
            print("[+] 等待5秒 cookie 失效")
            return False
        else:
            print("[+] cookie 有效")
            return True

async def douyin_check_cookie(cookies):
    async with async_playwright() as playwright:
        browser = await playwright.chromium.launch(headless=True)
        context = await browser.new_context(storage_state=cookies)
        context = await set_init_script(context)
        # 创建一个新的页面
        page = await context.new_page()
        # 访问指定的 URL
        await page.goto("https://creator.douyin.com/creator-micro/content/upload")
        try:
            await page.wait_for_url("https://creator.douyin.com/creator-micro/content/upload", timeout=5000)
        except:
            print("[+] 等待5秒 cookie 失效")
            return False
        # 2024.06.17 抖音创作者中心改版
        if await page.get_by_text('验证码登录').count():
            print("[+] 等待5秒 cookie 失效")
            return False
        else:
            print("[+] cookie 有效")
            return True        


async def douyin_setup(account_file, handle=False):
    if not os.path.exists(account_file) or not await cookie_auth(account_file):
        if not handle:
            # Todo alert message
            return False
        douyin_logger.info('[+] cookie文件不存在或已失效，即将自动打开浏览器，请扫码登录，登陆后会自动生成cookie文件')
        await douyin_cookie_gen(account_file)
    return True


async def douyin_cookie_gen_without_login():
    async with async_playwright() as playwright:
        options = {
            'headless': False
        }
        # Make sure to run headed.
        browser = await playwright.chromium.launch(**options)
        # Setup context however you like.
        context = await browser.new_context()  # Pass any options
        context = await set_init_script(context)
        # Pause the page, and start recording manually.
        page = await context.new_page()
        await page.goto("https://creator.douyin.com/")
        await page.pause()
        # 点击调试器的继续，保存cookie
        cookie_data = await context.storage_state()
        await context.close()
        await browser.close()
        return cookie_data

    # async with async_playwright() as playwright:
    #     options = {
    #         'headless': False
    #     }
    #     # Make sure to run headed.
    #     browser = await playwright.chromium.launch(**options)
    #     # Setup context however you like.
    #     context = await browser.new_context()  # Pass any options
    #     context = await set_init_script(context)
    #     # Pause the page, and start recording manually.
    #     page = await context.new_page()
    #     await page.goto("https://creator.douyin.com/")
    #     await page.pause()
    #     # 获取cookie并返回
    #     cookie_data = await context.storage_state()
    #     await context.close()
    #     await browser.close()
    #     return cookie_data


async def douyin_cookie_gen(account_file):
    async with async_playwright() as playwright:
        options = {
            'headless': False
        }
        # Make sure to run headed.
        browser = await playwright.chromium.launch(**options)
        # Setup context however you like.
        context = await browser.new_context()  # Pass any options
        context = await set_init_script(context)
        # Pause the page, and start recording manually.
        page = await context.new_page()
        await page.goto("https://creator.douyin.com/")
        await page.pause()
        # 点击调试器的继续，保存cookie到文件并返回cookie数据
        cookie_data = await context.storage_state(path=account_file)
        await context.close()
        await browser.close()
        return cookie_data


class DouYinVideo(object):
    def __init__(self, title, file_path, desc,tags, publish_date: datetime, cookie_dict, thumbnail_path=None):
        self.title = title  # 视频标题
        self.file_path = file_path
        self.tags = tags
        self.publish_date = 0
        self.desc = desc
        self.cookie_dict = cookie_dict
        self.date_format = '%Y年%m月%d日 %H:%M'
        self.local_executable_path = LOCAL_CHROME_PATH
        self.thumbnail_path = thumbnail_path

    async def set_schedule_time_douyin(self, page, publish_date):
        # 选择包含特定文本内容的 label 元素
        label_element = page.locator("[class^='radio']:has-text('定时发布')")
        # 在选中的 label 元素下点击 checkbox
        await label_element.click()
        await asyncio.sleep(1)
        publish_date_hour = publish_date.strftime("%Y-%m-%d %H:%M")

        await asyncio.sleep(1)
        await page.locator('.semi-input[placeholder="日期和时间"]').click()
        await page.keyboard.press("Control+KeyA")
        await page.keyboard.type(str(publish_date_hour))
        await page.keyboard.press("Enter")

        await asyncio.sleep(1)

    async def handle_upload_error(self, page):
        douyin_logger.info('视频出错了，重新上传中')
        await page.locator('div.progress-div [class^="upload-btn-input"]').set_input_files(self.file_path)

    async def upload(self, playwright: Playwright) -> None:
        # 使用 Chromium 浏览器启动一个浏览器实例
        if self.local_executable_path:
            browser = await playwright.chromium.launch(headless=False, executable_path=self.local_executable_path)
        else:
            browser = await playwright.chromium.launch(headless=False)
        # 创建一个浏览器上下文，使用指定的 cookie 文件
        context = await browser.new_context(storage_state=self.cookie_dict)
        context = await set_init_script(context)

        # 创建一个新的页面
        page = await context.new_page()
        # 访问指定的 URL
        await page.goto("https://creator.douyin.com/creator-micro/content/upload")
        douyin_logger.info(f'[+]正在上传-------{self.title}.mp4')
        # 等待页面跳转到指定的 URL，没进入，则自动等待到超时
        douyin_logger.info(f'[-] 正在打开主页...')
        await page.wait_for_url("https://creator.douyin.com/creator-micro/content/upload")
        # 点击 "上传视频" 按钮
        await page.locator("div[class^='container'] input").set_input_files(self.file_path)

        # 等待页面跳转到指定的 URL 2025.01.08修改在原有基础上兼容两种页面
        while True:
            try:
                # 尝试等待第一个 URL
                await page.wait_for_url(
                    "https://creator.douyin.com/creator-micro/content/publish?enter_from=publish_page", timeout=3)
                douyin_logger.info("[+] 成功进入version_1发布页面!")
                break  # 成功进入页面后跳出循环
            except Exception:
                try:
                    # 如果第一个 URL 超时，再尝试等待第二个 URL
                    await page.wait_for_url(
                        "https://creator.douyin.com/creator-micro/content/post/video?enter_from=publish_page",
                        timeout=3)
                    douyin_logger.info("[+] 成功进入version_2发布页面!")

                    break  # 成功进入页面后跳出循环
                except:
                    print("  [-] 超时未进入视频发布页面，重新尝试...")
                    await asyncio.sleep(0.5)  # 等待 0.5 秒后重新尝试
        # 填充标题和话题
        # 检查是否存在包含输入框的元素
        # 这里为了避免页面变化，故使用相对位置定位：作品标题父级右侧第一个元素的input子元素
        await asyncio.sleep(1)
        # douyin_logger.info(f'  [-] 正在填充标题和话题...')
        # title_container = page.get_by_text('作品标题').locator("..").locator("xpath=following-sibling::div[1]").locator("input")
        # if await title_container.count():
        #     await title_container.fill(self.title[:30])
        # else:
        #     titlecontainer = page.locator(".notranslate")
        #     await titlecontainer.click()
        #     await page.keyboard.press("Backspace")
        #     await page.keyboard.press("Control+KeyA")
        #     await page.keyboard.press("Delete")
        #     await page.keyboard.type(self.title)
        #     await page.keyboard.press("Enter")
        # css_selector = ".zone-container"
        # for index, tag in enumerate(self.tags, start=1):
        #     await page.type(css_selector, "#" + tag)
        #     await page.press(css_selector, "Space")
        # douyin_logger.info(f'总共添加{len(self.tags)}个话题')
        douyin_logger.info(f'  [-] 正在填充标题和话题...')
        
        inputLocator = page.locator('input.semi-input[placeholder="填写作品标题，为作品获得更多流量"]');
        await inputLocator.click(); 
        await inputLocator.fill(self.title);


        title_container = page.get_by_text('作品标题').locator("..").locator("xpath=following-sibling::div[1]").locator("input")
        if await title_container.count():
            await title_container.fill(self.title[:30])
        else:
            titlecontainer = page.locator(".notranslate")
            await titlecontainer.click()
            await page.keyboard.press("Backspace")
            await page.keyboard.press("Control+KeyA")
            await page.keyboard.press("Delete")
            await page.keyboard.type(self.desc)
            await page.keyboard.press("Enter")
        css_selector = ".zone-container"
        for index, tag in enumerate(self.tags, start=1):
            await page.type(css_selector, "#" + tag)
            await page.press(css_selector, "Space")
        douyin_logger.info(f'总共添加{len(self.tags)}个话题')
        while True:
            # 判断重新上传按钮是否存在，如果不存在，代表视频正在上传，则等待
            try:
                #  新版：定位重新上传
                number = await page.locator('[class^="long-card"] div:has-text("重新上传")').count()
                if number > 0:
                    douyin_logger.success("  [-]视频上传完毕")
                    break
                else:
                    douyin_logger.info("  [-] 正在上传视频中...")
                    await asyncio.sleep(2)

                    if await page.locator('div.progress-div > div:has-text("上传失败")').count():
                        douyin_logger.error("  [-] 发现上传出错了... 准备重试")
                        await self.handle_upload_error(page)
            except:
                douyin_logger.info("  [-] 正在上传视频中...")
                await asyncio.sleep(2)
        
        #上传视频封面
        await self.set_thumbnail(page, self.thumbnail_path)

        # 更换可见元素
        # await self.set_location(page, "杭州市")

        # 頭條/西瓜
        third_part_element = '[class^="info"] > [class^="first-part"] div div.semi-switch'
        # 定位是否有第三方平台
        if await page.locator(third_part_element).count():
            # 检测是否是已选中状态
            if 'semi-switch-checked' not in await page.eval_on_selector(third_part_element, 'div => div.className'):
                await page.locator(third_part_element).locator('input.semi-switch-native-control').click()

        if self.publish_date != 0:
            await self.set_schedule_time_douyin(page, self.publish_date)

        # 判断视频是否发布成功
        while True:
            # 判断视频是否发布成功
            try:
                publish_button = page.get_by_role('button', name="发布", exact=True)
                if await publish_button.count():
                    await publish_button.click()
                await page.wait_for_url("https://creator.douyin.com/creator-micro/content/manage**",
                                        timeout=3000)  # 如果自动跳转到作品页面，则代表发布成功
                douyin_logger.success("  [-]视频发布成功")
                break
            except:
                douyin_logger.info("  [-] 视频正在发布中...")
                await page.screenshot(full_page=True)
                await asyncio.sleep(0.5)

        cookie_data = await context.storage_state()  # 保存cookie
        douyin_logger.success('  [-]cookie更新完毕！')
        await asyncio.sleep(2)  # 这里延迟是为了方便眼睛直观的观看
        # 关闭浏览器上下文和浏览器实例
        await context.close()
        await browser.close()
        return cookie_data
    
    async def set_thumbnail(self, page: Page, thumbnail_path: str):
        if thumbnail_path:
            douyin_logger.info('  [-] 开始上传封面...')
            try:
                # 截图记录当前状态
                await page.screenshot(path="before_upload.png")
                
                # 首先确保封面文件存在且可读
                if not os.path.exists(thumbnail_path):
                    douyin_logger.error(f'  [-] 封面文件不存在: {thumbnail_path}')
                    return
                
                douyin_logger.info(f'  [-] 封面文件确认存在: {thumbnail_path}')
                
                # 找到点击封面区域
                cover_selectors = [
                    "div[class*='coverControl'] div[class*='cover']", 
                    ".coverControl-n0Dxfz .cover-ybR0xM",
                    ".coverContainer-NNTF1U .cover-ybR0xM", 
                    "div[class*='cover-']:not(:has(img))",
                    "div[role='button']:has-text('选择封面')"
                ]
                
                cover_clicked = False
                for selector in cover_selectors:
                    try:
                        if await page.locator(selector).count() > 0:
                            douyin_logger.info(f'  [-] 找到封面区域: {selector}')
                            await page.locator(selector).first.click()
                            douyin_logger.info('  [-] 点击了封面区域')
                            cover_clicked = True
                            await page.wait_for_timeout(2000)
                            break
                    except Exception as e:
                        douyin_logger.warning(f'  [-] 点击封面区域失败: {str(e)}')
                
                # 截图记录点击后的状态
                await page.screenshot(path="after_cover_click.png")
                
                # ===== 重点：检测并直接使用 semi-upload 上传组件 =====
                # 根据错误日志，我们应该直接定位到上传输入元素，而不是点击可能被拦截的按钮
                douyin_logger.info('  [-] 尝试直接定位上传输入元素...')
                
                upload_component_selectors = [
                    "div.semi-upload input[type='file']",
                    "div.upload-BvM5FF input[type='file']",  # 从错误日志中提取的类名
                    "div[class*='upload'] input[type='file']",
                    "input[type='file']"
                ]
                
                # 截图上传组件查找前状态
                await page.screenshot(path="before_find_upload_input.png")
                
                # 先检查是否存在上传输入元素
                upload_input_found = False
                for selector in upload_component_selectors:
                    try:
                        if await page.locator(selector).count() > 0:
                            douyin_logger.info(f'  [-] 找到上传输入元素: {selector}')
                            upload_input_found = True
                            # 使元素可见并解除限制
                            await page.evaluate("""
                                (selector) => {
                                    const inputs = document.querySelectorAll(selector);
                                    for (let input of inputs) {
                                        // 移除隐藏和禁用
                                        input.style.display = 'block';
                                        input.style.opacity = '1';
                                        input.style.visibility = 'visible';
                                        input.style.pointerEvents = 'auto';
                                        input.style.position = 'relative';
                                        input.style.zIndex = '9999';
                                        input.disabled = false;
                                        input.removeAttribute('disabled');
                                    }
                                    return inputs.length;
                                }
                            """, selector)
                            
                            # 上传文件
                            await page.locator(selector).first.set_input_files(thumbnail_path)
                            douyin_logger.info(f'  [-] 成功设置上传文件: {thumbnail_path}')
                            await page.wait_for_timeout(5000)  # 等待上传完成
                            break
                    except Exception as e:
                        douyin_logger.warning(f'  [-] 上传输入元素操作失败 {selector}: {str(e)}')
                
                # 如果无法找到上传输入元素，尝试直接点击拦截点击事件的元素
                if not upload_input_found:
                    douyin_logger.info('  [-] 尝试点击拦截元素和实际上传按钮...')
                    
                    # 从错误日志中，我们知道拦截元素是 <use xlink:href="#semi-icons-upload"></use>
                    # 直接处理这个元素和其父元素
                    intercepting_selectors = [
                        "use[xlink\\:href='#semi-icons-upload']",
                        "div.semi-upload",
                        "div.upload-BvM5FF",
                        "svg[class*='upload']"
                    ]
                    
                    for selector in intercepting_selectors:
                        try:
                            if await page.locator(selector).count() > 0:
                                douyin_logger.info(f'  [-] 找到上传按钮/拦截元素: {selector}')
                                
                                # 将点击事件传递到正确的元素
                                await page.evaluate("""
                                    (selector) => {
                                        const element = document.querySelector(selector);
                                        if (!element) return false;
                                        
                                        // 查找隐藏的文件输入元素
                                        const fileInput = element.querySelector('input[type="file"]') || 
                                                         document.querySelector('input[type="file"]');
                                        
                                        if (fileInput) {
                                            // 触发点击
                                            fileInput.click();
                                            return true;
                                        }
                                        return false;
                                    }
                                """, selector)
                                
                                # 尝试等待文件选择器
                                try:
                                    async with page.expect_file_chooser(timeout=3000) as fc_info:
                                        # 文件选择器出现，设置文件
                                        file_chooser = await fc_info.value
                                        await file_chooser.set_files(thumbnail_path)
                                        douyin_logger.info('  [-] 通过FileChooser上传文件成功')
                                        await page.wait_for_timeout(5000)
                                        upload_input_found = True
                                        break
                                except Exception as e:
                                    douyin_logger.warning(f'  [-] FileChooser等待超时: {str(e)}')
                        except Exception as e:
                            douyin_logger.warning(f'  [-] 拦截元素操作失败: {str(e)}')
                
                # 创建和注入自定义文件上传输入
                if not upload_input_found:
                    douyin_logger.info('  [-] 尝试创建自定义文件上传输入...')
                    
                    # 创建自定义文件上传输入
                    await page.evaluate("""
                        () => {
                            // 移除所有现有的文件输入框的点击拦截
                            document.querySelectorAll('input[type="file"]').forEach(input => {
                                const parent = input.parentElement;
                                if (parent) {
                                    // 清理所有子元素的pointer-events
                                    Array.from(parent.querySelectorAll('*')).forEach(el => {
                                        el.style.pointerEvents = 'none';
                                    });
                                    // 确保输入框可点击
                                    input.style.pointerEvents = 'auto';
                                    input.style.display = 'block';
                                    input.style.opacity = '1';
                                    input.style.position = 'absolute';
                                    input.style.top = '0';
                                    input.style.left = '0';
                                    input.style.width = '100%';
                                    input.style.height = '100%';
                                    input.style.zIndex = '9999';
                                }
                            });
                            
                            // 如果没有找到现有输入框，创建一个新的
                            if (document.querySelectorAll('input[type="file"]').length === 0) {
                                const input = document.createElement('input');
                                input.id = 'custom-upload-input';
                                input.type = 'file';
                                input.accept = 'image/*';
                                input.style.position = 'fixed';
                                input.style.top = '10px';
                                input.style.left = '10px';
                                input.style.zIndex = '9999';
                                input.style.opacity = '1';
                                document.body.appendChild(input);
                                return {created: true};
                            }
                            
                            return {fixed: true};
                        }
                    """)
                    
                    # 尝试使用新创建的输入框
                    try:
                        await page.locator('#custom-upload-input, input[type="file"]').first.set_input_files(thumbnail_path)
                        douyin_logger.info('  [-] 通过自定义输入上传文件成功')
                        await page.wait_for_timeout(5000)
                        upload_input_found = True
                    except Exception as e:
                        douyin_logger.error(f'  [-] 自定义输入上传失败: {str(e)}')
                
                # 截图记录上传状态
                await page.screenshot(path="after_upload_attempt.png")
                
                if not upload_input_found:
                    douyin_logger.error('  [-] 所有上传方法均失败，无法上传封面')
                    return
                
                # 等待上传完成，尝试点击完成按钮
                douyin_logger.info('  [-] 等待上传完成...')
                await page.wait_for_timeout(5000)
                
                # 点击完成按钮
                finish_button_selectors = [
                    "button:has-text('完成')",
                    "div[class*='footer'] button:has-text('完成')",
                    "div[class*='confirmBtn'] div:has-text('完成')",
                    "div.semi-modal-footer button:nth-child(2)"
                ]
                
                finish_clicked = False
                for selector in finish_button_selectors:
                    try:
                        if await page.locator(selector).count() > 0:
                            douyin_logger.info(f'  [-] 找到完成按钮: {selector}')
                            
                            # 移除禁用状态
                            await page.evaluate("""
                                (selector) => {
                                    const buttons = document.querySelectorAll(selector);
                                    for (const btn of buttons) {
                                        // 移除禁用状态
                                        btn.disabled = false;
                                        btn.removeAttribute('disabled');
                                        btn.style.pointerEvents = 'auto';
                                    }
                                }
                            """, selector)
                            
                            await page.locator(selector).first.click()
                            douyin_logger.success('  [-] 成功点击完成按钮')
                            finish_clicked = True
                            await page.wait_for_timeout(2000)
                            break
                    except Exception as e:
                        douyin_logger.warning(f'  [-] 点击完成按钮失败: {str(e)}')
                
                # 如果无法点击完成按钮，尝试JavaScript点击
                if not finish_clicked:
                    douyin_logger.info('  [-] 尝试使用JavaScript点击完成按钮')
                    await page.evaluate("""
                        () => {
                            // 尝试点击任何包含"完成"文本的按钮
                            const buttons = Array.from(document.querySelectorAll('button, div[role="button"]'));
                            for (const btn of buttons) {
                                if (btn.textContent && btn.textContent.includes('完成')) {
                                    // 修复禁用状态
                                    btn.disabled = false;
                                    btn.removeAttribute('disabled');
                                    btn.click();
                                    return true;
                                }
                            }
                            
                            // 点击模态框底部的任何按钮
                            const footerButtons = document.querySelectorAll('.semi-modal-footer button, div[class*="footer"] button');
                            if (footerButtons.length > 0) {
                                // 点击最后一个按钮（通常是确认按钮）
                                const confirmButton = footerButtons[footerButtons.length - 1];
                                confirmButton.disabled = false;
                                confirmButton.removeAttribute('disabled');
                                confirmButton.click();
                                return true;
                            }
                            
                            return false;
                        }
                    """)
                
                # 最终截图记录状态
                await page.screenshot(path="upload_complete.png")
                douyin_logger.success('  [-] 封面上传流程完成')
                
            except Exception as e:
                douyin_logger.error(f'  [-] 封面上传过程中出错: {str(e)}')
                await page.screenshot(path="thumbnail_error.png")
                # 继续执行，不因为封面上传失败而终止整个上传过程

    async def set_location(self, page: Page, location: str = "杭州市"):
        # todo supoort location later
        # await page.get_by_text('添加标签').locator("..").locator("..").locator("xpath=following-sibling::div").locator(
        #     "div.semi-select-single").nth(0).click()
        await page.locator('div.semi-select span:has-text("输入地理位置")').click()
        await page.keyboard.press("Backspace")
        await page.wait_for_timeout(2000)
        await page.keyboard.type(location)
        await page.wait_for_selector('div[role="listbox"] [role="option"]', timeout=5000)
        await page.locator('div[role="listbox"] [role="option"]').first.click()

    async def main(self):
        async with async_playwright() as playwright:
              cookie_data = await self.upload(playwright)
              return cookie_data



