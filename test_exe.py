#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本，用于验证exe打包是否正常
"""

import sys
import os
from pathlib import Path

print("=== 测试开始 ===")
print(f"Python版本: {sys.version}")
print(f"当前工作目录: {os.getcwd()}")
print(f"脚本路径: {__file__}")
print(f"sys.executable: {sys.executable}")

# 测试基本导入
try:
    import requests
    print("✓ requests导入成功")
except ImportError as e:
    print(f"✗ requests导入失败: {e}")

try:
    from lxml import etree
    print("✓ lxml.etree导入成功")
except ImportError as e:
    print(f"✗ lxml.etree导入失败: {e}")

try:
    import sqlite3
    print("✓ sqlite3导入成功")
except ImportError as e:
    print(f"✗ sqlite3导入失败: {e}")

try:
    from flask import Flask
    print("✓ Flask导入成功")
except ImportError as e:
    print(f"✗ Flask导入失败: {e}")

# 测试小说下载模块
try:
    from novel_sources.fanqie import FanqieDownloader
    print("✓ FanqieDownloader导入成功")
except ImportError as e:
    print(f"✗ FanqieDownloader导入失败: {e}")

try:
    from novel_sources.fanqie_api import get_fanqie_content
    print("✓ get_fanqie_content导入成功")
except ImportError as e:
    print(f"✗ get_fanqie_content导入失败: {e}")

try:
    from database import NovelDatabase
    print("✓ NovelDatabase导入成功")
except ImportError as e:
    print(f"✗ NovelDatabase导入失败: {e}")

# 测试数据库文件
db_path = "novels.db"
if os.path.exists(db_path):
    print(f"✓ 数据库文件存在: {db_path}")
else:
    print(f"✗ 数据库文件不存在: {db_path}")

# 测试模板和静态文件
if os.path.exists("templates"):
    print("✓ templates目录存在")
else:
    print("✗ templates目录不存在")

if os.path.exists("static"):
    print("✓ static目录存在")
else:
    print("✗ static目录不存在")

print("=== 测试结束 ===")
input("按回车键退出...")