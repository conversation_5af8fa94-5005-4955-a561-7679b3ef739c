import sqlite3
import json
import time
import random
import datetime

class NovelDatabase:
    def __init__(self, db_path):
        self.db_path = db_path
        # 设置较长的超时时间和启用WAL模式
        self.timeout = 30.0
        self.init_db()  # 确保数据库初始化
        self.run_migrations()  # 运行数据库迁移
        
    def get_connection(self):
        """获取一个数据库连接，增加超时设置和WAL模式"""
        conn = sqlite3.connect(self.db_path, timeout=self.timeout)
        # 启用WAL模式，减少写入锁定
        conn.execute('PRAGMA journal_mode=WAL')
        # 配置连接以减少锁定冲突
        conn.execute('PRAGMA busy_timeout=10000')  # 10秒的忙等待超时
        # 设置时区为北京时间 (UTC+8)
        # SQLite不直接支持时区，我们通过datetime('now', '+8 hours')来获取北京时间
        conn.row_factory = sqlite3.Row
        return conn

    def execute_with_retry(self, func, max_retries=5):
        """执行数据库操作，带有重试机制"""
        for attempt in range(max_retries):
            try:
                return func()
            except sqlite3.OperationalError as e:
                if "database is locked" in str(e) and attempt < max_retries - 1:
                    # 数据库锁定，等待后重试
                    wait_time = (2 ** attempt) * 0.1 + random.random()  # 指数退避
                    print(f"Database locked, retrying in {wait_time:.2f}s (attempt {attempt+1}/{max_retries})")
                    time.sleep(wait_time)
                else:
                    raise  # 重试次数用完，或者不是锁定错误，抛出异常

    def init_db(self):
        """Initialize the database with required tables"""
        def _init():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Create novels table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS novels (
                id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                author TEXT,
                cover_url TEXT,
                description TEXT,
                source TEXT NOT NULL,
                chapter_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT (datetime('now', '+8 hours'))
            )
            ''')
            
            # Create scheduled_tasks table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS scheduled_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                novel_id TEXT NOT NULL,
                novel_source TEXT NOT NULL,
                schedule_time TIMESTAMP NOT NULL,
                interval_seconds INTEGER NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                last_run TIMESTAMP,
                auto_generate_video BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT (datetime('now', '+8 hours')),
                FOREIGN KEY (novel_id) REFERENCES novels(id)
            )
            ''')
            
            # Create settings table
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                key TEXT PRIMARY KEY,
                value TEXT,
                updated_at TIMESTAMP DEFAULT (datetime('now', '+8 hours'))
            )
            ''')
            
            # Create text_replacements table for text replacement rules
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS text_replacements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                original_text TEXT NOT NULL,
                replacement_text TEXT NOT NULL,
                project_id INTEGER,
                created_at TIMESTAMP DEFAULT (datetime('now', '+8 hours'))
            )
            ''')
            
            # Create tts_voices table for available TTS voices
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS tts_voices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                voice_id TEXT NOT NULL UNIQUE,
                display_name TEXT NOT NULL,
                language TEXT NOT NULL,
                gender TEXT,
                is_favorite INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT (datetime('now', '+8 hours'))
            )
            ''')
            
            # Create video_projects table for novel chapter to video projects
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS video_projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                title TEXT NOT NULL,
                novel_id TEXT,
                created_at TIMESTAMP DEFAULT (datetime('now', '+8 hours')),
                updated_at TIMESTAMP DEFAULT (datetime('now', '+8 hours')),
                status TEXT DEFAULT 'draft',
                FOREIGN KEY (novel_id) REFERENCES novels(id)
            )
            ''')
            
            # Create video_project_chapters table for chapters in a video project
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS video_project_chapters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER NOT NULL,
                novel_id TEXT NOT NULL,
                chapter_id TEXT NOT NULL,
                order_index INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT (datetime('now', '+8 hours')),
                FOREIGN KEY (project_id) REFERENCES video_projects(id) ON DELETE CASCADE,
                FOREIGN KEY (novel_id) REFERENCES novels(id)
            )
            ''')
            
            # Create pending_chapter_updates table for storing chapters pending update
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS pending_chapter_updates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                novel_id TEXT NOT NULL,
                chapter_id TEXT NOT NULL,
                update_time TIMESTAMP DEFAULT (datetime('now', '+8 hours')),
                last_check_time TIMESTAMP DEFAULT (datetime('now', '+8 hours')),
                status TEXT DEFAULT 'pending',
                FOREIGN KEY (novel_id) REFERENCES novels(id),
                UNIQUE(novel_id, chapter_id)
            )
            ''')
            
            # Create video_settings table for video generation settings
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS video_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER NOT NULL UNIQUE,
                voice_id TEXT,
                voice_rate REAL DEFAULT 1.0,
                voice_volume REAL DEFAULT 1.0,
                subtitle_font TEXT,
                subtitle_size INTEGER,
                subtitle_color TEXT,
                subtitle_position TEXT,
                subtitle_x INTEGER,
                subtitle_y INTEGER,
                title_text TEXT,
                title_font TEXT,
                title_size INTEGER,
                title_color TEXT,
                title_position TEXT,
                title_x INTEGER,
                title_y INTEGER,
                bg_music_path TEXT,
                bg_music_volume REAL DEFAULT 0.5,
                bg_video_path TEXT,
                bg_video_thumbnail TEXT,
                video_width INTEGER DEFAULT 1280,
                video_height INTEGER DEFAULT 720,
                use_gpu INTEGER DEFAULT 1,
                subtitle_api TEXT DEFAULT 'jianying',
                created_at TIMESTAMP DEFAULT (datetime('now', '+8 hours')),
                updated_at TIMESTAMP DEFAULT (datetime('now', '+8 hours')),
                FOREIGN KEY (project_id) REFERENCES video_projects(id) ON DELETE CASCADE
            )
            ''')
            
            # Create video_renders table for tracking video render progress and history
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS video_renders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER NOT NULL,
                start_time TIMESTAMP,
                end_time TIMESTAMP,
                status TEXT DEFAULT 'pending',
                progress REAL DEFAULT 0,
                output_path TEXT,
                error_message TEXT,
                created_at TIMESTAMP DEFAULT (datetime('now', '+8 hours')),
                FOREIGN KEY (project_id) REFERENCES video_projects(id) ON DELETE CASCADE
            )
            ''')
            
            # Create render_tasks table for managing video rendering tasks
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS render_tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER NOT NULL,
                novel_id TEXT,
                chapter_ids TEXT,
                title TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                progress REAL DEFAULT 0,
                priority INTEGER DEFAULT 1,
                created_at TIMESTAMP DEFAULT (datetime('now', '+8 hours')),
                started_at TIMESTAMP,
                completed_at TIMESTAMP,
                error_message TEXT,
                output_path TEXT,
                FOREIGN KEY (project_id) REFERENCES video_projects(id) ON DELETE CASCADE,
                FOREIGN KEY (novel_id) REFERENCES novels(id)
            )
            ''')
            
            # Create social_media_settings table for social media upload settings
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS social_media_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER NOT NULL UNIQUE,
                platform TEXT NOT NULL,  -- 'douyin', 'kuaishou', etc.
                enable_upload BOOLEAN DEFAULT 0,  -- 0: disabled, 1: enabled
                title TEXT,
                description TEXT,  -- 视频描述字段
                cookies TEXT,  -- cookies字段
                tags TEXT,
                custom_cover_path TEXT,
                title_on_cover BOOLEAN DEFAULT 1,
                title_font TEXT,
                title_size INTEGER DEFAULT 40,
                title_color TEXT DEFAULT '#FFFFFF',
                title_position TEXT DEFAULT 'top',
                title_x INTEGER,
                title_y INTEGER,
                show_chapter_info BOOLEAN DEFAULT 0,
                chapter_info_text TEXT,
                chapter_font TEXT,
                chapter_size INTEGER DEFAULT 30,
                chapter_color TEXT DEFAULT '#FFFFFF',
                chapter_position TEXT DEFAULT 'bottom',
                chapter_x INTEGER,
                chapter_y INTEGER,
                created_at TIMESTAMP DEFAULT (datetime('now', '+8 hours')),
                updated_at TIMESTAMP DEFAULT (datetime('now', '+8 hours')),
                FOREIGN KEY (project_id) REFERENCES video_projects(id) ON DELETE CASCADE
            )
            ''')
            
            # Create social_media_upload_history table for tracking upload history
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS social_media_upload_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER NOT NULL,
                platform TEXT NOT NULL,
                status TEXT DEFAULT 'pending',
                progress REAL DEFAULT 0,
                url TEXT,
                error_message TEXT,
                created_at TIMESTAMP DEFAULT (datetime('now', '+8 hours')),
                completed_at TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES video_projects(id) ON DELETE CASCADE
            )
            ''')
            
            # Create uploaded_videos table for storing user uploaded videos
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS uploaded_videos (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                filename TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                duration REAL,
                width INTEGER,
                height INTEGER,
                thumbnail_path TEXT,
                created_at TIMESTAMP DEFAULT (datetime('now', '+8 hours'))
            )
            ''')
            
            conn.commit()
            conn.close()
        
        self.execute_with_retry(_init)
    
    def run_migrations(self):
        """运行数据库迁移，确保旧数据库结构兼容新功能"""
        try:
            # 检查social_media_settings表是否存在description列
            self.check_and_add_description_column()
            
            # 检查scheduled_tasks表是否存在auto_generate_video列
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 检查列是否存在
            cursor.execute("PRAGMA table_info(scheduled_tasks)")
            columns = [column[1] for column in cursor.fetchall()]
            
            if 'auto_generate_video' not in columns:
                print("添加 auto_generate_video 列到 scheduled_tasks 表")
                cursor.execute('''
                ALTER TABLE scheduled_tasks 
                ADD COLUMN auto_generate_video INTEGER DEFAULT 1
                ''')
                conn.commit()
            
            # 检查render_tasks表是否存在novel_id和chapter_ids列
            cursor.execute("PRAGMA table_info(render_tasks)")
            render_columns = [column[1] for column in cursor.fetchall()]
            
            if 'novel_id' not in render_columns:
                print("添加 novel_id 列到 render_tasks 表")
                cursor.execute('''
                ALTER TABLE render_tasks 
                ADD COLUMN novel_id TEXT
                ''')
                conn.commit()
            
            if 'chapter_ids' not in render_columns:
                print("添加 chapter_ids 列到 render_tasks 表")
                cursor.execute('''
                ALTER TABLE render_tasks 
                ADD COLUMN chapter_ids TEXT
                ''')
                conn.commit()
            
            conn.close()
        except Exception as e:
            print(f"数据库迁移过程中出错: {e}")
    
    def check_and_add_description_column(self):
        """检查并添加description列到social_media_settings表"""
        def _check_and_add():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='social_media_settings'")
            if cursor.fetchone() is None:
                # 表不存在，无需迁移
                conn.close()
                return
            
            # 检查description列是否存在
            cursor.execute("PRAGMA table_info(social_media_settings)")
            columns = cursor.fetchall()
            has_description = any(column['name'] == 'description' for column in columns)
            
            if not has_description:
                print("检测到social_media_settings表缺少description列，正在添加...")
                try:
                    # 添加description列
                    cursor.execute("ALTER TABLE social_media_settings ADD COLUMN description TEXT")
                    conn.commit()
                    print("成功添加description列")
                except Exception as e:
                    print(f"添加description列失败: {e}")
                    conn.rollback()
            
            conn.close()
        
        self.execute_with_retry(_check_and_add)
    
    def create_novel_table(self, novel_id):
        """Create a table for a specific novel's chapters"""
        def _create():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Table name is novel_{novel_id}
            table_name = f"novel_{novel_id}"
            
            cursor.execute(f'''
            CREATE TABLE IF NOT EXISTS {table_name} (
                chapter_id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                order_index INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT (datetime('now', '+8 hours'))
            )
            ''')
            
            conn.commit()
            conn.close()
        
        self.execute_with_retry(_create)
    
    def novel_exists(self, novel_id):
        """Check if a novel already exists in the database"""
        def _check():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT 1 FROM novels WHERE id = ?", (novel_id,))
            exists = cursor.fetchone() is not None
            
            conn.close()
            return exists
        
        return self.execute_with_retry(_check)
    
    def save_novel(self, novel_data):
        """Save novel information to the database"""
        # 先检查小说是否已存在
        if self.novel_exists(novel_data['id']):
            return False
        
        def _save():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute(
                    "INSERT INTO novels (id, title, author, cover_url, source, description) VALUES (?, ?, ?, ?, ?, ?)",
                    (
                        novel_data['id'],
                        novel_data['title'],
                        novel_data.get('author', ''),
                        novel_data.get('cover_url', ''),
                        novel_data['source'],
                        novel_data.get('description', '')
                    )
                )
                
                conn.commit()
                
                # 创建小说章节表
                self.create_novel_table(novel_data['id'])
                
                return True
            except Exception as e:
                conn.rollback()
                print(f"Error saving novel: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_save)
    
    def save_chapter(self, novel_id, chapter_data):
        """Save a chapter to the novel's table"""
        def _save():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            table_name = f"novel_{novel_id}"
            
            try:
                # 使用REPLACE INTO，如果章节已存在则替换
                cursor.execute(
                    f"REPLACE INTO {table_name} (chapter_id, title, content, order_index) VALUES (?, ?, ?, ?)",
                    (
                        chapter_data['chapter_id'],
                        chapter_data['title'],
                        chapter_data['content'],
                        chapter_data['order_index']
                    )
                )
                
                conn.commit()
                return True
            except Exception as e:
                conn.rollback()
                print(f"Error saving chapter: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_save)
    
    def get_all_novels(self):
        """Get all novels from the database"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM novels ORDER BY created_at DESC")
            novels = [dict(row) for row in cursor.fetchall()]
            
            conn.close()
            return novels
        
        return self.execute_with_retry(_get)
    
    def get_novel(self, novel_id):
        """Get a novel's information"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM novels WHERE id = ?", (novel_id,))
            novel = cursor.fetchone()
            
            conn.close()
            
            if novel:
                return dict(novel)
            return None
        
        return self.execute_with_retry(_get)
    
    def get_chapters(self, novel_id):
        """Get all chapters for a novel"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            table_name = f"novel_{novel_id}"
            
            try:
                cursor.execute(f"SELECT chapter_id, title, order_index FROM {table_name} ORDER BY order_index")
                chapters = [dict(row) for row in cursor.fetchall()]
            except sqlite3.OperationalError:
                # Table doesn't exist
                chapters = []
            
            conn.close()
            return chapters
        
        return self.execute_with_retry(_get)
    
    def get_chapter(self, novel_id, chapter_id):
        """Get a specific chapter from a novel"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            table_name = f"novel_{novel_id}"
            
            try:
                cursor.execute(f"SELECT * FROM {table_name} WHERE chapter_id = ?", (chapter_id,))
                chapter = cursor.fetchone()
            except sqlite3.OperationalError:
                # Table doesn't exist
                chapter = None
            
            conn.close()
            
            if chapter:
                return dict(chapter)
            return None
        
        return self.execute_with_retry(_get)
    
    def get_max_chapter_id(self, novel_id):
        """Get the maximum chapter_id for a novel"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            table_name = f"novel_{novel_id}"
            
            try:
                cursor.execute(f"SELECT MAX(chapter_id) FROM {table_name}")
                result = cursor.fetchone()
                
                conn.close()
                
                if result and result[0]:
                    return result[0]
                return "0"  # Default to "0" if no chapters exist
            except sqlite3.OperationalError:
                # Table doesn't exist
                conn.close()
                return "0"
        
        return self.execute_with_retry(_get)
        
    # Methods for scheduled tasks
    def add_scheduled_task(self, novel_id, schedule_time, interval_seconds, auto_generate_video=True):
        """Add a new scheduled task for a novel"""
        def _add():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute(
                    "INSERT INTO scheduled_tasks (novel_id, schedule_time, interval_seconds, is_active, auto_generate_video) VALUES (?, ?, ?, 1, ?)",
                    (novel_id, schedule_time, interval_seconds, 1 if auto_generate_video else 0)
                )
                
                task_id = cursor.lastrowid
                conn.commit()
                return task_id
            except Exception as e:
                conn.rollback()
                print(f"Error adding scheduled task: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_add)
    
    def update_scheduled_task(self, task_id, schedule_time=None, interval_seconds=None, is_active=None, auto_generate_video=None):
        """Update a scheduled task"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            update_parts = []
            params = []
            
            if schedule_time is not None:
                update_parts.append("schedule_time = ?")
                params.append(schedule_time)
            
            if interval_seconds is not None:
                update_parts.append("interval_seconds = ?")
                params.append(interval_seconds)
            
            if is_active is not None:
                update_parts.append("is_active = ?")
                params.append(1 if is_active else 0)
            
            if auto_generate_video is not None:
                update_parts.append("auto_generate_video = ?")
                params.append(1 if auto_generate_video else 0)
            
            if not update_parts:
                return False
            
            # Add task_id to params
            params.append(task_id)
            
            # Execute update query
            cursor.execute(
                f"UPDATE scheduled_tasks SET {', '.join(update_parts)} WHERE id = ?", 
                params
            )
            
            conn.commit()
            conn.close()
            return True
        
        return self.execute_with_retry(_update)
    
    def delete_scheduled_task(self, task_id):
        """Delete a scheduled task"""
        def _delete():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute("DELETE FROM scheduled_tasks WHERE id = ?", (task_id,))
                
                conn.commit()
                return cursor.rowcount > 0
            except Exception as e:
                conn.rollback()
                print(f"Error deleting scheduled task: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_delete)
    
    def get_scheduled_task(self, task_id):
        """Get a specific scheduled task"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT st.*, n.title as novel_title, n.source as novel_source 
                FROM scheduled_tasks st
                JOIN novels n ON st.novel_id = n.id
                WHERE st.id = ?
            """, (task_id,))
            
            task = cursor.fetchone()
            
            conn.close()
            
            if task:
                return dict(task)
            return None
        
        return self.execute_with_retry(_get)
    
    def get_all_scheduled_tasks(self):
        """Get all scheduled tasks"""
        def _get():
            conn = self.get_connection()
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT st.*, n.title as novel_title, n.source as novel_source 
                FROM scheduled_tasks st
                JOIN novels n ON st.novel_id = n.id
                ORDER BY st.created_at DESC
            """)
            
            tasks = [dict(row) for row in cursor.fetchall()]
            
            conn.close()
            return tasks
        
        return self.execute_with_retry(_get)
    
    def get_active_scheduled_tasks(self):
        """Get all active scheduled tasks"""
        def _get():
            conn = self.get_connection()
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT st.*, n.title as novel_title, n.source as novel_source 
            FROM scheduled_tasks st
            JOIN novels n ON st.novel_id = n.id
            WHERE st.is_active = 1
            ''')
            
            tasks = [dict(row) for row in cursor.fetchall()]
            conn.close()
            return tasks
        
        return self.execute_with_retry(_get)
    
    def update_task_last_run_time(self, task_id, run_time):
        """Update the last run time of a scheduled task"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute(
                    "UPDATE scheduled_tasks SET last_run_time = ? WHERE id = ?",
                    (run_time, task_id)
                )
                
                conn.commit()
                return cursor.rowcount > 0
            except Exception as e:
                conn.rollback()
                print(f"Error updating task last run time: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_update)
    
    # Methods for settings
    def save_setting(self, key, value):
        """Save a setting to the database"""
        def _save():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute(
                    "REPLACE INTO settings (key, value, updated_at) VALUES (?, ?, datetime('now', '+8 hours'))",
                    (key, value)
                )
                
                conn.commit()
                return True
            except Exception as e:
                conn.rollback()
                print(f"Error saving setting: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_save)
    
    def get_setting(self, key, default=None):
        """Get a setting from the database"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT value FROM settings WHERE key = ?", (key,))
            result = cursor.fetchone()
            
            conn.close()
            
            if result:
                return result[0]
            return default
        
        return self.execute_with_retry(_get)
    
    def set_fanqie_cookie(self, cookie_json):
        """Store Fanqie cookie data"""
        return self.save_setting('fanqie_cookie', cookie_json)
    
    def get_fanqie_cookie(self):
        """Get Fanqie cookie data"""
        return self.get_setting('fanqie_cookie')
    
    def generate_unique_novel_id(self):
        """Generate a unique novel ID that doesn't conflict with existing ones"""
        import random
        import time
        
        def _generate():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # 生成基于时间戳和随机数的ID，确保唯一性
            base_id = int(time.time() * 1000) % 1000000  # 取时间戳的后6位
            
            for attempt in range(100):  # 最多尝试100次
                # 添加随机数确保唯一性
                novel_id = str(base_id + random.randint(1000, 9999))
                
                # 检查ID是否已存在
                cursor.execute("SELECT 1 FROM novels WHERE id = ?", (novel_id,))
                if cursor.fetchone() is None:
                    conn.close()
                    return novel_id
                
                # 如果ID已存在，增加基数重试
                base_id += random.randint(1, 100)
            
            conn.close()
            # 如果100次都失败，使用UUID的一部分
            import uuid
            return str(abs(hash(str(uuid.uuid4()))) % 1000000)
        
        return self.execute_with_retry(_generate)
    
    def create_manual_novel(self, title, author="", description=""):
        """Create a manual novel with generated ID"""
        novel_id = self.generate_unique_novel_id()
        
        novel_data = {
            'id': novel_id,
            'title': title,
            'author': author,
            'cover_url': '',
            'source': 'manual',
            'description': description
        }
        
        success = self.save_novel(novel_data)
        if success:
            return novel_id
        return None
    
    def update_novel_chapter_count(self, novel_id):
        """Update the chapter count for a novel"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            table_name = f"novel_{novel_id}"
            
            try:
                # Count chapters in the novel's table
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                chapter_count = cursor.fetchone()[0]
                
                # Update the novels table
                cursor.execute(
                    "UPDATE novels SET chapter_count = ? WHERE id = ?",
                    (chapter_count, novel_id)
                )
                
                conn.commit()
                return True
            except sqlite3.OperationalError:
                # Table doesn't exist
                conn.commit()
                return False
            except Exception as e:
                conn.rollback()
                print(f"Error updating chapter count: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_update)
    
    def delete_chapter(self, novel_id, chapter_id):
        """Delete a specific chapter from a novel"""
        def _delete():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            table_name = f"novel_{novel_id}"
            
            try:
                # Delete the chapter from the novel's table
                cursor.execute(f"DELETE FROM {table_name} WHERE chapter_id = ?", (chapter_id,))
                
                conn.commit()
                return cursor.rowcount > 0
            except Exception as e:
                conn.rollback()
                print(f"Error deleting chapter: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_delete)
    
    def delete_novel(self, novel_id):
        """Delete a novel and all its related data"""
        def _delete():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                # Delete the novel's chapter table
                table_name = f"novel_{novel_id}"
                cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
                
                # Delete related scheduled tasks
                cursor.execute("DELETE FROM scheduled_tasks WHERE novel_id = ?", (novel_id,))
                
                # Delete related video projects and their data
                cursor.execute("SELECT id FROM video_projects WHERE novel_id = ?", (novel_id,))
                project_ids = [row[0] for row in cursor.fetchall()]
                
                for project_id in project_ids:
                    # Delete video project chapters
                    cursor.execute("DELETE FROM video_project_chapters WHERE project_id = ?", (project_id,))
                    # Delete video settings
                    cursor.execute("DELETE FROM video_settings WHERE project_id = ?", (project_id,))
                    # Delete video renders
                    cursor.execute("DELETE FROM video_renders WHERE project_id = ?", (project_id,))
                    # Delete render tasks
                    cursor.execute("DELETE FROM render_tasks WHERE project_id = ?", (project_id,))
                    # Delete social media settings
                    cursor.execute("DELETE FROM social_media_settings WHERE project_id = ?", (project_id,))
                    # Delete social media upload history
                    cursor.execute("DELETE FROM social_media_upload_history WHERE project_id = ?", (project_id,))
                
                # Delete video projects
                cursor.execute("DELETE FROM video_projects WHERE novel_id = ?", (novel_id,))
                
                # Delete pending chapter updates
                cursor.execute("DELETE FROM pending_chapter_updates WHERE novel_id = ?", (novel_id,))
                
                # Finally, delete the novel itself
                cursor.execute("DELETE FROM novels WHERE id = ?", (novel_id,))
                
                conn.commit()
                return cursor.rowcount > 0
            except Exception as e:
                conn.rollback()
                print(f"Error deleting novel: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_delete)
    
    # Text replacement methods
    def add_text_replacement(self, original_text, replacement_text, project_id=None):
        """Add a new text replacement rule
        Args:
            original_text: The text to be replaced
            replacement_text: The text to replace with
            project_id: Optional video project ID this rule belongs to
        """
        def _add():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute(
                    "INSERT INTO text_replacements (original_text, replacement_text, project_id) VALUES (?, ?, ?)",
                    (original_text, replacement_text, project_id)
                )
                
                replacement_id = cursor.lastrowid
                conn.commit()
                return replacement_id
            except Exception as e:
                conn.rollback()
                print(f"Error adding text replacement: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_add)
    
    def get_all_text_replacements(self):
        """Get all text replacement rules"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM text_replacements ORDER BY id")
            replacements = [dict(row) for row in cursor.fetchall()]
            
            conn.close()
            return replacements
        
        return self.execute_with_retry(_get)
    def get_all_text_replacements_with_projectid(self,project_id):
        """Get all text replacement rules"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM text_replacements 
                WHERE project_id = ?
                ORDER BY id
            """, (project_id,))
            replacements = [dict(row) for row in cursor.fetchall()]
            
            conn.close()
            return replacements
        
        return self.execute_with_retry(_get)
    
    def update_text_replacement(self, replacement_id, original_text, replacement_text):
        """Update an existing text replacement rule"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute(
                    "UPDATE text_replacements SET original_text = ?, replacement_text = ? WHERE id = ?",
                    (original_text, replacement_text, replacement_id)
                )
                
                conn.commit()
                return cursor.rowcount > 0
            except Exception as e:
                conn.rollback()
                print(f"Error updating text replacement: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_update)
    
    def delete_text_replacement(self, replacement_id):
        """Delete a text replacement rule"""
        def _delete():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute("DELETE FROM text_replacements WHERE id = ?", (replacement_id,))
                
                conn.commit()
                return cursor.rowcount > 0
            except Exception as e:
                conn.rollback()
                print(f"Error deleting text replacement: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_delete)
    
    # TTS Voice methods
    def add_tts_voice(self, voice_id, display_name, language, gender=None):
        """Add a new TTS voice"""
        def _add():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                # 首先检查是否已存在该voice_id
                cursor.execute("SELECT 1 FROM tts_voices WHERE voice_id = ?", (voice_id,))
                if cursor.fetchone():
                    # 语音已存在，更新信息
                    cursor.execute(
                        "UPDATE tts_voices SET display_name = ?, language = ?, gender = ? WHERE voice_id = ?",
                        (display_name, language, gender, voice_id)
                    )
                    conn.commit()
                    # 返回0表示已存在但更新了信息
                    return 0
                
                # 如果不存在，创建新记录
                cursor.execute(
                    "INSERT INTO tts_voices (voice_id, display_name, language, gender) VALUES (?, ?, ?, ?)",
                    (voice_id, display_name, language, gender)
                )
                
                new_id = cursor.lastrowid
                conn.commit()
                # 返回新ID表示添加成功
                return new_id
            except Exception as e:
                conn.rollback()
                print(f"Error adding TTS voice: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_add)
    
    def get_all_tts_voices(self):
        """Get all available TTS voices"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM tts_voices ORDER BY is_favorite DESC, language, display_name")
            voices = [dict(row) for row in cursor.fetchall()]
            
            conn.close()
            return voices
        
        return self.execute_with_retry(_get)
    
    def set_voice_favorite(self, voice_id, is_favorite):
        """Set a voice as favorite"""
        def _set():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute(
                    "UPDATE tts_voices SET is_favorite = ? WHERE voice_id = ?",
                    (1 if is_favorite else 0, voice_id)
                )
                
                conn.commit()
                return cursor.rowcount > 0
            except Exception as e:
                conn.rollback()
                print(f"Error setting voice favorite: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_set)
    
    # Video Project methods
    def create_video_project(self, title, novel_id=None):
        """Create a new video project"""
        def _create():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                now = time.strftime('%Y-%m-%d %H:%M:%S')
                cursor.execute(
                    "INSERT INTO video_projects (title, novel_id, created_at, updated_at) VALUES (?, ?, ?, ?)",
                    (title, novel_id, now, now)
                )
                
                project_id = cursor.lastrowid
                
                # Create default video settings for this project
                cursor.execute(
                    "INSERT INTO video_settings (project_id, voice_id, created_at, updated_at) VALUES (?, ?, ?, ?)",
                    (project_id, 'zh-CN-XiaoxiaoNeural', now, now)
                )
                
                conn.commit()
                return project_id
            except Exception as e:
                conn.rollback()
                print(f"Error creating video project: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_create)
    
    def add_chapters_to_project(self, project_id, novel_id, chapter_ids):
        """Add chapters to a video project"""
        def _add():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                # Get the current highest order_index
                cursor.execute(
                    "SELECT MAX(order_index) FROM video_project_chapters WHERE project_id = ?",
                    (project_id,)
                )
                result = cursor.fetchone()
                next_order = (result[0] or 0) + 1
                
                for chapter_id in chapter_ids:
                    cursor.execute(
                        "INSERT INTO video_project_chapters (project_id, novel_id, chapter_id, order_index) VALUES (?, ?, ?, ?)",
                        (project_id, novel_id, chapter_id, next_order)
                    )
                    next_order += 1
                
                # Update project timestamp
                now = time.strftime('%Y-%m-%d %H:%M:%S')
                cursor.execute(
                    "UPDATE video_projects SET updated_at = ? WHERE id = ?",
                    (now, project_id)
                )
                
                conn.commit()
                return True
            except Exception as e:
                conn.rollback()
                print(f"Error adding chapters to project: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_add)
    
    def get_project_chapters(self, project_id):
        """Get all chapters in a video project with their content"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute("""
                    SELECT vpc.id, vpc.novel_id, vpc.chapter_id, vpc.order_index, 
                           n.title as novel_title
                    FROM video_project_chapters vpc
                    JOIN novels n ON vpc.novel_id = n.id
                    WHERE vpc.project_id = ?
                    ORDER BY vpc.order_index
                """, (project_id,))
                
                chapters = []
                for row in cursor.fetchall():
                    chapter_data = dict(row)
                    # Get the actual chapter content
                    novel_id = chapter_data['novel_id']
                    chapter_id = chapter_data['chapter_id']
                    chapter = self.get_chapter(novel_id, chapter_id)
                    if chapter:
                        chapter_data['title'] = chapter['title']
                        chapter_data['content'] = chapter['content']
                    chapters.append(chapter_data)
                
                conn.close()
                return chapters
            except Exception as e:
                print(f"Error getting project chapters: {e}")
                conn.close()
                raise
        
        return self.execute_with_retry(_get)
    
    def update_video_settings(self, project_id, settings):
        """Update video generation settings for a project"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Get the existing settings
            cursor.execute("SELECT * FROM video_settings WHERE project_id = ?", (project_id,))
            current_settings = cursor.fetchone()
            
            if not current_settings:
                conn.close()
                return False
            
            # Prepare the update
            fields = []
            values = []
            
            for key, value in settings.items():
                if key in current_settings.keys() and key != 'project_id' and key != 'id':
                    fields.append(f"{key} = ?")
                    values.append(value)
            
            if not fields:
                conn.close()
                return False
            
            # Add updated_at timestamp
            fields.append("updated_at = ?")
            values.append(time.strftime('%Y-%m-%d %H:%M:%S'))
            values.append(project_id)
            
            try:
                query = f"UPDATE video_settings SET {', '.join(fields)} WHERE project_id = ?"
                cursor.execute(query, values)
                
                conn.commit()
                return cursor.rowcount > 0
            except Exception as e:
                conn.rollback()
                print(f"Error updating video settings: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_update)
    
    def get_video_settings(self, project_id):
        """Get video generation settings for a project"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM video_settings WHERE project_id = ?", (project_id,))
            settings = cursor.fetchone()
            
            conn.close()
            return dict(settings) if settings else None
        
        return self.execute_with_retry(_get)
    
    def create_render_job(self, project_id):
        """Create a new video render job"""
        def _create():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                now = time.strftime('%Y-%m-%d %H:%M:%S')
                cursor.execute(
                    "INSERT INTO video_renders (project_id, start_time, status, created_at) VALUES (?, ?, ?, ?)",
                    (project_id, now, 'processing', now)
                )
                
                render_id = cursor.lastrowid
                
                # Update project status
                cursor.execute(
                    "UPDATE video_projects SET status = ?, updated_at = ? WHERE id = ?",
                    ('rendering', now, project_id)
                )
                
                conn.commit()
                return render_id
            except Exception as e:
                conn.rollback()
                print(f"Error creating render job: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_create)
    
    def update_render_progress(self, render_id, progress, status=None, error_message=None, output_path=None):
        """Update the progress of a render job"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                fields = ["progress = ?"]
                values = [progress]
                
                if status:
                    fields.append("status = ?")
                    values.append(status)
                    
                    # If status is 'completed' or 'failed', set end_time
                    if status in ('completed', 'failed'):
                        fields.append("end_time = ?")
                        values.append(time.strftime('%Y-%m-%d %H:%M:%S'))
                
                if error_message:
                    fields.append("error_message = ?")
                    values.append(error_message)
                
                if output_path:
                    fields.append("output_path = ?")
                    values.append(output_path)
                
                query = f"UPDATE video_renders SET {', '.join(fields)} WHERE id = ?"
                values.append(render_id)
                
                cursor.execute(query, values)
                
                # If rendering is complete, update the project status
                if status in ('completed', 'failed'):
                    # Get the project_id first
                    cursor.execute("SELECT project_id FROM video_renders WHERE id = ?", (render_id,))
                    result = cursor.fetchone()
                    if result:
                        project_id = result['project_id']
                        project_status = 'completed' if status == 'completed' else 'failed'
                        
                        cursor.execute(
                            "UPDATE video_projects SET status = ?, updated_at = ? WHERE id = ?",
                            (project_status, time.strftime('%Y-%m-%d %H:%M:%S'), project_id)
                        )
                
                conn.commit()
                return cursor.rowcount > 0
            except Exception as e:
                conn.rollback()
                print(f"Error updating render progress: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_update)
    
    def get_render_status(self, render_id):
        """Get the status of a render job"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM video_renders WHERE id = ?", (render_id,))
            render = cursor.fetchone()
            
            conn.close()
            return dict(render) if render else None
        
        return self.execute_with_retry(_get)
    
    # Video project related methods
    def get_all_video_projects(self):
        """Get all video projects"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT * FROM video_projects 
            ORDER BY updated_at DESC
            ''')
            
            projects = [dict(row) for row in cursor.fetchall()]
            conn.close()
            return projects
        
        return self.execute_with_retry(_get)
    
    def get_video_project(self, project_id):
        """Get a specific video project"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT * FROM video_projects 
            WHERE id = ?
            ''', (project_id,))
            
            project = cursor.fetchone()
            conn.close()
            
            if project:
                return dict(project)
            return None
        
        return self.execute_with_retry(_get)
    
    def delete_video_project(self, project_id):
        """Delete a video project and its associated data"""
        def _delete():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Delete project settings and chapters (cascade will handle this)
            cursor.execute('''
            DELETE FROM video_projects 
            WHERE id = ?
            ''', (project_id,))
            
            conn.commit()
            conn.close()
        
        return self.execute_with_retry(_delete)
    
    def clear_project_chapters(self, project_id):
        """Clear all chapters from a project"""
        def _clear():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            DELETE FROM video_project_chapters 
            WHERE project_id = ?
            ''', (project_id,))
            
            conn.commit()
            conn.close()
        
        return self.execute_with_retry(_clear)
    
    def get_project_chapters(self, project_id):
        """Get chapters in a video project"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT vpc.*, n.title as novel_title 
            FROM video_project_chapters vpc
            JOIN novels n ON vpc.novel_id = n.id
            WHERE vpc.project_id = ?
            ORDER BY vpc.order_index ASC
            ''', (project_id,))
            
            chapters = [dict(row) for row in cursor.fetchall()]
            
            # Add chapter titles
            for chapter in chapters:
                novel_id = chapter['novel_id']
                chapter_id = chapter['chapter_id']
                
                table_name = f"novel_{novel_id}"
                try:
                    cursor.execute(f'''
                    SELECT title FROM {table_name}
                    WHERE chapter_id = ?
                    ''', (chapter_id,))
                    
                    result = cursor.fetchone()
                    if result:
                        chapter['chapter_title'] = result['title']
                    else:
                        chapter['chapter_title'] = "未知章节"
                except:
                    chapter['chapter_title'] = "未知章节"
            
            conn.close()
            return chapters
        
        return self.execute_with_retry(_get)
    
    def update_video_settings(self, project_id, settings):
        """Update settings for a video project"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Check if settings exist
            cursor.execute('''
            SELECT id FROM video_settings
            WHERE project_id = ?
            ''', (project_id,))
            
            settings_exist = cursor.fetchone() is not None
            
            if settings_exist:
                # Build the update query dynamically based on the provided settings
                set_clauses = []
                values = []
                
                for key, value in settings.items():
                    set_clauses.append(f"{key} = ?")
                    values.append(value)
                
                set_clauses.append("updated_at = datetime('now', '+8 hours')")
                set_clause = ", ".join(set_clauses)
                
                # Add project_id to values
                values.append(project_id)
                
                cursor.execute(f'''
                UPDATE video_settings
                SET {set_clause}
                WHERE project_id = ?
                ''', values)
            else:
                # Create new settings record
                columns = ["project_id"]
                placeholders = ["?"]
                values = [project_id]
                
                for key, value in settings.items():
                    columns.append(key)
                    placeholders.append("?")
                    values.append(value)
                
                column_str = ", ".join(columns)
                placeholder_str = ", ".join(placeholders)
                
                cursor.execute(f'''
                INSERT INTO video_settings ({column_str})
                VALUES ({placeholder_str})
                ''', values)
            
            # Update project's updated_at timestamp
            cursor.execute('''
            UPDATE video_projects
            SET updated_at = datetime('now', '+8 hours')
            WHERE id = ?
            ''', (project_id,))
            
            conn.commit()
            conn.close()
        
        return self.execute_with_retry(_update)
    
    def get_video_settings(self, project_id):
        """Get settings for a video project"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT * FROM video_settings
            WHERE project_id = ?
            ''', (project_id,))
            
            settings = cursor.fetchone()
            conn.close()
            
            if settings:
                return dict(settings)
            return None
        
        return self.execute_with_retry(_get)
    
    def create_render_job(self, project_id):
        """Create a new render job and return the render ID"""
        def _create():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            INSERT INTO video_renders (project_id, start_time, status, progress)
            VALUES (?, datetime('now', '+8 hours'), 'pending', 0)
            ''', (project_id,))
            
            # Get the ID of the inserted render
            render_id = cursor.lastrowid
            
            # Update project's updated_at timestamp
            cursor.execute('''
            UPDATE video_projects
            SET updated_at = datetime('now', '+8 hours')
            WHERE id = ?
            ''', (project_id,))
            
            conn.commit()
            conn.close()
            
            return render_id
        
        return self.execute_with_retry(_create)
    
    def update_render_progress(self, render_id, progress, status=None, error_message=None, output_path=None):
        """Update render progress and status"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            update_fields = ["progress = ?"]
            update_values = [progress]
            
            if status:
                update_fields.append("status = ?")
                update_values.append(status)
                
                # If status is completed, set end_time
                if status == 'completed':
                    update_fields.append("end_time = datetime('now', '+8 hours')")
            
            if error_message is not None:
                update_fields.append("error_message = ?")
                update_values.append(error_message)
            
            if output_path is not None:
                update_fields.append("output_path = ?")
                update_values.append(output_path)
            
            # Add render_id to values
            update_values.append(render_id)
            
            update_clause = ", ".join(update_fields)
            
            cursor.execute(f'''
            UPDATE video_renders
            SET {update_clause}
            WHERE id = ?
            ''', update_values)
            
            # Get project_id for this render
            cursor.execute('''
            SELECT project_id FROM video_renders
            WHERE id = ?
            ''', (render_id,))
            
            result = cursor.fetchone()
            if result:
                project_id = result['project_id']
                
                # Update project status based on render status
                if status == 'in_progress':
                    cursor.execute('''
                    UPDATE video_projects
                    SET status = 'in_progress', updated_at = datetime('now', '+8 hours')
                    WHERE id = ?
                    ''', (project_id,))
                elif status == 'completed':
                    cursor.execute('''
                    UPDATE video_projects
                    SET status = 'completed', updated_at = datetime('now', '+8 hours')
                    WHERE id = ?
                    ''', (project_id,))
            
            conn.commit()
            conn.close()
        
        return self.execute_with_retry(_update)
    
    def get_render_status(self, render_id):
        """Get the status of a render job"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT * FROM video_renders
            WHERE id = ?
            ''', (render_id,))
            
            render = cursor.fetchone()
            conn.close()
            
            if render:
                return dict(render)
            return None
        
        return self.execute_with_retry(_get)
    
    def get_render_history(self, project_id):
        """Get render history for a project"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT * FROM video_renders
            WHERE project_id = ?
            ORDER BY created_at DESC
            ''', (project_id,))
            
            renders = [dict(row) for row in cursor.fetchall()]
            conn.close()
            
            return renders
        
        return self.execute_with_retry(_get)
    
    # Task management methods
    def create_render_task(self, project_id, title, novel_id=None, chapter_ids=None):
        """Create a new render task for a project with novel and chapter information"""
        def _create():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                # 使用局部变量避免参数覆盖问题
                task_novel_id = novel_id
                task_chapter_ids = chapter_ids
                
                # 如果没有提供novel_id和chapter_ids，从project中获取
                if task_novel_id is None or task_chapter_ids is None:
                    # 从video_project_chapters表中获取项目的章节信息
                    cursor.execute('''
                    SELECT DISTINCT vpc.novel_id, GROUP_CONCAT(vpc.chapter_id) as chapter_ids
                    FROM video_project_chapters vpc
                    WHERE vpc.project_id = ?
                    GROUP BY vpc.novel_id
                    ''', (project_id,))
                    
                    project_chapters = cursor.fetchall()
                    if project_chapters:
                        # 如果项目有多个小说，取第一个
                        task_novel_id = project_chapters[0]['novel_id']
                        task_chapter_ids = project_chapters[0]['chapter_ids']
                
                # 将chapter_ids转换为JSON字符串存储
                chapter_ids_json = json.dumps(task_chapter_ids.split(',') if isinstance(task_chapter_ids, str) else task_chapter_ids) if task_chapter_ids else None
                
                cursor.execute('''
                INSERT INTO render_tasks (project_id, novel_id, chapter_ids, title, status, created_at)
                VALUES (?, ?, ?, ?, 'pending', datetime('now', '+8 hours'))
                ''', (project_id, task_novel_id, chapter_ids_json, title))
                
                task_id = cursor.lastrowid
                conn.commit()
                return task_id
            except Exception as e:
                conn.rollback()
                print(f"Error creating render task: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_create)
    
    def get_all_render_tasks(self):
        """Get all render tasks with chapter range information"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT rt.*, vp.title as project_title, n.title as novel_title
            FROM render_tasks rt
            JOIN video_projects vp ON rt.project_id = vp.id
            LEFT JOIN novels n ON rt.novel_id = n.id
            ORDER BY rt.priority DESC, rt.created_at DESC
            ''')
            
            tasks = [dict(row) for row in cursor.fetchall()]
            
            # 为每个任务获取章节范围信息
            for task in tasks:
                try:
                    # 优先使用存储的章节ID信息
                    if task['novel_id'] and task['chapter_ids']:
                        chapter_ids = json.loads(task['chapter_ids'])
                        novel_id = task['novel_id']
                        
                        # 从小说表中获取章节标题
                        chapter_titles = []
                        table_name = f"novel_{novel_id}"
                        
                        try:
                            for chapter_id in chapter_ids:
                                cursor.execute(f'''
                                SELECT title FROM {table_name} 
                                WHERE chapter_id = ?
                                ''', (chapter_id,))
                                
                                chapter_detail = cursor.fetchone()
                                if chapter_detail:
                                    chapter_titles.append({'title': chapter_detail['title']})
                        except sqlite3.OperationalError:
                            # 表不存在，回退到项目章节查询
                            chapter_titles = []
                        
                        if chapter_titles:
                            task['chapter_range'] = self._extract_chapter_range_db(chapter_titles)
                        else:
                            # 回退到从项目章节获取
                            task['chapter_range'] = self._get_chapter_range_from_project(cursor, task['project_id'])
                    else:
                        # 回退到从项目章节获取
                        task['chapter_range'] = self._get_chapter_range_from_project(cursor, task['project_id'])
                        
                except Exception as e:
                    task['chapter_range'] = f"获取出错: {str(e)[:50]}"
            
            conn.close()
            return tasks
        
        return self.execute_with_retry(_get)
    
    def _get_chapter_range_from_project(self, cursor, project_id):
        """从项目章节中获取章节范围（回退方法）"""
        try:
            cursor.execute('''
            SELECT vpc.chapter_id, vpc.novel_id, n.title as novel_title 
            FROM video_project_chapters vpc
            JOIN novels n ON vpc.novel_id = n.id
            WHERE vpc.project_id = ?
            ORDER BY vpc.order_index ASC
            ''', (project_id,))
            
            chapters = cursor.fetchall()
            
            if chapters:
                # 获取章节标题列表
                chapter_titles = []
                for chapter in chapters:
                    novel_id = chapter['novel_id']
                    chapter_id = chapter['chapter_id']
                    
                    # 从对应的小说表中获取章节详细信息
                    table_name = f"novel_{novel_id}"
                    try:
                        cursor.execute(f'''
                        SELECT title FROM {table_name} 
                        WHERE chapter_id = ?
                        ''', (chapter_id,))
                        
                        chapter_detail = cursor.fetchone()
                        if chapter_detail:
                            chapter_titles.append({'title': chapter_detail['title']})
                    except sqlite3.OperationalError:
                        # 表不存在或查询出错，跳过这个章节
                        continue
                
                # 使用extract_chapter_range函数计算章节范围
                if chapter_titles:
                    return self._extract_chapter_range_db(chapter_titles)
                else:
                    return "无章节标题"
            else:
                return "无章节关联"
        except Exception as e:
            return f"获取出错: {str(e)[:50]}"
    
    def get_render_task(self, task_id):
        """Get a specific render task with its chapter information"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT rt.*, vp.title as project_title, n.title as novel_title
            FROM render_tasks rt
            JOIN video_projects vp ON rt.project_id = vp.id
            LEFT JOIN novels n ON rt.novel_id = n.id
            WHERE rt.id = ?
            ''', (task_id,))
            
            task = cursor.fetchone()
            if not task:
                conn.close()
                return None
            
            task = dict(task)
            
            # 获取章节信息
            try:
                if task['novel_id'] and task['chapter_ids']:
                    chapter_ids = json.loads(task['chapter_ids'])
                    novel_id = task['novel_id']
                    
                    # 获取章节详细信息
                    chapters = []
                    table_name = f"novel_{novel_id}"
                    
                    try:
                        for chapter_id in chapter_ids:
                            cursor.execute(f'''
                            SELECT chapter_id, title, content FROM {table_name} 
                            WHERE chapter_id = ?
                            ''', (chapter_id,))
                            
                            chapter_detail = cursor.fetchone()
                            if chapter_detail:
                                chapters.append(dict(chapter_detail))
                    except sqlite3.OperationalError:
                        # 表不存在，从项目章节获取
                        chapters = []
                    
                    task['chapters'] = chapters
                    
                    # 计算章节范围
                    if chapters:
                        chapter_titles = [{'title': ch['title']} for ch in chapters]
                        task['chapter_range'] = self._extract_chapter_range_db(chapter_titles)
                    else:
                        task['chapter_range'] = self._get_chapter_range_from_project(cursor, task['project_id'])
                else:
                    # 从项目章节获取
                    task['chapter_range'] = self._get_chapter_range_from_project(cursor, task['project_id'])
                    task['chapters'] = []
                    
            except Exception as e:
                task['chapter_range'] = f"获取出错: {str(e)[:50]}"
                task['chapters'] = []
            
            conn.close()
            return task
        
        return self.execute_with_retry(_get)
    
    def update_render_task_status(self, task_id, status, progress=None, error_message=None, output_path=None):
        """Update render task status and progress"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                fields = ["status = ?"]
                values = [status]
                
                if progress is not None:
                    fields.append("progress = ?")
                    values.append(progress)
                
                if error_message is not None:
                    fields.append("error_message = ?")
                    values.append(error_message)
                
                if output_path is not None:
                    fields.append("output_path = ?")
                    values.append(output_path)
                
                # 设置时间戳
                if status == 'processing':
                    fields.append("started_at = ?")
                    values.append(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                elif status in ['completed', 'failed']:
                    fields.append("completed_at = ?")
                    values.append(datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
                
                values.append(task_id)
                
                query = f"UPDATE render_tasks SET {', '.join(fields)} WHERE id = ?"
                cursor.execute(query, values)
                
                conn.commit()
                return cursor.rowcount > 0
            except Exception as e:
                conn.rollback()
                print(f"Error updating render task status: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_update)
    
    def delete_render_task(self, task_id):
        """Delete a render task"""
        def _delete():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute("DELETE FROM render_tasks WHERE id = ?", (task_id,))
                
                conn.commit()
                return cursor.rowcount > 0
            except Exception as e:
                conn.rollback()
                print(f"Error deleting render task: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_delete)
    
    def get_pending_render_tasks(self):
        """Get all pending render tasks"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT rt.*, vp.title as project_title, n.title as novel_title
            FROM render_tasks rt
            JOIN video_projects vp ON rt.project_id = vp.id
            LEFT JOIN novels n ON rt.novel_id = n.id
            WHERE rt.status = 'pending'
            ORDER BY rt.priority DESC, rt.created_at ASC
            ''')
            
            tasks = [dict(row) for row in cursor.fetchall()]
            conn.close()
            return tasks
        
        return self.execute_with_retry(_get)
    
    def _extract_chapter_range_db(self, chapter_texts):
        """
        从章节数据中提取章节范围 (数据库版本)
        """
        import re
        
        chapter_numbers = []
        
        # 处理输入数据
        if isinstance(chapter_texts, dict):
            titles = [chapter_texts.get('title', '')]
        elif isinstance(chapter_texts, list):
            titles = [item.get('title', '') if isinstance(item, dict) else str(item) for item in chapter_texts]
        else:
            return "无法解析章节信息"
        
        # 优化的中文数字转换函数
        def chinese_to_number(chinese_num):
            chinese_digits = {
                '零': 0, '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
                '六': 6, '七': 7, '八': 8, '九': 9,
                '壹': 1, '贰': 2, '叁': 3, '肆': 4, '伍': 5,
                '陆': 6, '柒': 7, '捌': 8, '玖': 9,
            }
            
            units = {
                '十': 10, '拾': 10,
                '百': 100, '佰': 100,
                '千': 1000, '仟': 1000,
                '万': 10000, '萬': 10000,
            }
            
            chinese_num = chinese_num.strip()
            if not chinese_num:
                return None
            
            if chinese_num in ['十', '拾']:
                return 10
            
            if chinese_num.startswith(('十', '拾')):
                if len(chinese_num) == 1:
                    return 10
                remainder = chinese_num[1:]
                if remainder in chinese_digits:
                    return 10 + chinese_digits[remainder]
            
            result = 0
            temp_num = 0
            
            i = 0
            while i < len(chinese_num):
                char = chinese_num[i]
                
                if char in chinese_digits:
                    temp_num = chinese_digits[char]
                elif char in units:
                    unit_value = units[char]
                    if unit_value == 10:
                        if temp_num == 0:
                            temp_num = 1
                        result += temp_num * unit_value
                        temp_num = 0
                    elif unit_value == 100:
                        if temp_num == 0:
                            temp_num = 1
                        result += temp_num * unit_value
                        temp_num = 0
                    elif unit_value == 1000:
                        if temp_num == 0:
                            temp_num = 1
                        result += temp_num * unit_value
                        temp_num = 0
                    elif unit_value == 10000:
                        if temp_num == 0:
                            temp_num = result if result > 0 else 1
                        else:
                            temp_num = result + temp_num
                        result = temp_num * unit_value
                        temp_num = 0
                
                i += 1
            
            result += temp_num
            return result if result > 0 else None
        
        # 提取所有章节号
        for title in titles:
            if title:
                # 匹配阿拉伯数字，允许空格
                matches = re.findall(r'第\s*(\d+)\s*章', title)
                for match in matches:
                    chapter_numbers.append(int(match))
                
                # 匹配中文数字
                chinese_pattern = r'第([零一二三四五六七八九十壹贰叁肆伍陆柒捌玖拾百佰千仟万萬]+)章'
                chinese_matches = re.findall(chinese_pattern, title)
                for match in chinese_matches:
                    num = chinese_to_number(match)
                    if num is not None:
                        chapter_numbers.append(num)
        
        # 如果没有找到章节号
        if not chapter_numbers:
            return "未找到章节信息"
        
        # 去重并排序
        chapter_numbers = sorted(set(chapter_numbers))
        
        # 生成范围字符串
        if len(chapter_numbers) == 1:
            return f"第{chapter_numbers[0]}章"
        else:
            return f"第{chapter_numbers[0]}章 ~ 第{chapter_numbers[-1]}章"
    
    def get_render_task(self, task_id):
        """Get details of a specific render task"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT rt.*, vp.title as project_title 
            FROM render_tasks rt
            JOIN video_projects vp ON rt.project_id = vp.id
            WHERE rt.id = ?
            ''', (task_id,))
            
            task = cursor.fetchone()
            
            if task:
                task_dict = dict(task)
                
                # Get chapters for this task's project
                project_id = task_dict['project_id']
                cursor.execute('''
                SELECT vpc.*, n.title as novel_title 
                FROM video_project_chapters vpc
                JOIN novels n ON vpc.novel_id = n.id
                WHERE vpc.project_id = ?
                ORDER BY vpc.order_index ASC
                ''', (project_id,))
                
                chapters = [dict(row) for row in cursor.fetchall()]
                task_dict['chapters'] = chapters
                
                conn.close()
                return task_dict
            
            conn.close()
            return None
        
        return self.execute_with_retry(_get)
    
    def update_task_status(self, task_id, status, progress=None, error_message=None, output_path=None):
        """Update status and progress of a render task"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            update_fields = ["status = ?"]
            update_values = [status]
            
            if progress is not None:
                update_fields.append("progress = ?")
                update_values.append(progress)
            
            if error_message is not None:
                update_fields.append("error_message = ?")
                update_values.append(error_message)
            
            if output_path is not None:
                update_fields.append("output_path = ?")
                update_values.append(output_path)
            
            # Update timestamps based on status
            if status == 'processing':
                update_fields.append("started_at = datetime('now', '+8 hours')")
            elif status in ('completed', 'failed'):
                update_fields.append("completed_at = datetime('now', '+8 hours')")
            
            # Add task_id to values
            update_values.append(task_id)
            
            query = f'''
            UPDATE render_tasks 
            SET {", ".join(update_fields)}
            WHERE id = ?
            '''
            
            cursor.execute(query, update_values)
            
            conn.commit()
            conn.close()
            return True
        
        return self.execute_with_retry(_update)
    
    def delete_render_task(self, task_id):
        """Delete a render task"""
        def _delete():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            DELETE FROM render_tasks
            WHERE id = ?
            ''', (task_id,))
            
            conn.commit()
            conn.close()
            return True
        
        return self.execute_with_retry(_delete)
    
    def update_task_priority(self, task_id, priority):
        """Update the priority of a render task"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            UPDATE render_tasks
            SET priority = ?
            WHERE id = ?
            ''', (priority, task_id))
            
            conn.commit()
            conn.close()
            return True
        
        return self.execute_with_retry(_update)
    
    def get_pending_tasks(self):
        """Get all pending tasks ordered by priority"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute('''
            SELECT rt.*, vp.title as project_title 
            FROM render_tasks rt
            JOIN video_projects vp ON rt.project_id = vp.id
            WHERE rt.status = 'pending'
            ORDER BY rt.priority DESC, rt.created_at ASC
            ''')
            
            tasks = [dict(row) for row in cursor.fetchall()]
            conn.close()
            return tasks
        
        return self.execute_with_retry(_get)
    
    def get_processing_tasks(self):
        """Get all tasks with 'processing' status"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM render_tasks 
                WHERE status = 'processing'
                ORDER BY priority DESC, created_at ASC
            """)
            tasks = [dict(row) for row in cursor.fetchall()]
            
            conn.close()
            return tasks
        
        return self.execute_with_retry(_get)
    
    def update_social_media_settings(self, project_id, platform, settings):
        """Update social media upload settings for a project"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # Check if settings exist for this project and platform
            cursor.execute("""
                SELECT id FROM social_media_settings 
                WHERE project_id = ? AND platform = ?
            """, (project_id, platform))
            
            settings_exist = cursor.fetchone()
            
            if settings_exist:
                # Update existing settings
                set_clauses = []
                params = []
                
                for key, value in settings.items():
                    set_clauses.append(f"{key} = ?")
                    params.append(value)
                
                set_clauses.append("updated_at = datetime('now', '+8 hours')")
                
                query = f"""
                    UPDATE social_media_settings 
                    SET {', '.join(set_clauses)} 
                    WHERE project_id = ? AND platform = ?
                """
                params.extend([project_id, platform])
                
                cursor.execute(query, params)
            else:
                # Insert new settings
                settings['project_id'] = project_id
                settings['platform'] = platform
                
                columns = list(settings.keys())
                placeholders = ['?'] * len(columns)
                values = [settings[col] for col in columns]
                
                query = f"""
                    INSERT INTO social_media_settings 
                    ({', '.join(columns)}) 
                    VALUES ({', '.join(placeholders)})
                """
                
                cursor.execute(query, values)
            
            conn.commit()
            conn.close()
            return True
        
        return self.execute_with_retry(_update)
    
    def get_social_media_settings(self, project_id, platform='douyin'):
        """Get social media upload settings for a project"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM social_media_settings 
                WHERE project_id = ? AND platform = ?
            """, (project_id, platform))
            
            row = cursor.fetchone()
            settings = dict(row) if row else None
            
            conn.close()
            return settings
        
        return self.execute_with_retry(_get)
    
    def update_social_media_settings_cookies(self, project_id,cookiestring=None):
        """Update social media settings for a project"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            # cursor.execute("""
            #     UPDATE social_media_settings 
            #     SET updated_at = CURRENT_TIMESTAMP 
            #     WHERE project_id = ? AND platform = ?
            # """, (project_id, platform))
            cursor.execute("UPDATE social_media_settings SET cookies = ? WHERE project_id = ? AND platform = 'douyin'",(cookiestring, project_id))
            
            conn.commit()
            conn.close()
        
        return self.execute_with_retry(_update)
    
    def add_upload_history(self, project_id, platform, status='pending'):
        """Add a new upload history entry"""
        def _add():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO social_media_upload_history 
                (project_id, platform, status) 
                VALUES (?, ?, ?)
            """, (project_id, platform, status))
            
            upload_id = cursor.lastrowid
            conn.commit()
            conn.close()
            return upload_id
        
        return self.execute_with_retry(_add)
    
    def update_upload_progress(self, upload_id, progress, status=None, error_message=None, url=None):
        """Update upload progress"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            set_clauses = ["progress = ?", "updated_at = datetime('now', '+8 hours')"]
            params = [progress]
            
            if status:
                set_clauses.append("status = ?")
                params.append(status)
            
            if error_message:
                set_clauses.append("error_message = ?")
                params.append(error_message)
            
            if url:
                set_clauses.append("url = ?")
                params.append(url)
            
            query = f"""
                UPDATE social_media_upload_history 
                SET {', '.join(set_clauses)} 
                WHERE id = ?
            """
            params.append(upload_id)
            
            cursor.execute(query, params)
            conn.commit()
            conn.close()
            return True
        
        return self.execute_with_retry(_update)
    
    def get_upload_history(self, project_id):
        """Get upload history for a project"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM social_media_upload_history 
                WHERE project_id = ? 
                ORDER BY created_at DESC
            """, (project_id,))
            
            history = [dict(row) for row in cursor.fetchall()]
            conn.close()
            return history
        
        return self.execute_with_retry(_get)
    
    def get_upload_status(self, upload_id):
        """Get the status of a specific upload"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT * FROM social_media_upload_history 
                WHERE id = ?
            """, (upload_id,))
            
            row = cursor.fetchone()
            status = dict(row) if row else None
            
            conn.close()
            return status
        
        return self.execute_with_retry(_get)
    
    def get_video_projects_by_novel(self, novel_id):
        """根据小说ID查找关联的视频项目"""
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute(
                "SELECT * FROM video_projects WHERE novel_id = ?",
                (novel_id,)
            )
            projects = cursor.fetchall()
            conn.close()
            
            return projects
        except Exception as e:
            print(f"获取小说关联视频项目时出错: {e}")
            return []

    # 添加方法以更新任务的自动视频生成设置
    def update_scheduled_task_auto_video(self, task_id, auto_generate_video):
        """更新定时任务的自动视频生成设置"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute(
                "UPDATE scheduled_tasks SET auto_generate_video = ? WHERE id = ?",
                (1 if auto_generate_video else 0, task_id)
            )
            
            conn.commit()
            conn.close()
            return cursor.rowcount > 0
        
        return self.execute_with_retry(_update)

    def add_pending_chapter_update(self, novel_id, chapter_id):
        """Add a chapter to the pending updates table"""
        def _add():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                now = time.strftime('%Y-%m-%d %H:%M:%S')
                
                # Use INSERT OR REPLACE to handle duplicates
                cursor.execute(
                    """
                    INSERT OR REPLACE INTO pending_chapter_updates 
                    (novel_id, chapter_id, update_time, last_check_time, status) 
                    VALUES (?, ?, ?, ?, 'pending')
                    """,
                    (novel_id, chapter_id, now, now)
                )
                
                conn.commit()
                return True
            except Exception as e:
                conn.rollback()
                print(f"Error adding pending chapter update: {e}")
                return False
            finally:
                conn.close()
        
        return self.execute_with_retry(_add)

    def get_pending_chapter_updates(self, novel_id):
        """Get all pending chapter updates for a novel"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute(
                    """
                    SELECT * FROM pending_chapter_updates 
                    WHERE novel_id = ? AND status = 'pending'
                    ORDER BY update_time ASC
                    """,
                    (novel_id,)
                )
                
                updates = [dict(row) for row in cursor.fetchall()]
                return updates
            except Exception as e:
                print(f"Error getting pending chapter updates: {e}")
                return []
            finally:
                conn.close()
        
        return self.execute_with_retry(_get)

    def update_pending_chapter_status(self, novel_id, chapter_id, status):
        """Update the status of a pending chapter update"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                now = time.strftime('%Y-%m-%d %H:%M:%S')
                
                cursor.execute(
                    """
                    UPDATE pending_chapter_updates 
                    SET status = ?, last_check_time = ?
                    WHERE novel_id = ? AND chapter_id = ?
                    """,
                    (status, now, novel_id, chapter_id)
                )
                
                conn.commit()
                return cursor.rowcount > 0
            except Exception as e:
                conn.rollback()
                print(f"Error updating pending chapter status: {e}")
                return False
            finally:
                conn.close()
        
        return self.execute_with_retry(_update)

    def clear_pending_chapter_updates(self, novel_id, status='processed'):
        """Clear all pending chapter updates for a novel with the given status"""
        def _clear():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute(
                    """
                    DELETE FROM pending_chapter_updates 
                    WHERE novel_id = ? AND status = ?
                    """,
                    (novel_id, status)
                )
                
                conn.commit()
                return cursor.rowcount
            except Exception as e:
                conn.rollback()
                print(f"Error clearing pending chapter updates: {e}")
                return 0
            finally:
                conn.close()
        
        return self.execute_with_retry(_clear)

    def get_all_pending_chapter_updates_for_project(self, project_id):
        """Get all pending chapter updates for a project via its novel_id"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                # First get the novel_id for this project
                cursor.execute(
                    "SELECT novel_id FROM video_projects WHERE id = ?",
                    (project_id,)
                )
                
                result = cursor.fetchone()
                if not result:
                    return []
                
                novel_id = result['novel_id']
                
                # Then get all pending updates for this novel
                cursor.execute(
                    """
                    SELECT pcu.*, n.title as novel_title 
                    FROM pending_chapter_updates pcu
                    JOIN novels n ON pcu.novel_id = n.id
                    WHERE pcu.novel_id = ? AND pcu.status = 'pending'
                    ORDER BY pcu.update_time ASC
                    """,
                    (novel_id,)
                )
                
                updates = [dict(row) for row in cursor.fetchall()]
                
                # Add chapter titles
                for update in updates:
                    chapter_id = update['chapter_id']
                    table_name = f"novel_{novel_id}"
                    try:
                        cursor.execute(f"""
                        SELECT title FROM {table_name}
                        WHERE chapter_id = ?
                        """, (chapter_id,))
                        
                        result = cursor.fetchone()
                        if result:
                            update['chapter_title'] = result['title']
                        else:
                            update['chapter_title'] = "未知章节"
                    except:
                        update['chapter_title'] = "未知章节"
                
                return updates
            except Exception as e:
                print(f"Error getting pending chapter updates for project: {e}")
                return []
            finally:
                conn.close()
        
        return self.execute_with_retry(_get)

    def check_pending_updates_timeout(self, novel_id, timeout_minutes=10):
        """Check if there have been no new updates for the specified timeout period"""
        def _check():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                now = datetime.datetime.now()
                timeout_threshold = (now - datetime.timedelta(minutes=timeout_minutes)).strftime('%Y-%m-%d %H:%M:%S')
                
                # Get the most recent update time
                cursor.execute(
                    """
                    SELECT MAX(update_time) as last_update
                    FROM pending_chapter_updates
                    WHERE novel_id = ? AND status = 'pending'
                    """,
                    (novel_id,)
                )
                
                result = cursor.fetchone()
                if not result or not result['last_update']:
                    # No pending updates
                    return True
                
                last_update = result['last_update']
                
                # Check if the last update is older than the timeout threshold
                timed_out = last_update < timeout_threshold
                
                return timed_out
            except Exception as e:
                print(f"Error checking pending updates timeout: {e}")
                # Default to true in case of error to avoid indefinite waiting
                return True
            finally:
                conn.close()
        
        return self.execute_with_retry(_check)
    
    # Methods for uploaded videos management
    def save_uploaded_video(self, name, filename, file_path, file_size=None, duration=None, width=None, height=None, thumbnail_path=None):
        """Save uploaded video information to database"""
        def _save():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute(
                    "INSERT INTO uploaded_videos (name, filename, file_path, file_size, duration, width, height, thumbnail_path) VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
                    (name, filename, file_path, file_size, duration, width, height, thumbnail_path)
                )
                
                video_id = cursor.lastrowid
                conn.commit()
                return video_id
            except Exception as e:
                conn.rollback()
                print(f"Error saving uploaded video: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_save)
    
    def get_all_uploaded_videos(self):
        """Get all uploaded videos"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM uploaded_videos ORDER BY created_at DESC")
            videos = [dict(row) for row in cursor.fetchall()]
            
            conn.close()
            return videos
        
        return self.execute_with_retry(_get)
    
    def get_uploaded_video(self, video_id):
        """Get a specific uploaded video"""
        def _get():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            cursor.execute("SELECT * FROM uploaded_videos WHERE id = ?", (video_id,))
            video = cursor.fetchone()
            
            conn.close()
            
            if video:
                return dict(video)
            return None
        
        return self.execute_with_retry(_get)
    
    def delete_uploaded_video(self, video_id):
        """Delete an uploaded video record"""
        def _delete():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            try:
                cursor.execute("DELETE FROM uploaded_videos WHERE id = ?", (video_id,))
                
                conn.commit()
                return cursor.rowcount > 0
            except Exception as e:
                conn.rollback()
                print(f"Error deleting uploaded video: {e}")
                raise
            finally:
                conn.close()
        
        return self.execute_with_retry(_delete)
    
    def update_uploaded_video(self, video_id, name=None, duration=None, width=None, height=None, thumbnail_path=None):
        """Update uploaded video information"""
        def _update():
            conn = self.get_connection()
            cursor = conn.cursor()
            
            update_parts = []
            params = []
            
            if name is not None:
                update_parts.append("name = ?")
                params.append(name)
            
            if duration is not None:
                update_parts.append("duration = ?")
                params.append(duration)
            
            if width is not None:
                update_parts.append("width = ?")
                params.append(width)
            
            if height is not None:
                update_parts.append("height = ?")
                params.append(height)
            
            if thumbnail_path is not None:
                update_parts.append("thumbnail_path = ?")
                params.append(thumbnail_path)
            
            if not update_parts:
                return False
            
            # Add video_id to params
            params.append(video_id)
            
            # Execute update query
            cursor.execute(
                f"UPDATE uploaded_videos SET {', '.join(update_parts)} WHERE id = ?", 
                params
            )
            
            conn.commit()
            conn.close()
            return True
        
        return self.execute_with_retry(_update)