# 社交媒体上传功能 (抖音)

## 功能概述

此功能允许用户将视频项目上传到抖音平台，并提供完整的元数据和封面图片配置选项。

## 用户界面

在视频项目编辑页面添加一个新的标签页 "社交媒体上传"，与现有的章节选择、语音设置等标签页并列。

## 功能详情

### 抖音配置设置

用户可以配置以下抖音上传设置:

1. **启用上传**:
   - 切换按钮可启用/禁用自动上传到抖音
   - 使用 Playwright 自动执行上传流程

2. **视频标题**: 
   - 显示在抖音视频上方
   - 可同步到封面图片上

3. **视频标签**: 
   - 以逗号分隔的标签列表 
   - 用于抖音推荐和搜索

4. **自定义封面图片**:
   - 上传自定义封面图片
   - 选择视频中的帧作为封面
   - 预览封面效果

5. **封面文字设置**:
   - 标题文字: 可以显示到封面上
   - 字体设置: 选择字体类型
   - 字体大小: 调整文字大小
   - 字体颜色: 颜色选择器
   - 字体位置: 顶部/中间/底部或自定义坐标
   - 自定义坐标设置 (X, Y)
   - 实时预览文字效果

6. **章节信息显示**:
   - 在封面上显示选定的章节信息
   - 可自定义章节文字格式和位置

## 数据库设计

在现有数据库中添加新表 `social_media_settings`:

```sql
CREATE TABLE IF NOT EXISTS social_media_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    novel_id INTEGER NOT NULL UNIQUE,
    platform TEXT NOT NULL,  -- 'douyin', 'kuaishou', etc.
    enable_upload BOOLEAN DEFAULT 0,  -- 0: disabled, 1: enabled
    title TEXT,
    tags TEXT,
    custom_cover_path TEXT,
    title_on_cover BOOLEAN DEFAULT 1,
    title_font TEXT,
    title_size INTEGER DEFAULT 40,
    title_color TEXT DEFAULT '#FFFFFF',
    title_position TEXT DEFAULT 'top',
    title_x INTEGER,
    title_y INTEGER,
    show_chapter_info BOOLEAN DEFAULT 0,
    chapter_font TEXT,
    chapter_size INTEGER DEFAULT 30,
    chapter_color TEXT DEFAULT '#FFFFFF',
    chapter_position TEXT DEFAULT 'bottom',
    chapter_x INTEGER,
    chapter_y INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE
)
```

## 前端交互逻辑

1. **实时预览**:
   - 当用户修改标题、字体设置或位置时，实时更新预览区域
   - 使用JavaScript监听表单变化并更新预览

2. **坐标设置**:
   - 当用户选择"自定义坐标"时，显示X和Y坐标输入框
   - 坐标以像素为单位，相对于封面图片尺寸
   - 提供拖拽界面让用户直观地设置文字位置

3. **章节信息显示**:
   - 当用户启用"显示章节信息"时，提供配置选项
   - 可以选择显示章节名称或编号

## 后端实现

1. 新增路由处理社交媒体设置的保存和获取:
   - `/novel/<novel_id>/social_media/update` - 更新设置
   - `/novel/<novel_id>/social_media/preview` - 生成预览图

2. 数据库交互方法:
   - `get_social_media_settings(novel_id, platform)` - 获取社交媒体设置
   - `update_social_media_settings(novel_id, platform, settings)` - 更新社交媒体设置

3. 图像处理功能:
   - 将文字叠加到封面图片上
   - 根据用户设置调整文字位置和样式
   - 生成最终的封面预览

4. Playwright 自动上传功能:
   - 检查 `enable_upload` 设置是否启用
   - 使用 Playwright 自动化浏览器操作上传视频到抖音
   - 自动填写标题、标签和其他元数据
   - 上传指定的封面图片
   - 提供上传状态和结果反馈

## 上传功能

1. 获取抖音API访问权限
2. 用户授权连接抖音账户
3. 实现视频上传功能
   - 使用 Playwright 自动化上传流程
   - 根据 `enable_upload` 设置决定是否自动上传
   - 在视频渲染完成后自动触发上传流程
   - 根据小说ID获取相关配置和内容
4. 提供上传进度和状态反馈
5. 显示上传历史记录

## 技术要求

1. PIL或OpenCV用于图像处理
2. JavaScript用于实时预览
3. 抖音开放平台API接入
4. 安全的API密钥存储机制
5. Playwright库用于自动化浏览器操作
6. asyncio和threading用于异步任务处理 