{% extends "base.html" %}

{% block title %}{{ chapter.title }} - {{ novel.title }} - 小说云{% endblock %}

{% block extra_css %}
<style>
    .chapter-content {
        font-size: 1.1rem;
        line-height: 1.8;
        text-align: justify;
        white-space: pre-wrap;
    }
    
    .chapter-nav {
        position: sticky;
        bottom: 20px;
        z-index: 100;
    }
    
    .reading-settings {
        position: fixed;
        right: 20px;
        top: 100px;
        z-index: 101;
    }
    
    .dark-mode {
        background-color: #232323;
        color: #eee;
    }
    
    .dark-mode .card {
        background-color: #333;
        color: #eee;
    }
    
    .dark-mode .dropdown-menu {
        background-color: #333;
        color: #eee;
    }
    
    .dark-mode .dropdown-item {
        color: #eee;
    }
    
    .dark-mode .dropdown-item:hover {
        background-color: #444;
    }
    
    .sepia-mode {
        background-color: #f4ecd8;
        color: #5b4636;
    }
    
    .sepia-mode .card {
        background-color: #f4ecd8;
        color: #5b4636;
    }
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-lg-10 offset-lg-1">
        <div class="card shadow-sm mb-4">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h1 class="h3 mb-0">{{ chapter.title }}</h1>
                    </div>
                    <div class="col-auto">
                        <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-list-ul me-1"></i>章节列表
                        </a>
                        <button type="button" class="btn btn-outline-danger btn-sm ms-2" data-bs-toggle="modal" data-bs-target="#deleteChapterModal">
                            <i class="bi bi-trash me-1"></i>删除章节
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="chapter-content">{{ chapter.content }}</div>
            </div>
        </div>
        
        <div class="chapter-nav text-center">
            <div class="btn-group shadow">
                {% if prev_chapter %}
                <a href="{{ url_for('read_chapter', novel_id=novel.id, chapter_id=prev_chapter.chapter_id) }}" class="btn btn-light">
                    <i class="bi bi-chevron-left"></i> 上一章
                </a>
                {% else %}
                <button class="btn btn-light" disabled>
                    <i class="bi bi-chevron-left"></i> 上一章
                </button>
                {% endif %}
                
                <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="btn btn-light">
                    <i class="bi bi-list-ul"></i> 目录
                </a>
                
                {% if next_chapter %}
                <a href="{{ url_for('read_chapter', novel_id=novel.id, chapter_id=next_chapter.chapter_id) }}" class="btn btn-light">
                    下一章 <i class="bi bi-chevron-right"></i>
                </a>
                {% else %}
                <button class="btn btn-light" disabled>
                    下一章 <i class="bi bi-chevron-right"></i>
                </button>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="reading-settings">
    <div class="btn-group-vertical shadow">
        <button id="font-size-increase" class="btn btn-light" title="增大字体">
            <i class="bi bi-type-h1"></i>
        </button>
        <button id="font-size-decrease" class="btn btn-light" title="减小字体">
            <i class="bi bi-type-h3"></i>
        </button>
        <div class="btn-group dropend">
            <button type="button" class="btn btn-light dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" title="阅读模式">
                <i class="bi bi-palette"></i>
            </button>
            <ul class="dropdown-menu">
                <li><a class="dropdown-item theme-switch" data-theme="light" href="javascript:void(0)">
                    <i class="bi bi-sun me-2"></i>白色模式
                </a></li>
                <li><a class="dropdown-item theme-switch" data-theme="dark" href="javascript:void(0)">
                    <i class="bi bi-moon me-2"></i>暗色模式
                </a></li>
                <li><a class="dropdown-item theme-switch" data-theme="sepia" href="javascript:void(0)">
                    <i class="bi bi-journal me-2"></i>护眼模式
                </a></li>
            </ul>
        </div>
    </div>
</div>

<div class="modal fade" id="deleteChapterModal" tabindex="-1" aria-labelledby="deleteChapterModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteChapterModalLabel">确认删除章节</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        您确定要删除章节 <strong>"{{ chapter.title }}"</strong> 吗？此操作无法撤销。
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
        <form action="{{ url_for('delete_chapter', novel_id=novel.id, chapter_id=chapter.chapter_id) }}" method="post">
          <button type="submit" class="btn btn-danger">确认删除</button>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 读取保存的设置
    const savedFontSize = localStorage.getItem('fontSize') || '1.1';
    const savedTheme = localStorage.getItem('theme') || 'light';
    
    // 应用字体大小
    $('.chapter-content').css('font-size', savedFontSize + 'rem');
    
    // 应用主题
    applyTheme(savedTheme);
    
    // 字体大小调整
    $('#font-size-increase').click(function() {
        let currentSize = parseFloat($('.chapter-content').css('font-size'));
        let newSize = (currentSize / 16) + 0.1;
        if (newSize <= 2.0) {
            $('.chapter-content').css('font-size', newSize + 'rem');
            localStorage.setItem('fontSize', newSize.toFixed(1));
        }
    });
    
    $('#font-size-decrease').click(function() {
        let currentSize = parseFloat($('.chapter-content').css('font-size'));
        let newSize = (currentSize / 16) - 0.1;
        if (newSize >= 0.8) {
            $('.chapter-content').css('font-size', newSize + 'rem');
            localStorage.setItem('fontSize', newSize.toFixed(1));
        }
    });
    
    // 主题切换
    $('.theme-switch').click(function() {
        const theme = $(this).data('theme');
        applyTheme(theme);
        localStorage.setItem('theme', theme);
    });
    
    function applyTheme(theme) {
        $('body').removeClass('dark-mode sepia-mode');
        if (theme === 'dark') {
            $('body').addClass('dark-mode');
        } else if (theme === 'sepia') {
            $('body').addClass('sepia-mode');
        }
    }
    
    // 键盘导航
    $(document).keydown(function(e) {
        // 左方向键 - 上一章
        if (e.keyCode === 37) {
            const prevLink = $('a:contains("上一章")').not(':disabled');
            if (prevLink.length) {
                window.location.href = prevLink.attr('href');
            }
        }
        // 右方向键 - 下一章
        else if (e.keyCode === 39) {
            const nextLink = $('a:contains("下一章")').not(':disabled');
            if (nextLink.length) {
                window.location.href = nextLink.attr('href');
            }
        }
    });
});
</script>
{% endblock %} 