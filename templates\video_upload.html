{% extends "base.html" %}

{% block title %}视频上传管理 - 小说云{% endblock %}

{% block extra_css %}
<style>
.video-card {
    transition: transform 0.2s;
}
.video-card:hover {
    transform: translateY(-2px);
}
.video-thumbnail {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
}
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 40px;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s;
}
.upload-area:hover {
    border-color: #007bff;
    background-color: #e3f2fd;
}
.upload-area.dragover {
    border-color: #007bff;
    background-color: #e3f2fd;
}
.progress {
    height: 20px;
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3">视频上传管理</h1>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#uploadModal">
            <i class="bi bi-cloud-upload"></i> 上传视频
        </button>
    </div>

    <!-- 视频列表 -->
    <div class="row" id="videoList">
        {% for video in videos %}
        <div class="col-md-4 col-lg-3 mb-4">
            <div class="card video-card h-100">
                {% if video.thumbnail_path %}
                <img src="{{ url_for('uploaded_video', filename=video.thumbnail_path.split('/')[-1]) }}" 
                     class="video-thumbnail" alt="视频缩略图">
                {% else %}
                <div class="video-thumbnail d-flex align-items-center justify-content-center bg-light">
                    <i class="bi bi-play-circle" style="font-size: 3rem; color: #6c757d;"></i>
                </div>
                {% endif %}
                
                <div class="card-body">
                    <h6 class="card-title">{{ video.name }}</h6>
                    <p class="card-text text-muted small">
                        {% if video.duration %}
                        时长: {{ "%.1f"|format(video.duration) }}秒<br>
                        {% endif %}
                        {% if video.width and video.height %}
                        分辨率: {{ video.width }}x{{ video.height }}<br>
                        {% endif %}
                        {% if video.file_size %}
                        大小: {{ "%.1f"|format(video.file_size / 1024 / 1024) }}MB<br>
                        {% endif %}
                        上传时间: {{ video.created_at }}
                    </p>
                </div>
                
                <div class="card-footer">
                    <div class="btn-group w-100" role="group">
                        <button class="btn btn-sm btn-outline-primary" onclick="previewVideo({{ video.id }})">
                            <i class="bi bi-eye"></i> 预览
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="editVideo({{ video.id }})">
                            <i class="bi bi-pencil"></i> 编辑
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteVideo({{ video.id }})">
                            <i class="bi bi-trash"></i> 删除
                        </button>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
        
        {% if not videos %}
        <div class="col-12">
            <div class="text-center py-5">
                <i class="bi bi-camera-video" style="font-size: 4rem; color: #6c757d;"></i>
                <h4 class="mt-3 text-muted">还没有上传任何视频</h4>
                <p class="text-muted">点击上传按钮开始添加您的视频文件</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 上传模态框 -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">上传视频</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="videoName" class="form-label">视频名称</label>
                        <input type="text" class="form-control" id="videoName" name="name" required>
                        <div class="form-text">给视频起一个便于识别的名称</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">选择视频文件</label>
                        <input type="file" class="form-control d-none" id="videoFile" name="video" accept="video/*" required>
                        <div class="upload-area" id="uploadArea">
                            <div id="uploadDisplay">
                                <i class="bi bi-cloud-upload" style="font-size: 3rem; color: #6c757d;"></i>
                                <h5 class="mt-3">拖拽视频文件到这里</h5>
                                <p class="text-muted">或者点击选择文件</p>
                                <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('videoFile').click()">
                                    选择文件
                                </button>
                            </div>
                        </div>
                        <div class="form-text">支持 MP4, AVI, MOV, WMV 等常见视频格式</div>
                    </div>
                    
                    <div id="uploadProgress" class="mb-3" style="display: none;">
                        <label class="form-label">上传进度</label>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div class="text-center mt-2">
                            <span id="progressText">0%</span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="uploadBtn" onclick="uploadVideo()">上传</button>
            </div>
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">视频预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <video id="previewVideo" class="w-100" controls>
                    您的浏览器不支持视频播放
                </video>
            </div>
        </div>
    </div>
</div>

<!-- 编辑模态框 -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">编辑视频信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editForm">
                    <input type="hidden" id="editVideoId">
                    <div class="mb-3">
                        <label for="editVideoName" class="form-label">视频名称</label>
                        <input type="text" class="form-control" id="editVideoName" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveVideoEdit()">保存</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 拖拽上传功能
const uploadArea = document.getElementById('uploadArea');
const videoFile = document.getElementById('videoFile');

uploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    uploadArea.classList.add('dragover');
});

uploadArea.addEventListener('dragleave', () => {
    uploadArea.classList.remove('dragover');
});

uploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        videoFile.files = files;
        updateFileName();
    }
});

videoFile.addEventListener('change', updateFileName);

function updateFileName() {
    const file = videoFile.files[0];
    if (file) {
        const fileName = file.name;
        const baseName = fileName.substring(0, fileName.lastIndexOf('.'));
        document.getElementById('videoName').value = baseName;
        
        // 更新显示区域
        const displayArea = document.getElementById('uploadDisplay');
        displayArea.innerHTML = `
            <i class="bi bi-file-earmark-play" style="font-size: 3rem; color: #28a745;"></i>
            <h5 class="mt-3">已选择文件</h5>
            <p class="text-muted">${fileName}</p>
            <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('videoFile').click()">
                重新选择
            </button>
        `;
    }
}

function uploadVideo() {
    const form = document.getElementById('uploadForm');
    const formData = new FormData(form);
    
    if (!videoFile.files[0]) {
        alert('请选择视频文件');
        return;
    }
    
    const uploadBtn = document.getElementById('uploadBtn');
    const progressDiv = document.getElementById('uploadProgress');
    const progressBar = progressDiv.querySelector('.progress-bar');
    const progressText = document.getElementById('progressText');
    
    uploadBtn.disabled = true;
    progressDiv.style.display = 'block';
    
    const xhr = new XMLHttpRequest();
    
    xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable) {
            const percentComplete = (e.loaded / e.total) * 100;
            progressBar.style.width = percentComplete + '%';
            progressText.textContent = Math.round(percentComplete) + '%';
        }
    });
    
    xhr.addEventListener('load', () => {
        if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            if (response.success) {
                alert('视频上传成功！');
                location.reload();
            } else {
                alert('上传失败：' + response.message);
            }
        } else {
            alert('上传失败，请重试');
        }
        
        uploadBtn.disabled = false;
        progressDiv.style.display = 'none';
    });
    
    xhr.addEventListener('error', () => {
        alert('上传失败，请检查网络连接');
        uploadBtn.disabled = false;
        progressDiv.style.display = 'none';
    });
    
    xhr.open('POST', '/upload_video');
    xhr.send(formData);
}

function previewVideo(videoId) {
    fetch(`/get_video/${videoId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const video = data.video;
                const previewVideo = document.getElementById('previewVideo');
                previewVideo.src = `/uploaded_videos/${video.filename}`;
                
                const modal = new bootstrap.Modal(document.getElementById('previewModal'));
                modal.show();
            } else {
                alert('获取视频信息失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('获取视频信息失败');
        });
}

function editVideo(videoId) {
    fetch(`/get_video/${videoId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const video = data.video;
                document.getElementById('editVideoId').value = video.id;
                document.getElementById('editVideoName').value = video.name;
                
                const modal = new bootstrap.Modal(document.getElementById('editModal'));
                modal.show();
            } else {
                alert('获取视频信息失败');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('获取视频信息失败');
        });
}

function saveVideoEdit() {
    const videoId = document.getElementById('editVideoId').value;
    const name = document.getElementById('editVideoName').value;
    
    if (!name.trim()) {
        alert('请输入视频名称');
        return;
    }
    
    fetch(`/update_video/${videoId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            name: name
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('更新成功！');
            location.reload();
        } else {
            alert('更新失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('更新失败');
    });
}

function deleteVideo(videoId) {
    if (!confirm('确定要删除这个视频吗？此操作不可恢复。')) {
        return;
    }
    
    fetch(`/delete_video/${videoId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('删除成功！');
            location.reload();
        } else {
            alert('删除失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('删除失败');
    });
}
</script>
{% endblock %}