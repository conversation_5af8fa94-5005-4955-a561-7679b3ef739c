import sys
import os
import time
import json
import requests
import hashlib
import random
import base64
import re
import zipfile
import io
from tqdm import tqdm
from Crypto.Cipher import AES
from Crypto.Util.Padding import unpad

# 灵猫小说下载器 - 按照qimao.py的接口实现
class SwiftcatDownloader:
    # 类级别的sign_key，在实例没有时使用这个作为备份
    DEFAULT_SIGN_KEY = 'd3dGiJc651gSQ8w1'
    
    def __init__(self, novel_id, db, progress_tracker=None):
        self.novel_id = novel_id
        self.db = db
        self.progress_tracker = progress_tracker
        self.timeout = 10  # 减少超时时间，从30秒改为10秒
        self.max_retries = 2  # 减少重试次数，从5次改为2次
        # 类级别的sign_key，在实例没有时使用这个作为备份
        self.sign_key = self.DEFAULT_SIGN_KEY  # 七猫签名密钥
        self.last_error = None  # 保存最后一次错误信息
        self.proxies = None
        
        # 灵猫特有的常量
        self._AES_KEY_HEX = '32343263636238323330643730396531'
        self._BASE_URL_BC = "https://api-bc.wtzw.com"
        self._BASE_URL_KS = "https://api-ks.wtzw.com"
        
        # 版本列表，与Flutter版本保持一致
        self._VERSION_LIST = [
            '73720', '73700', '73620', '73600', '73500', '73420', '73400',
            '73328', '73325', '73320', '73300', '73220', '73200', '73100',
            '73000', '72900', '72820', '72800', '70720', '62010', '62112',
        ]
        
        # 在所有属性定义完成后再初始化headers
        self.headers = self.get_headers(novel_id)
    
    def get_headers(self, book_id):
        """获取请求头，模拟APP请求"""
        # 使用书籍ID作为随机种子，确保相同书籍使用相同版本
        random.seed(hash(book_id))
        version = random.choice(self._VERSION_LIST)

        headers = {
            "AUTHORIZATION": "",
            "app-version": f"{version}",
            "application-id": "com.kmxs.reader",
            "channel": "unknown",
            "net-env": "1",
            "platform": "android",
            "qm-params": "",
            "reg": "0",
        }

        # 获取 headers 的所有键并排序
        keys = sorted(headers.keys())

        # 确保sign_key可用，使用默认值作为备份
        sign_key = getattr(self, 'sign_key', self.DEFAULT_SIGN_KEY)
        
        # 生成待签名的字符串
        sign_str = ''.join([k + '=' + str(headers[k]) for k in keys]) + sign_key

        # 生成签名
        headers['sign'] = hashlib.md5(sign_str.encode()).hexdigest()

        return headers
    
    def sign_url_params(self, params):
        """给请求参数添加签名"""
        keys = sorted(params.keys())
        
        # 确保sign_key可用，使用默认值作为备份
        sign_key = getattr(self, 'sign_key', self.DEFAULT_SIGN_KEY)

        # 生成待签名的字符串
        sign_str = ''.join([k + '=' + str(params[k]) for k in keys]) + sign_key

        # 使用MD5哈希生成签名
        signature = hashlib.md5(sign_str.encode()).hexdigest()

        # 将签名添加到参数字典中
        params['sign'] = signature

        return params
    
    @staticmethod
    def search(keyword):
        """Search for novels on SwiftCat (七猫平台)"""
        try:
            url = "https://api-bc.wtzw.com/api/v1/search/search"
            params = {
                'keyword': keyword,
                'page': 1,
                'type': 1
            }
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            }
            response = requests.get(url, params=params, headers=headers, timeout=30)
            data = response.json()
            
            results = []
            if data.get('code') == 0 and 'data' in data:
                for item in data['data'].get('list', []):
                    results.append({
                        'id': item.get('id'),
                        'title': item.get('title'),
                        'author': item.get('author'),
                        'cover_url': item.get('cover_url'),
                        'description': item.get('intro'),
                        'source': 'swiftcat'
                    })
            return results
        except Exception as e:
            print(f"Error searching SwiftCat: {e}")
            return []
    
    def _make_request_with_retry(self, url, params=None, method="get"):
        """Make a request with retry mechanism"""
        last_exception = None
        for attempt in range(self.max_retries):
            try:
                if method.lower() == "get":
                    response = requests.get(url, params=params, headers=self.headers, timeout=self.timeout, proxies=self.proxies)
                else:
                    response = requests.post(url, data=params, headers=self.headers, timeout=self.timeout, proxies=self.proxies)
                
                # 简化调试输出，减少日志量
                if attempt == 0:  # 只在第一次尝试时打印详细信息
                    print(f"Request URL: {url}")
                    print(f"Response status: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        # 快速检测API错误
                        if data and data.get('errors'):
                            # 有错误直接返回失败，不再重试
                            print(f"API returned error: {data.get('errors')}")
                            return None
                        return data
                    except json.JSONDecodeError as e:
                        last_exception = e
                        if attempt == 0:  # 只在第一次错误时输出详细信息
                            print(f"JSON decode error: {e}")
                elif response.status_code == 404:
                    # 404错误直接返回，不再重试
                    print(f"API endpoint not found (404)")
                    return None
                else:
                    print(f"Request failed with status code {response.status_code}, attempt {attempt+1}/{self.max_retries}")
                    last_exception = Exception(f"HTTP Error: {response.status_code}")
            except Exception as e:
                print(f"Request error on attempt {attempt+1}/{self.max_retries}: {e}")
                last_exception = e
                
            # 等待后重试 (指数退避，但使用更短的基础时间)
            if attempt < self.max_retries - 1:
                sleep_time = 0.5 * (2 ** attempt) + random.random() * 0.5  # 更短的等待时间
                print(f"Retrying in {sleep_time:.2f} seconds...")
                time.sleep(sleep_time)
        
        # 所有重试都失败
        if last_exception:
            self.last_error = f"All {self.max_retries} attempts failed: {last_exception}"
        else:
            self.last_error = f"All {self.max_retries} attempts failed with unknown error"
        return None 
   
    def get_novel_info(self):
        """Get novel information from SwiftCat (七猫平台)"""
        try:
            # 使用七猫小说的API获取小说信息
            params = {'id': self.novel_id, 'imei_ip': '2937357107', 'teeny_mode': '0'}
            params = self.sign_url_params(params)
            
            response = requests.get(
                f"{self._BASE_URL_BC}/api/v4/book/detail",
                params=params,
                headers=self.headers,
                timeout=self.timeout,
                proxies=self.proxies
            )
            
            response_data = response.json()
            if response.status_code == 200 and response_data.get('data'):
                book_info = response_data['data']['book']
                
                # 创建小说信息对象
                novel_info = {
                    'id': self.novel_id,
                    'title': book_info.get('title', '未知标题'),
                    'author': book_info.get('author', '未知作者'),
                    'cover_url': book_info.get('image_link', book_info.get('big_image_link', '')),
                    'description': book_info.get('intro', '无描述'),
                    'source': 'qimao'
                }
                print(f"Successfully extracted novel info: {novel_info}")
                return novel_info
            else:
                error_msg = response_data.get('message', '未知错误')
                print(f'获取书籍信息失败: {error_msg}')
                return None
                
        except Exception as e:
            self.last_error = f"Error getting novel info: {e}"
            print(self.last_error)
            return None
    
    def get_chapters(self):
        """获取章节列表"""
        try:
            params = {'chapter_ver': '0', 'id': self.novel_id}
            params = self.sign_url_params(params)
            
            response = requests.get(
                f"{self._BASE_URL_KS}/api/v1/chapter/chapter-list",
                params=params,
                headers=self.headers,
                timeout=self.timeout,
                proxies=self.proxies
            )
            
            response_data = response.json()
            if response.status_code == 200 and response_data.get('data', {}).get('chapter_lists'):
                chapters_json = response_data['data']['chapter_lists']
                # 按章节序号排序
                chapters_json.sort(key=lambda x: x.get('chapter_sort', 0))
                
                # 转换为我们需要的格式
                formatted_chapters = []
                for i, chapter in enumerate(chapters_json):
                    formatted_chapters.append({
                        'chapter_id': chapter['id'],
                        'title': chapter['title'],
                        'order_index': i
                    })
                
                print(f"成功获取 {len(formatted_chapters)} 个章节")
                return formatted_chapters
            else:
                error_msg = response_data.get('message', '未知错误')
                print(f'获取目录失败: {error_msg}')
                return []
                
        except Exception as e:
            self.last_error = f"获取章节列表出错: {e}"
            print(self.last_error)
            return []
    
    def get_cache_zip_link(self):
        """获取缓存ZIP链接"""
        try:
            params = {'id': self.novel_id, 'source': 1, 'type': 2, 'is_vip': 1}
            params = self.sign_url_params(params)
            
            response = requests.get(
                f"{self._BASE_URL_BC}/api/v1/book/download",
                params=params,
                headers=self.headers,
                timeout=self.timeout,
                proxies=self.proxies
            )
            
            response_data = response.json()
            if response.status_code == 200 and response_data.get('data', {}).get('link'):
                return response_data['data']['link']
            else:
                error_msg = response_data.get('message', '未知错误')
                print(f'获取下载链接失败: {error_msg}')
                return None
                
        except Exception as e:
            self.last_error = f"获取缓存ZIP链接出错: {e}"
            print(self.last_error)
            return None
    
    def decrypt_chapter_content(self, encrypted_content):
        """解密章节内容"""
        try:
            # 将十六进制密钥转换为字节
            key = bytes.fromhex(self._AES_KEY_HEX)
            
            # Base64解码加密内容
            encrypted_bytes = base64.b64decode(encrypted_content)
            
            # 提取IV（前16字节）
            iv = encrypted_bytes[:16]
            
            # 创建AES解密器（CBC模式）
            cipher = AES.new(key, AES.MODE_CBC, iv)
            
            # 解密内容（从第16字节开始）
            decrypted = unpad(cipher.decrypt(encrypted_bytes[16:]), AES.block_size)
            return decrypted.decode('utf-8')
        except Exception as e:
            print(f"解密失败: {e}")
            return ""
    
    def get_chapter_content(self, chapter_id):
        """获取章节内容 - 使用缓存ZIP方式"""
        # 注意：这里我们不能直接获取单个章节内容
        # 灵猫的实现是通过下载整个ZIP文件来获取所有章节
        # 所以这个方法在灵猫实现中不太适用
        # 我们在download方法中会一次性获取所有章节
        print(f"SwiftCat downloader uses batch download, cannot get single chapter {chapter_id}")
        return f"SwiftCat downloader uses batch download method"    

    def download(self):
        """Download the entire novel using ZIP cache method"""
        # Get novel info
        novel_info = self.get_novel_info()
        if not novel_info:
            error_msg = f'Failed to get novel info for ID {self.novel_id}. Please check the novel ID or try again later.'
            if self.last_error:
                error_msg += f" Error: {self.last_error}"
            print(error_msg)
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = error_msg
            return False
        
        # Save novel info to database
        self.db.save_novel(novel_info)
        print(f"Novel info saved: {novel_info['title']} by {novel_info['author']}")
        
        # Get all chapters
        chapters = self.get_chapters()
        if not chapters:
            error_msg = f'Failed to get chapters for novel ID {self.novel_id}. The novel might not exist or be accessible.'
            if self.last_error:
                error_msg += f" Error: {self.last_error}"
            print(error_msg)
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = error_msg
            return False
        
        print(f"Found {len(chapters)} chapters")
        
        # Update progress tracker
        if self.progress_tracker:
            self.progress_tracker[self.novel_id]['total'] = len(chapters)
            self.progress_tracker[self.novel_id]['current'] = 0
            self.progress_tracker[self.novel_id]['message'] = f"Downloading {len(chapters)} chapters..."
        
        try:
            # 1. 获取缓存文件链接
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['message'] = '正在获取缓存文件链接...'
            
            zip_link = self.get_cache_zip_link()
            if not zip_link:
                error_msg = '获取缓存文件链接失败'
                print(error_msg)
                if self.progress_tracker:
                    self.progress_tracker[self.novel_id]['status'] = 'error'
                    self.progress_tracker[self.novel_id]['message'] = error_msg
                return False
            
            # 2. 下载ZIP文件
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['message'] = '正在下载缓存文件...'
            
            response = requests.get(zip_link, stream=True, timeout=30)
            response.raise_for_status()
            
            # 获取文件大小
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            zip_content = bytearray()
            
            # 分块下载
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    zip_content.extend(chunk)
                    downloaded_size += len(chunk)
                    if total_size > 0 and self.progress_tracker:
                        progress = (downloaded_size / total_size) * 0.4  # 下载占40%进度
                        self.progress_tracker[self.novel_id]['message'] = f'正在下载缓存文件... {downloaded_size/1024/1024:.2f}MB'
            
            # 3. 解压和解密章节
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['message'] = '正在解压和解密章节...'
            
            decrypted_chapters = {}
            with zipfile.ZipFile(io.BytesIO(zip_content)) as zip_file:
                file_list = zip_file.namelist()
                for i, file_name in enumerate(file_list):
                    # 提取章节ID（文件名去除扩展名）
                    chapter_id = os.path.splitext(os.path.basename(file_name))[0]
                    
                    # 读取加密内容
                    encrypted_content = zip_file.read(file_name).decode('utf-8')
                    
                    # 解密章节
                    decrypted_content = self.decrypt_chapter_content(encrypted_content)
                    decrypted_chapters[chapter_id] = decrypted_content
                    
                    # 更新进度
                    if self.progress_tracker:
                        progress = 0.5 + (i / len(file_list)) * 0.2  # 解密占20%进度
            
            # 4. 保存章节到数据库
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['message'] = '正在保存章节...'
            
            success_count = 0
            error_count = 0
            
            for chapter in tqdm(chapters, desc=f"Saving {novel_info['title']}"):
                try:
                    chapter_id = str(chapter['chapter_id'])
                    if chapter_id in decrypted_chapters:
                        # 构建完整章节数据
                        chapter_data = {
                            'chapter_id': chapter_id,
                            'title': chapter['title'],
                            'content': decrypted_chapters[chapter_id],
                            'order_index': chapter['order_index']
                        }
                        self.db.save_chapter(self.novel_id, chapter_data)
                        success_count += 1
                        print(f"已保存: {chapter['title']}")
                    else:
                        error_count += 1
                        print(f"未找到章节内容: {chapter['title']}")
                    
                    # Update progress
                    if self.progress_tracker:
                        self.progress_tracker[self.novel_id]['current'] += 1
                        
                except Exception as e:
                    error_count += 1
                    print(f"保存章节 {chapter.get('title', 'unknown')} 时出错: {e}")
            
            # Report download completion with statistics
            if self.progress_tracker:
                if success_count == 0:
                    self.progress_tracker[self.novel_id]['status'] = 'error'
                    self.progress_tracker[self.novel_id]['message'] = f'Failed to download any chapters. {error_count} errors occurred.'
                else:
                    if success_count == len(chapters):
                        self.progress_tracker[self.novel_id]['status'] = 'completed'
                        self.progress_tracker[self.novel_id]['message'] = f'Successfully downloaded all {success_count} chapters!'
                    else:
                        self.progress_tracker[self.novel_id]['status'] = 'partial'
                        self.progress_tracker[self.novel_id]['message'] = f'Downloaded {success_count}/{len(chapters)} chapters. {error_count} errors occurred.'
            
            print(f"Download completed: {success_count} chapters successful, {error_count} failures")
            return success_count > 0
            
        except Exception as e:
            error_msg = f'下载失败: {e}'
            print(error_msg)
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = error_msg
            return False   
 
    def update(self):
        """Update an existing novel with new chapters"""
        # 获取小说信息
        novel_info = self.get_novel_info()
        if not novel_info:
            error_msg = f'更新失败: 无法获取小说信息 (ID: {self.novel_id})'
            if self.last_error:
                error_msg += f" 错误: {self.last_error}"
            print(error_msg)
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = error_msg
            return False
            
        print(f"正在更新小说: {novel_info['title']} 作者: {novel_info['author']}")
        
        # 获取数据库中已有的章节
        local_chapters = self.db.get_chapters(self.novel_id)
        local_chapter_ids = set(chapter['chapter_id'] for chapter in local_chapters)
        
        # 获取网络上所有章节
        online_chapters = self.get_chapters()
        if not online_chapters:
            error_msg = f'无法获取章节列表 (ID: {self.novel_id})'
            if self.last_error:
                error_msg += f" 错误: {self.last_error}"
            print(error_msg)
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = error_msg
            return False
            
        # 找出新章节（网络章节中不在本地的）
        new_chapters = [chapter for chapter in online_chapters if str(chapter['chapter_id']) not in local_chapter_ids]
        
        if self.progress_tracker is not None:
            # 检查 novel_id 是否存在于 progress_tracker 中
            if self.novel_id not in self.progress_tracker:
                # 如果不存在，初始化该novel_id的跟踪数据
                self.progress_tracker[self.novel_id] = {
                    'status': 'pending',
                    'total': 0,
                    'current': 0,
                    'message': '准备更新...'
                }
            self.progress_tracker[self.novel_id]['total'] = len(new_chapters)
            self.progress_tracker[self.novel_id]['current'] = 0
            
        if not new_chapters:
            print(f"小说 '{novel_info['title']}' 已是最新，没有新章节")
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'completed'
                self.progress_tracker[self.novel_id]['message'] = '小说已是最新，没有新章节'
            return False
            
        print(f"发现 {len(new_chapters)} 个新章节，开始下载...")
        
        # 对于灵猫，我们需要重新下载整个ZIP文件来获取新章节
        # 这里简化处理，直接调用download方法重新下载所有章节
        # 在实际应用中，可以优化为只下载新章节
        try:
            # 获取缓存ZIP链接
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['message'] = '正在获取缓存文件链接...'
            
            zip_link = self.get_cache_zip_link()
            if not zip_link:
                error_msg = '获取缓存文件链接失败'
                print(error_msg)
                if self.progress_tracker:
                    self.progress_tracker[self.novel_id]['status'] = 'error'
                    self.progress_tracker[self.novel_id]['message'] = error_msg
                return False
            
            # 下载ZIP文件
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['message'] = '正在下载缓存文件...'
            
            response = requests.get(zip_link, stream=True, timeout=30)
            response.raise_for_status()
            
            zip_content = bytearray()
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    zip_content.extend(chunk)
            
            # 解压和解密章节
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['message'] = '正在解压和解密章节...'
            
            decrypted_chapters = {}
            with zipfile.ZipFile(io.BytesIO(zip_content)) as zip_file:
                file_list = zip_file.namelist()
                for file_name in file_list:
                    chapter_id = os.path.splitext(os.path.basename(file_name))[0]
                    encrypted_content = zip_file.read(file_name).decode('utf-8')
                    decrypted_content = self.decrypt_chapter_content(encrypted_content)
                    decrypted_chapters[chapter_id] = decrypted_content
            
            # 只保存新章节
            success_count = 0
            error_count = 0
            
            for chapter in tqdm(new_chapters, desc=f"更新 {novel_info['title']}"):
                try:
                    chapter_id = str(chapter['chapter_id'])
                    if chapter_id in decrypted_chapters:
                        chapter_data = {
                            'chapter_id': chapter_id,
                            'title': chapter['title'],
                            'content': decrypted_chapters[chapter_id],
                            'order_index': chapter['order_index']
                        }
                        self.db.save_chapter(self.novel_id, chapter_data)
                        success_count += 1
                        print(f"已下载: {chapter['title']}")
                    else:
                        error_count += 1
                        print(f"未找到章节内容: {chapter['title']}")
                    
                    # 更新进度
                    if self.progress_tracker:
                        self.progress_tracker[self.novel_id]['current'] += 1
                        
                except Exception as e:
                    error_count += 1
                    print(f"下载章节 {chapter.get('title', '未知')} 时出错: {e}")
            
            # 报告更新完成情况
            if self.progress_tracker:
                if success_count == 0:
                    self.progress_tracker[self.novel_id]['status'] = 'error'
                    self.progress_tracker[self.novel_id]['message'] = f'未能下载任何新章节，发生 {error_count} 个错误'
                else:
                    if success_count == len(new_chapters):
                        self.progress_tracker[self.novel_id]['status'] = 'completed'
                        self.progress_tracker[self.novel_id]['message'] = f'成功下载所有 {success_count} 个新章节！'
                    else:
                        self.progress_tracker[self.novel_id]['status'] = 'partial'
                        self.progress_tracker[self.novel_id]['message'] = f'下载了 {success_count}/{len(new_chapters)} 个新章节，发生 {error_count} 个错误'
            
            print(f"更新完成: {success_count} 章成功, {error_count} 章失败")
            return success_count > 0
            
        except Exception as e:
            error_msg = f'更新失败: {e}'
            print(error_msg)
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = error_msg
            return False