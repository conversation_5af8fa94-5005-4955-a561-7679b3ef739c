<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}小说云 - 多源小说搜索与下载系统{% endblock %}</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    {% block extra_css %}{% endblock %}
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="bi bi-book me-2"></i>小说云
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
                        <div class="collapse navbar-collapse" id="navbarNav">                <ul class="navbar-nav me-auto">                    <li class="nav-item">                        <a class="nav-link" href="{{ url_for('index') }}">首页</a>                    </li>                    <li class="nav-item">                        <a class="nav-link" href="{{ url_for('search') }}">搜索</a>                    </li>                    <li class="nav-item">                        <a class="nav-link" href="{{ url_for('scheduled_updates') }}">                            <i class="bi bi-clock-history me-1"></i>定时更新                        </a>                    </li>                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="videoDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-camera-video me-1"></i>视频生成
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="videoDropdown">
                            <li><a class="dropdown-item" href="{{ url_for('novel_to_video') }}">创建视频项目</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('task_manager') }}">任务管理</a></li>
                            <!-- <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('video_upload') }}">
                                <i class="bi bi-cloud-upload me-1"></i>视频管理
                            </a></li> -->
                        </ul>
                    </li>                </ul>
                <form class="d-flex" action="{{ url_for('search') }}" method="post">
                    <input class="form-control me-2" type="search" name="keyword" placeholder="输入小说名或作者" aria-label="Search">
                    <button class="btn btn-light" type="submit">搜索</button>
                </form>
            </div>
        </div>
    </nav>

    <div class="container mt-4 mb-5">
        {% block content %}{% endblock %}
    </div>

    <footer class="footer mt-auto py-3 bg-light">
        <div class="container text-center">
            <span class="text-muted">小说云 &copy; 2025 - 2026</span>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html> 