from pathlib import Path
import os

# Get the absolute path to the project root directory
BASE_DIR = Path(os.path.dirname(os.path.abspath(__file__)))

# Path to local Chrome/Chromium executable
# Change this path to match your Chrome installation if needed
LOCAL_CHROME_PATH = r"C:\Program Files\Google\Chrome\Application\chrome.exe"

# If Chrome is not in the default location, you might need to update the path above
# Common alternative locations:
# - r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe"
# - r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe" 