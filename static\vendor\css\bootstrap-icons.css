@font-face {
  font-display: block;
  font-family: "bootstrap-icons";
  src: url("../fonts/bootstrap-icons.woff2?8d200481aa7f3c9ca1b0c9da5d6a4d05") format("woff2"),
url("../fonts/bootstrap-icons.woff?8d200481aa7f3c9ca1b0c9da5d6a4d05") format("woff");
}

.bi::before,
[class^="bi-"]::before,
[class*=" bi-"]::before {
  display: inline-block;
  font-family: bootstrap-icons !important;
  font-style: normal;
  font-weight: normal !important;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: -.125em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.bi-book::before { content: "\f17b"; }
.bi-books::before { content: "\f1a0"; }
.bi-bookmark::before { content: "\f184"; }
.bi-download::before { content: "\f30b"; }
.bi-exclamation-triangle::before { content: "\f33a"; }
.bi-info-circle::before { content: "\f430"; }
.bi-search::before { content: "\f52a"; }
.bi-house-door::before { content: "\f425"; }
.bi-arrow-left::before { content: "\f12a"; }
.bi-check-circle::before { content: "\f26d"; }
.bi-list-ul::before { content: "\f47c"; }
.bi-chevron-left::before { content: "\f284"; }
.bi-chevron-right::before { content: "\f285"; }
.bi-palette::before { content: "\f4d9"; }
.bi-sun::before { content: "\f5a2"; }
.bi-moon::before { content: "\f499"; }
.bi-journal::before { content: "\f448"; }
.bi-type-h1::before { content: "\f5eb"; }
.bi-type-h3::before { content: "\f5ed"; }
.bi-book-half::before { content: "\f199"; }
.bi-hourglass::before { content: "\f423"; }
.bi-exclamation-circle::before { content: "\f33a"; } 