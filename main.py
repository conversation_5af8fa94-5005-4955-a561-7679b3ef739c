import os
from flask import Flask, render_template, request, jsonify, redirect, url_for, send_file, session, abort
from werkzeug.utils import secure_filename
import sqlite3
import json
import threading
import time
import datetime
import logging
import shutil
import database
import uuid
import queue

import re
import cv2
import subprocess
import sys
from pathlib import Path
import glob
from pydub import AudioSegment
import tempfile
import urllib.parse
from PIL import Image, ImageDraw, ImageFont
import hashlib

# Import novel downloader modules
from novel_sources.fanqie import FanqieDownloader
from novel_sources.qimao import QimaoDownloader
from novel_sources.swiftcat_downloader_new import SwiftcatDownloader
from database import NovelDatabase

from app.localtrancribe.client_transcribe import transcribe_check, transcribe_send, transcribe_recv

# Add imports for video generation
import edge_tts
import asyncio
import subprocess
import tempfile
import platform
import shutil
from pathlib import Path
from app.core.bk_asr.jianying import JianYingASR
from app.core.bk_asr.bcut import BcutASR
from app.core.bk_asr.asr_data import ASRData
from social.utils.files_times import generate_schedule_time_next_day
from social.uploader.douyin_uploader.main import DouYinVideo, douyin_cookie_gen_without_login,douyin_check_cookie
app = Flask(__name__)
app.config['SECRET_KEY'] = 'novelcloud-secret-key'

# 配置上传文件夹
UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static', 'uploads')
# app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
# if not os.path.exists(UPLOAD_FOLDER):
#     os.makedirs(UPLOAD_FOLDER)
app.config['UPLOAD_FOLDER'] = 'downloads'

# 配置视频上传专用目录
VIDEO_UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploaded_videos')
app.config['VIDEO_UPLOAD_FOLDER'] = VIDEO_UPLOAD_FOLDER

# 配置允许上传的最大文件大小
app.config['MAX_CONTENT_LENGTH'] = 2000 * 1024 * 1024  # 2000 MB

# 检查文件扩展名是否允许上传
def allowed_file(filename, allowed_extensions=None):
    """检查文件扩展名是否在允许列表中"""
    if allowed_extensions is None:
        allowed_extensions = {'txt', 'pdf', 'jpg', 'jpeg', 'png', 'gif', 'mp3', 'mp4', 'wav'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

app.config['DATABASE'] = 'novels.db'

# Ensure download directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Initialize database
db = NovelDatabase(app.config['DATABASE'])
db.init_db()

# Global variable to track download progress
download_progress = {}
# Global variable to track update progress
update_progress = {}
# Global variable to track running scheduled tasks
scheduled_task_threads = {}
# Flag to control the scheduler thread
scheduler_running = True

# Global variables for task management
task_threads = {}
task_control = {}  # Used to control running tasks (pause/resume/stop)

# 添加全局变量用于控制更新和视频生成任务
video_generation_lock = threading.Lock()  # 视频生成锁
update_to_video_tasks = {}  # 存储更新后待生成视频的任务 {task_id: {novel_id, last_update_time, chapters, project_id}}
is_generating_video = False  # 指示是否正在生成视频

@app.route('/')
def index():
    novels = db.get_all_novels()
    # 获取Cookie配置状态
    fanqie_cookie = db.get_fanqie_cookie()
    cookie_status = '已配置' if fanqie_cookie else '未配置'
    
    # 首先尝试渲染原始模板
    try:
        return render_template('index.html', novels=novels, cookie_status=cookie_status)
    except Exception as e:
        print(f"Error rendering index.html: {e}")
        # 如果失败，尝试渲染简化版模板
        try:
            return render_template('simple_index.html')
        except Exception as e2:
            print(f"Error rendering simple_index.html: {e2}")
            # 如果两者都失败，返回基本HTML
            return """
            <html>
            <head>
                <title>小说云</title>
                <link rel="stylesheet" href="/static/css/style.css">
            </head>
            <body>
                <h1>小说云</h1>
                <ul>
                    <li><a href="/">首页</a></li>
                    <li><a href="/search">搜索</a></li>
                </ul>
                <form action="/search" method="post">
                    <input type="text" name="keyword" placeholder="输入小说名或作者">
                    <button type="submit">搜索</button>
                </form>
                
                <h2>已下载的小说</h2>
                <p>还没有下载过小说，请先<a href="/search">搜索小说</a>并下载</p>
                
                <footer>
                    小说云 © 2023
                </footer>
            </body>
            </html>
            """

@app.route('/simple')
def simple_index():
    return render_template('simple_index.html')

@app.route('/search', methods=['GET', 'POST'])
def search():
    if request.method == 'POST':
        keyword = request.form.get('keyword', '')
        if not keyword:
            return render_template('search.html', error="Please enter a search keyword")
        
        # Search in both sources
        fanqie_results = FanqieDownloader.search(keyword)
        qimao_results = QimaoDownloader.search(keyword)
        
        results = {
            'fanqie': fanqie_results,
            'qimao': qimao_results
        }
        
        return render_template('search.html', results=results, keyword=keyword)
    
    return render_template('search.html')

@app.route('/download', methods=['POST'])
def download_novel():
    novel_id = request.form.get('novel_id')
    source = request.form.get('source')
    
    if not novel_id or not source:
        return jsonify({'error': 'Missing novel_id or source'}), 400
    
    # Check if novel already exists
    if db.novel_exists(novel_id):
        return jsonify({'status': 'exists', 'message': 'Novel already downloaded'}), 200
    
    # Initialize download progress tracking
    download_progress[novel_id] = {
        'total': 0,
        'current': 0,
        'status': 'starting'
    }
    
    # Start download in a separate thread
    thread = threading.Thread(target=download_novel_task, args=(novel_id, source))
    thread.daemon = True
    thread.start()
    
    return jsonify({'status': 'started', 'novel_id': novel_id}), 200

def download_novel_task(novel_id, source):
    try:
        download_progress[novel_id]['status'] = 'downloading'
        
        if source == 'fanqie':
            downloader = FanqieDownloader(novel_id, db, download_progress)
            downloader.download()
        elif source == 'qimao':
            downloader = SwiftcatDownloader(novel_id, db, download_progress)
            downloader.download()
        else:
            download_progress[novel_id]['status'] = 'error'
            download_progress[novel_id]['message'] = 'Invalid source'
            return
        
        download_progress[novel_id]['status'] = 'completed'
    except Exception as e:
        download_progress[novel_id]['status'] = 'error'
        download_progress[novel_id]['message'] = str(e)

@app.route('/progress/<novel_id>')
def get_progress(novel_id):
    if novel_id in download_progress:
        return jsonify(download_progress[novel_id])
    return jsonify({'status': 'not_found'}), 404

@app.route('/novel/<novel_id>')
def view_novel(novel_id):
    novel = db.get_novel(novel_id)
    if not novel:
        return render_template('error.html', message='Novel not found')
    
    chapters = db.get_chapters(novel_id)
    return render_template('novel.html', novel=novel, chapters=chapters)

@app.route('/read/<novel_id>/<int:chapter_id>')
def read_chapter(novel_id, chapter_id):
    novel = db.get_novel(novel_id)
    if not novel:
        return render_template('error.html', message='Novel not found')
    
    chapter = db.get_chapter(novel_id, chapter_id)
    if not chapter:
        return render_template('error.html', message='Chapter not found')
    
    prev_chapter = db.get_chapter(novel_id, chapter_id - 1) if chapter_id > 1 else None
    next_chapter = db.get_chapter(novel_id, chapter_id + 1)
    
    return render_template('read.html', novel=novel, chapter=chapter, 
                          prev_chapter=prev_chapter, next_chapter=next_chapter)

@app.route('/create_manual_novel', methods=['POST'])
def create_manual_novel():
    title = request.form.get('title', '').strip()
    author = request.form.get('author', '').strip()
    description = request.form.get('description', '').strip()
    
    if not title:
        return jsonify({'status': 'error', 'message': '小说标题不能为空'}), 400
    
    try:
        # 创建手动小说
        novel_id = db.create_manual_novel(title, author, description)
        
        if novel_id:
            return jsonify({
                'status': 'success', 
                'message': f'小说《{title}》创建成功',
                'novel_id': novel_id
            }), 200
        else:
            return jsonify({'status': 'error', 'message': '创建小说失败，请重试'}), 500
            
    except Exception as e:
        print(f"Error creating manual novel: {e}")
        return jsonify({'status': 'error', 'message': f'创建失败: {str(e)}'}), 500

@app.route('/add_custom_chapter/<novel_id>', methods=['POST'])
def add_custom_chapter(novel_id):
    """Add a custom chapter to a novel"""
    novel = db.get_novel(novel_id)
    if not novel:
        return redirect(url_for('index'))
    
    title = request.form.get('title', '').strip()
    content = request.form.get('content', '').strip()
    
    if not title or not content:
        return redirect(url_for('view_novel', novel_id=novel_id))
    
    try:
        # 获取当前最大章节ID，生成新的章节ID
        max_chapter_id = db.get_max_chapter_id(novel_id)
        new_chapter_id = str(int(max_chapter_id) + 1)
        
        # 获取当前章节数量作为排序索引
        chapters = db.get_chapters(novel_id)
        order_index = len(chapters) + 1
        
        # 创建章节数据
        chapter_data = {
            'chapter_id': new_chapter_id,
            'title': title,
            'content': content,
            'order_index': order_index
        }
        
        # 保存章节
        success = db.save_chapter(novel_id, chapter_data)
        
        if success:
            # 更新小说的章节数量
            db.update_novel_chapter_count(novel_id)
        
        return redirect(url_for('view_novel', novel_id=novel_id))
        
    except Exception as e:
        print(f"Error adding custom chapter: {e}")
        return redirect(url_for('view_novel', novel_id=novel_id))

@app.route('/download_by_id', methods=['POST'])
def download_by_id():
    novel_id = request.form.get('novel_id')
    
    if not novel_id:
        return jsonify({'status': 'error', 'message': 'Missing novel ID. Please provide a valid novel ID.'}), 400
    
    # Validate that novel_id is a number
    try:
        int(novel_id)
    except ValueError:
        return jsonify({'status': 'error', 'message': 'Invalid novel ID format. Please provide a valid numeric ID.'}), 400
    
    # Check if novel already exists
    if db.novel_exists(novel_id):
        novel = db.get_novel(novel_id)
        return jsonify({
            'status': 'exists', 
            'message': f'Novel already downloaded: {novel.get("title", "Unknown")}',
            'novel_id': novel_id
        }), 200
    
    # Initialize download progress tracking
    download_progress[novel_id] = {
        'total': 0,
        'current': 0,
        'status': 'starting',
        'message': 'Download process starting...'
    }
    
    # Start download in a separate thread, trying both sources
    thread = threading.Thread(target=download_by_id_task, args=(novel_id,))
    thread.daemon = True
    thread.start()
    
    return jsonify({
        'status': 'started', 
        'message': 'Download started. Please check the progress for updates.',
        'novel_id': novel_id
    }), 200

def download_by_id_task(novel_id):
    try:
        download_progress[novel_id]['status'] = 'downloading'
        
        # First determine if the novel id is a valid number
        try:
            int(novel_id)  # Attempt to convert to integer to validate
        except ValueError:
            download_progress[novel_id]['status'] = 'error'
            download_progress[novel_id]['message'] = 'Invalid novel ID format. Please provide a valid numeric ID.'
            return
        
        error_messages = []
        
        # Try Fanqie first
        fanqie_downloader = FanqieDownloader(novel_id, db, download_progress)
        try:
            print(f"Attempting to download novel ID {novel_id} from Fanqie...")
            novel_info = fanqie_downloader.get_novel_info()
            if novel_info:
                # Novel exists in Fanqie, download it
                print(f"Novel found in Fanqie, starting download: {novel_info.get('title', 'Unknown title')}")
                download_success = fanqie_downloader.download()
                if download_success:
                    download_progress[novel_id]['status'] = 'completed'
                    download_progress[novel_id]['message'] = f"Successfully downloaded from Fanqie: {novel_info.get('title', 'Unknown title')}"
                    return
                else:
                    error_messages.append("Fanqie download process failed")
            else:
                error_messages.append("Novel not found in Fanqie")
        except Exception as e:
            error_messages.append(f"Fanqie download error: {str(e)}")
            print(f"Failed to download from Fanqie: {e}")
        
        # If Fanqie fails, try QiMao
        try:
            print(f"Attempting to download novel ID {novel_id} from QiMao...")
            qimao_downloader = SwiftcatDownloader(novel_id, db, download_progress)
            novel_info = qimao_downloader.get_novel_info()
            if novel_info:
                # Novel exists in QiMao, download it
                print(f"Novel found in QiMao, starting download: {novel_info.get('title', 'Unknown title')}")
                download_success = qimao_downloader.download()
                if download_success:
                    download_progress[novel_id]['status'] = 'completed'
                    download_progress[novel_id]['message'] = f"Successfully downloaded from QiMao: {novel_info.get('title', 'Unknown title')}"
                    return
                else:
                    error_messages.append("QiMao download process failed")
            else:
                error_messages.append("Novel not found in QiMao")
        except Exception as e:
            error_messages.append(f"QiMao download error: {str(e)}")
            print(f"Failed to download from QiMao: {e}")
        
        # If we reach here, both download attempts failed
        download_progress[novel_id]['status'] = 'error'
        download_progress[novel_id]['message'] = f"Failed to download novel: {'; '.join(error_messages)}"
        
    except Exception as e:
        download_progress[novel_id]['status'] = 'error'
        download_progress[novel_id]['message'] = f"Unexpected error: {str(e)}"

@app.route('/update_novel/<novel_id>', methods=['POST'])
def update_novel(novel_id):
    novel = db.get_novel(novel_id)
    if not novel:
        return jsonify({'status': 'error', 'message': 'Novel not found'}), 404
    
    source = novel.get('source')
    if not source:
        return jsonify({'status': 'error', 'message': 'Novel source not found'}), 400
    
    # Initialize update progress tracking
    update_progress[novel_id] = {
        'total': 0,
        'current': 0,
        'status': 'starting',
        'message': 'Update process starting...'
    }
    
    # Start update in a separate thread
    thread = threading.Thread(target=update_novel_task, args=(novel_id, source))
    thread.daemon = True
    thread.start()
    
    return jsonify({'status': 'started', 'novel_id': novel_id}), 200

@app.route('/delete_novel/<novel_id>', methods=['POST'])
def delete_novel(novel_id):
    """Delete a novel and all its related data"""
    try:
        novel = db.get_novel(novel_id)
        if not novel:
            return jsonify({'status': 'error', 'message': '小说不存在'}), 404
        
        # Stop any running scheduled tasks for this novel
        tasks_to_stop = []
        for task_id, thread in scheduled_task_threads.items():
            if hasattr(thread, 'novel_id') and thread.novel_id == novel_id:
                tasks_to_stop.append(task_id)
        
        for task_id in tasks_to_stop:
            stop_task(task_id)
        
        # Delete the novel and all related data
        success = db.delete_novel(novel_id)
        
        if success:
            return jsonify({'status': 'success', 'message': f'小说《{novel.get("title", "未知")}》已成功删除'}), 200
        else:
            return jsonify({'status': 'error', 'message': '删除失败，小说可能不存在'}), 404
            
    except Exception as e:
        print(f"Error deleting novel {novel_id}: {e}")
        return jsonify({'status': 'error', 'message': f'删除失败: {str(e)}'}), 500

@app.route('/update_progress/<novel_id>')
def get_update_progress(novel_id):
    if novel_id in update_progress:
        return jsonify(update_progress[novel_id])
    return jsonify({'status': 'not_found'}), 404

def update_novel_task(novel_id, source):
    try:
        update_progress[novel_id]['status'] = 'updating'
        
        if source == 'fanqie':
            downloader = FanqieDownloader(novel_id, db, update_progress)
            downloader.update()
        elif source == 'qimao':
            # downloader = QimaoDownloader(novel_id, db, update_progress)
            # downloader.update()
            downloader =  SwiftcatDownloader(novel_id, db, update_progress)
            downloader.update()
        elif source == 'swiftcat':
            downloader = SwiftcatDownloader(novel_id, db, update_progress)
            downloader.update()
        else:
            update_progress[novel_id]['status'] = 'error'
            update_progress[novel_id]['message'] = 'Invalid source'
            return
        
        update_progress[novel_id]['status'] = 'completed'
    except Exception as e:
        update_progress[novel_id]['status'] = 'error'
        update_progress[novel_id]['message'] = str(e)

# 定时更新相关路由和功能
@app.route('/scheduled_updates')
def scheduled_updates():
    novels = db.get_all_novels()
    tasks = db.get_all_scheduled_tasks()
    return render_template('scheduled_updates.html', novels=novels, tasks=tasks)

@app.route('/add_scheduled_task', methods=['POST'])
def add_scheduled_task():
    novel_id = request.form.get('novel_id')
    schedule_time = request.form.get('schedule_time')
    interval_seconds = request.form.get('interval_seconds')
    auto_generate_video = request.form.get('auto_generate_video', '0') == '1'
    
    if not novel_id or not schedule_time or not interval_seconds:
        return redirect(url_for('scheduled_updates'))
    
    # 转换datetime-local格式到SQLite兼容的格式
    schedule_time = schedule_time.replace('T', ' ')
    
    # 将interval_seconds转换为整数
    try:
        interval_seconds = int(interval_seconds)
        if interval_seconds < 30:
            interval_seconds = 30  # 设置最小间隔为30秒
    except ValueError:
        interval_seconds = 3600  # 默认为1小时
    
    # 添加任务到数据库
    task_id = db.add_scheduled_task(novel_id, schedule_time, interval_seconds, auto_generate_video=auto_generate_video)
    
    # 检查是否需要立即启动任务
    check_scheduled_tasks()
    
    return redirect(url_for('scheduled_updates'))

@app.route('/update_task', methods=['POST'])
def update_task():
    task_id = request.form.get('task_id')
    schedule_time = request.form.get('schedule_time')
    interval_seconds = request.form.get('interval_seconds')
    auto_generate_video = request.form.get('auto_generate_video', '0') == '1'
    
    if not task_id or not schedule_time or not interval_seconds:
        return redirect(url_for('scheduled_updates'))
    
    # 转换datetime-local格式到SQLite兼容的格式
    schedule_time = schedule_time.replace('T', ' ')
    
    # 将interval_seconds转换为整数
    try:
        interval_seconds = int(interval_seconds)
        if interval_seconds < 30:
            interval_seconds = 30  # 设置最小间隔为30秒
    except ValueError:
        interval_seconds = 3600  # 默认为1小时
    
    # 更新任务
    db.update_scheduled_task(task_id, schedule_time, interval_seconds, auto_generate_video=auto_generate_video)
    
    # 如果任务正在运行，需要停止并重启
    if task_id in scheduled_task_threads and scheduled_task_threads[task_id].is_alive():
        stop_task(task_id)
    
    # 检查是否需要重新启动任务
    check_scheduled_tasks()
    
    return redirect(url_for('scheduled_updates'))

@app.route('/toggle_task', methods=['POST'])
def toggle_task():
    task_id = request.form.get('task_id')
    is_active = request.form.get('is_active')
    
    if not task_id:
        return redirect(url_for('scheduled_updates'))
    
    # 转换is_active为布尔值
    is_active = bool(int(is_active)) if is_active else None
    
    # 更新任务状态
    db.update_scheduled_task(task_id, is_active=is_active)
    
    # 如果设置为非活动状态，停止任务
    if not is_active and task_id in scheduled_task_threads:
        stop_task(task_id)
    # 如果设置为活动状态，检查是否需要启动任务
    elif is_active:
        check_scheduled_tasks()
    
    return redirect(url_for('scheduled_updates'))

@app.route('/delete_task', methods=['POST'])
def delete_task():
    task_id = request.form.get('task_id')
    
    if not task_id:
        return redirect(url_for('scheduled_updates'))
    
    # 如果任务正在运行，先停止
    if task_id in scheduled_task_threads:
        stop_task(task_id)
    
    # 删除任务
    db.delete_scheduled_task(task_id)
    
    return redirect(url_for('scheduled_updates'))

def stop_task(task_id):
    """停止指定的任务线程"""
    if task_id in scheduled_task_threads:
        # 这里并不能真正停止线程，但可以设置一个标志位让线程自己退出
        if hasattr(scheduled_task_threads[task_id], 'stop'):
            scheduled_task_threads[task_id].stop = True
        # 从字典中移除线程引用
        del scheduled_task_threads[task_id]

def stop_all_tasks():
    """停止所有任务线程"""
    for task_id in list(scheduled_task_threads.keys()):
        stop_task(task_id)

def check_scheduled_tasks():
    """检查并启动需要运行的定时任务"""
    active_tasks = db.get_active_scheduled_tasks()
    now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    for task in active_tasks:
        task_id = str(task['id'])
        
        # 如果任务已经在运行，跳过
        if task_id in scheduled_task_threads and scheduled_task_threads[task_id].is_alive():
            continue
        
        # 检查是否应该启动任务
        schedule_time = task['schedule_time']
        
        if now >= schedule_time:
            # 启动任务
            start_scheduled_task(task)

def start_scheduled_task(task):
    """启动定时任务"""
    task_id = str(task['id'])
    novel_id = task['novel_id']
    novel_source = task['novel_source']
    interval_seconds = task['interval_seconds']
    auto_generate_video = task.get('auto_generate_video', 0) == 1  # 从数据库读取设置
    
    # 创建任务线程
    thread = ScheduledUpdateThread(task_id, novel_id, novel_source, interval_seconds, db, auto_generate_video)
    thread.daemon = True
    thread.start()
    
    # 记录线程引用
    scheduled_task_threads[task_id] = thread

class ScheduledUpdateThread(threading.Thread):
    """用于定时更新小说的线程类"""
    
    def __init__(self, task_id, novel_id, novel_source, interval_seconds, db, auto_generate_video=True):
        super().__init__()
        self.task_id = task_id
        self.novel_id = novel_id
        self.novel_source = novel_source
        self.interval_seconds = interval_seconds
        self.db = db
        self.stop = False  # 停止标志
        self.paused = False  # 暂停标志
        self.paused_lock = threading.Condition(threading.Lock())  # 用于暂停和恢复的锁
        self.auto_generate_video = auto_generate_video  # 是否在更新后自动生成视频
    
    def run(self):
        global is_generating_video
        while not self.stop:
            # 检查是否暂停
            with self.paused_lock:
                if self.paused:
                    # 等待恢复信号
                    self.paused_lock.wait()
            
            if self.stop:
                break
            
            # 检查是否有视频正在生成中，如果有则等待
            while is_generating_video and not self.stop and not self.paused:
                print(f"任务 {self.task_id} 等待视频生成完成...")
                time.sleep(10)
            
            try:
                # 记录执行时间
                now = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self.db.update_task_last_run_time(self.task_id, now)
                
                # 初始化下载器
                if self.novel_source == 'fanqie':
                    downloader = FanqieDownloader(self.novel_id, self.db, update_progress)
                elif self.novel_source == 'qimao':
                    downloader = QimaoDownloader(self.novel_id, self.db, update_progress)
                else:
                    print(f"不支持的小说源: {self.novel_source}")
                    return
                
                # 记录更新前的状态
                chapters_before = self.db.get_chapters(self.novel_id)
                chapter_ids_before = {str(chapter['chapter_id']) for chapter in chapters_before}
                
                # 获取线上章节信息
                online_chapters = downloader.get_chapters()
                if not online_chapters:
                    print(f"无法获取小说 {self.novel_id} 的线上章节信息")
                    return
                
                print(f"小说 {self.novel_id} 当前状态: 本地章节 {len(chapters_before)} 章，线上章节 {len(online_chapters)} 章")
                
                # 执行更新
                has_updates = downloader.update()
                
                # 检查更新后的状态
                chapters_after = self.db.get_chapters(self.novel_id)
                chapter_ids_after = {str(chapter['chapter_id']) for chapter in chapters_after}
                
                # 计算新增的章节ID
                new_chapter_ids = chapter_ids_after - chapter_ids_before
                updated_chapter_ids = sorted(new_chapter_ids)  # 按字符串排序
                
                # 如果有更新，记录更新信息
                if has_updates and updated_chapter_ids:
                    print(f"检测到小说 {self.novel_id} 有更新，共 {len(updated_chapter_ids)} 章")
                    
                    # 将新章节添加到待更新表中
                    for chapter_id in updated_chapter_ids:
                        self.db.add_pending_chapter_update(self.novel_id, chapter_id)
                    
                    print(f"已将 {len(updated_chapter_ids)} 章添加到待更新列表")
                    
                    # 检查是否需要处理待更新章节
                    self.check_and_process_pending_updates(1)
                else:
                    # 即使没有新章节，也检查是否有待处理的更新
                    self.check_and_process_pending_updates(1)
                
            except Exception as e:
                print(f"Error updating novel {self.novel_id}: {e}")
            
            # 等待下一次执行
            wait_start = time.time()
            while time.time() - wait_start < self.interval_seconds:
                # 检查是否需要停止
                if self.stop:
                    break
                
                # 检查是否暂停
                with self.paused_lock:
                    if self.paused:
                        # 等待恢复信号
                        self.paused_lock.wait()
                
                # 短暂休眠，避免CPU占用过高
                time.sleep(1)
    
    def check_and_process_pending_updates(self, timeout_minutes=10):
        """检查并处理待更新的章节，如果超过指定时间没有新更新则处理"""
        try:
            # 检查是否有待处理的章节
            pending_updates = self.db.get_pending_chapter_updates(self.novel_id)
            if not pending_updates:
                print(f"小说 {self.novel_id} 没有待处理的章节更新")
                return
            
            print(f"小说 {self.novel_id} 有 {len(pending_updates)} 章待处理的更新")
            
            # 检查是否超过超时时间没有新更新
            timed_out = self.db.check_pending_updates_timeout(self.novel_id, timeout_minutes)
            
            if not timed_out:
                print(f"小说 {self.novel_id} 的更新仍在进行中，等待更多章节...")
                return
            
            print(f"小说 {self.novel_id} 的更新已经 {timeout_minutes} 分钟没有新章节，开始处理待更新章节...")
            
            # 获取所有待处理的章节ID
            chapter_ids = [update['chapter_id'] for update in pending_updates]
            
            if self.auto_generate_video:
                # 获取关联的视频项目
                projects = self.db.get_video_projects_by_novel(self.novel_id)
                
                if not projects:
                    print(f"小说 {self.novel_id} 没有关联的视频项目")
                    # 将所有章节标记为已处理
                    for chapter_id in chapter_ids:
                        self.db.update_pending_chapter_status(self.novel_id, chapter_id, 'processed')
                    self.db.clear_pending_chapter_updates(self.novel_id, 'processed')
                    return
                
                # 为每个关联的项目更新章节
                for project in projects:
                    project_id = project['id']
                    print(f"更新项目 {project_id} 的章节...")
                    
                    # 清理项目中现有的章节
                    self.db.clear_project_chapters(project_id)
                    
                    # 添加新章节到项目
                    self.db.add_chapters_to_project(project_id, self.novel_id, chapter_ids)
                    print(f"已为项目 {project_id} 添加 {len(chapter_ids)} 章内容")
                
                # 将所有章节标记为已处理
                for chapter_id in chapter_ids:
                    self.db.update_pending_chapter_status(self.novel_id, chapter_id, 'processed')
                
                # 清理已处理的章节
                self.db.clear_pending_chapter_updates(self.novel_id, 'processed')
                
                # 准备生成视频
                self.prepare_video_generation()
            else:
                print(f"自动视频生成已禁用，仅清理待更新章节")
                # 将所有章节标记为已处理
                for chapter_id in chapter_ids:
                    self.db.update_pending_chapter_status(self.novel_id, chapter_id, 'processed')
                self.db.clear_pending_chapter_updates(self.novel_id, 'processed')
        
        except Exception as e:
            print(f"处理待更新章节时出错: {e}")
            import traceback
            traceback.print_exc()
    
    def wait_for_update_completion(self, wait_seconds):
        """等待指定的秒数，确认更新是否完成"""
        # 记录开始等待的时间和章节数量
        start_time = time.time()
        local_chapters_count = len(self.db.get_chapters(self.novel_id))
        
        # 获取下载器实例以检查在线章节
        if self.novel_source == 'fanqie':
            downloader = FanqieDownloader(self.novel_id, self.db)
        elif self.novel_source == 'qimao':
            downloader = QimaoDownloader(self.novel_id, self.db)
        else:
            print(f"不支持的小说源: {self.novel_source}")
            return False
            
        # 获取在线章节总数
        online_chapters = downloader.get_chapters()
        if not online_chapters:
            print(f"无法获取在线章节信息，终止等待")
            return False
            
        online_chapters_count = len(online_chapters)
        print(f"小说 {self.novel_id} 当前状态: 本地章节 {local_chapters_count} 章，线上章节 {online_chapters_count} 章")
        
        # 如果本地章节数已经等于或超过线上章节数，则无需等待
        if local_chapters_count >= online_chapters_count:
            print(f"本地章节数 ({local_chapters_count}) 已经与线上章节数 ({online_chapters_count}) 同步，无需等待")
            return True
        
        # 每10秒检查一次是否有新章节
        check_interval = 10
        last_local_count = local_chapters_count
        
        while time.time() - start_time < wait_seconds:
            time.sleep(check_interval)
            
            # 检查是否需要停止
            if self.stop or self.paused:
                return False
            
            # 重新获取在线章节数量，检查是否有新更新
            new_online_chapters = downloader.get_chapters()
            new_online_count = len(new_online_chapters) if new_online_chapters else online_chapters_count
            
            # 检查本地章节数量
            current_local_count = len(self.db.get_chapters(self.novel_id))
            
            print(f"检查更新: 本地章节 {current_local_count}/{new_online_count} 章")
            
            # 如果线上章节数量增加，重置等待时间
            if new_online_count > online_chapters_count:
                print(f"检测到线上持续更新，章节数从 {online_chapters_count} 增加到 {new_online_count}，重置等待时间...")
                start_time = time.time()
                online_chapters_count = new_online_count
            
            # 如果本地章节数量增加，更新记录
            if current_local_count > last_local_count:
                print(f"本地章节数量从 {last_local_count} 增加到 {current_local_count}")
                last_local_count = current_local_count
            
            # 如果本地章节数已经等于或接近线上章节数，可以提前结束等待
            if current_local_count >= online_chapters_count or \
               (online_chapters_count - current_local_count <= 2 and current_local_count > local_chapters_count + 5):
                print(f"本地章节数 ({current_local_count}) 已接近线上章节数 ({online_chapters_count})，提前结束等待")
                return True
        
        # 等待结束，确认更新状态
        final_local_count = len(self.db.get_chapters(self.novel_id))
        final_online_count = len(downloader.get_chapters()) if downloader.get_chapters() else online_chapters_count
        
        print(f"等待结束，小说 {self.novel_id} 更新状态: 本地章节 {final_local_count}/{final_online_count} 章")
        
        # 判断更新是否基本完成（允许有少量章节未下载，可能是因为权限问题）
        update_completed = final_local_count > local_chapters_count and \
                          (final_local_count >= final_online_count or \
                           final_online_count - final_local_count <= 3)
                           
        return update_completed
    
    def prepare_video_generation(self):
        """准备生成视频任务"""
        global is_generating_video, video_generation_lock
        
        # 查找与该小说关联的视频项目
        projects = self.db.get_video_projects_by_novel(self.novel_id)
        if not projects:
            print(f"没有与小说 {self.novel_id} 关联的视频项目")
            return
        
        # 获取更新章节的信息
        chapters = self.db.get_chapters(self.novel_id)
        
        # 按项目ID迭代，为每个关联的项目创建视频渲染任务
        for project in projects:
            project_id = project['id']
            
            with video_generation_lock:
                is_generating_video = True
                # 暂停所有更新任务
                pause_all_update_tasks()
                
                # 创建视频渲染任务
                try:
                    # 创建一个渲染任务标题
                    novel = self.db.get_novel(self.novel_id)
                    novel_title = novel['title'] if novel else "未知小说"
                    task_title = f"自动生成 - {novel_title} 更新后视频"
                    
                    # 创建渲染任务，传递小说ID
                    task_id = self.db.create_render_task(project_id, task_title, novel_id=self.novel_id)
                    print(f"已创建视频渲染任务 {task_id} 用于项目 {project_id}")
                    
                    # 启动渲染任务
                    render_thread = threading.Thread(
                        target=auto_render_video_after_update,
                        args=(task_id, self.novel_id, project_id)
                    )
                    render_thread.daemon = True
                    render_thread.start()
                except Exception as e:
                    print(f"创建视频渲染任务失败: {e}")
                    # 恢复更新任务
                    resume_all_update_tasks()
                    is_generating_video = False
    
    def pause(self):
        """暂停线程执行"""
        with self.paused_lock:
            self.paused = True
    
    def resume(self):
        """恢复线程执行"""
        with self.paused_lock:
            self.paused = False
            self.paused_lock.notify()  # 通知等待的线程

@app.route('/pause_task/<task_id>', methods=['POST'])
def pause_task(task_id):
    """暂停一个正在运行的任务"""
    if task_id in scheduled_task_threads and hasattr(scheduled_task_threads[task_id], 'pause'):
        scheduled_task_threads[task_id].pause()
        return jsonify({'status': 'success', 'message': 'Task paused'})
    return jsonify({'status': 'error', 'message': 'Task not found or cannot be paused'}), 404

@app.route('/resume_task/<task_id>', methods=['POST'])
def resume_task(task_id):
    """恢复一个暂停的任务"""
    if task_id in scheduled_task_threads and hasattr(scheduled_task_threads[task_id], 'resume'):
        scheduled_task_threads[task_id].resume()
        return jsonify({'status': 'success', 'message': 'Task resumed'})
    return jsonify({'status': 'error', 'message': 'Task not found or cannot be resumed'}), 404

@app.route('/task_status/<task_id>')
def task_status(task_id):
    """获取任务状态"""
    if task_id in scheduled_task_threads:
        thread = scheduled_task_threads[task_id]
        return jsonify({
            'status': 'running',
            'paused': thread.paused if hasattr(thread, 'paused') else False
        })
    return jsonify({'status': 'not_running'})

# 启动定时任务调度器线程
def start_scheduler():
    """启动定时任务调度器"""
    thread = threading.Thread(target=scheduler_thread)
    thread.daemon = True
    thread.start()
    return thread

def scheduler_thread():
    """定时任务调度器线程"""
    while scheduler_running:
        try:
            # 检查并启动需要运行的任务
            check_scheduled_tasks()
        except Exception as e:
            print(f"Error in scheduler thread: {e}")
        
        # 每分钟检查一次
        time.sleep(60)

# 应用启动时启动调度器
@app.before_first_request
def before_first_request():
    start_scheduler()

# 应用退出时清理资源
import atexit

@atexit.register
def cleanup():
    global scheduler_running
    scheduler_running = False
    stop_all_tasks()

@app.route('/fanqie_cookie', methods=['GET', 'POST'])
def fanqie_cookie():
    if request.method == 'POST':
        try:
            cookie_json = request.form.get('cookie_json', '')
            if not cookie_json:
                return jsonify({'status': 'error', 'message': '没有提供Cookie数据'}), 400
            
            # 验证JSON格式
            try:
                # Replace JSON booleans with Python booleans
              
                cookie_data = json.loads(cookie_json)
                if not isinstance(cookie_data, list):
                    return jsonify({'status': 'error', 'message': 'Cookie格式错误，应为JSON数组'}), 400
                
                # 验证格式
                valid = True
                for cookie in cookie_data:
                    if not isinstance(cookie, dict) or 'name' not in cookie or 'value' not in cookie:
                        valid = False
                        break
                
                if not valid:
                    return jsonify({'status': 'error', 'message': 'Cookie格式错误，每个条目应包含name和value属性'}), 400
                
            except json.JSONDecodeError:
                return jsonify({'status': 'error', 'message': '无效的JSON格式'}), 400
            
           
            # 保存到数据库
            db.set_fanqie_cookie(cookie_json)
            
            return jsonify({'status': 'success', 'message': '番茄Cookie配置已保存'}), 200
        except Exception as e:
            return jsonify({'status': 'error', 'message': f'保存Cookie失败: {str(e)}'}), 500
    
    # GET请求返回当前配置
    current_cookie = db.get_fanqie_cookie()
    return jsonify({
        'status': 'success', 
        'has_cookie': bool(current_cookie), 
        'cookie': current_cookie or ''
    })

@app.route('/delete_chapter/<novel_id>/<chapter_id>', methods=['POST'])
def delete_chapter(novel_id, chapter_id):
    """Delete a specific chapter from a novel"""
    try:
        # Get the novel and chapter to validate they exist
        novel = db.get_novel(novel_id)
        if not novel:
            return jsonify({'status': 'error', 'message': 'Novel not found'}), 404
        
        chapter = db.get_chapter(novel_id, chapter_id)
        if not chapter:
            return jsonify({'status': 'error', 'message': 'Chapter not found'}), 404
        
        # Delete the chapter from the database
        success = db.delete_chapter(novel_id, chapter_id)
        
        if success:
            # Redirect to the novel page after deletion
            return redirect(url_for('view_novel', novel_id=novel_id))
        else:
            return jsonify({'status': 'error', 'message': 'Failed to delete chapter'}), 500
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500



# Novel to video routes
@app.route('/novel_to_video')
def novel_to_video():
    novels = db.get_all_novels()
    projects = db.get_all_video_projects()
    return render_template('novel_to_video.html', novels=novels, projects=projects)

@app.route('/create_video_project', methods=['POST'])
def create_video_project():
    title = request.form.get('title')
    novel_id = request.form.get('novel_id') or None
    
    if not title:
        return jsonify({'status': 'error', 'message': 'Project title is required'}), 400
    
    try:
        project_id = db.create_video_project(title, novel_id)
        # Initialize default settings
        db.update_video_settings(project_id, {
            'voice_id': 'zh-CN-XiaoxiaoNeural',
            'voice_rate': 1.0,
            'voice_volume': 1.0,
            'subtitle_font': 'SimHei',
            'subtitle_size': 40,
            'subtitle_color': '#FFFFFF',
            'subtitle_position': 'bottom',
            'title_text': title,
            'title_font': 'SimHei',
            'title_size': 60,
            'title_color': '#FFFFFF',
            'title_position': 'top',
            'video_width': 1280,
            'video_height': 720,
            'use_gpu': 1
        })
        return redirect(url_for('edit_video_project', project_id=project_id))
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/edit_video_project/<int:project_id>')
def edit_video_project(project_id):
    active_tab = request.args.get('tab', 'chapters')
    
    project = db.get_video_project(project_id)
    if not project:
        return render_template('error.html', message='Project not found')
    
    settings = db.get_video_settings(project_id)
    social_media_settings = db.get_social_media_settings(project_id, 'douyin')
    novel = None
    novel_chapters = []
    project_chapters = db.get_project_chapters(project_id)
    selected_chapter_ids = [chapter['chapter_id'] for chapter in project_chapters]
    
    if project['novel_id']:
        novel = db.get_novel(project['novel_id'])
        if novel:
            novel_chapters = db.get_chapters(project['novel_id'])
    
    tts_voices = db.get_all_tts_voices()
    if not tts_voices:
        # Initialize default voices if none exist
        initialize_tts_voices()
        tts_voices = db.get_all_tts_voices()
    
    replacement_rules = db.get_all_text_replacements_with_projectid(project_id)
    render_history = db.get_render_history(project_id)
    upload_history = db.get_upload_history(project_id)
    uploaded_videos = db.get_all_uploaded_videos()  # 获取所有上传的视频
    
    # 从设置中提取视频宽度和高度，或使用默认值
    video_width = settings.get('video_width', 1280) if settings else 1280
    video_height = settings.get('video_height', 720) if settings else 720
    
    return render_template('edit_video_project.html', 
                          project=project,
                          settings=settings,
                          social_media_settings=social_media_settings,
                          novel=novel,
                          novel_chapters=novel_chapters,
                          project_chapters=project_chapters,
                          selected_chapter_ids=selected_chapter_ids,
                          tts_voices=tts_voices,
                          replacement_rules=replacement_rules,
                          render_history=render_history,
                          upload_history=upload_history,
                          uploaded_videos=uploaded_videos,  # 传递上传视频列表
                          active_tab=active_tab,
                          video_width=video_width,
                          video_height=video_height)

@app.route('/update_project_chapters/<int:project_id>', methods=['POST'])
def update_project_chapters(project_id):
    chapter_ids = request.form.getlist('chapter_ids')
    
    project = db.get_video_project(project_id)
    if not project:
        return jsonify({'status': 'error', 'message': 'Project not found'}), 404
    
    # Clear existing chapters
    db.clear_project_chapters(project_id)
    
    # Add new selection
    if chapter_ids and project['novel_id']:
        db.add_chapters_to_project(project_id, project['novel_id'], chapter_ids)
    
    return redirect(url_for('edit_video_project', project_id=project_id, tab='chapters'))

@app.route('/update_video_voice/<int:project_id>', methods=['POST'])
def update_video_voice(project_id):
    voice_id = request.form.get('voice_id')
    voice_rate = float(request.form.get('voice_rate', 1.0))
    voice_volume = float(request.form.get('voice_volume', 1.0))
    
    settings = {
        'voice_id': voice_id,
        'voice_rate': voice_rate,
        'voice_volume': voice_volume
    }
    
    db.update_video_settings(project_id, settings)
    return redirect(url_for('edit_video_project', project_id=project_id, tab='voice'))

@app.route('/update_video_subtitle/<int:project_id>', methods=['POST'])
def update_video_subtitle(project_id):
    subtitle_font = request.form.get('subtitle_font')
    subtitle_size = int(request.form.get('subtitle_size', 40))
    subtitle_color = request.form.get('subtitle_color', '#FFFFFF')
    subtitle_position = request.form.get('subtitle_position', 'bottom')
    
    # 获取字幕自定义坐标
    subtitle_x = None
    subtitle_y = None
    if subtitle_position == 'custom':
        subtitle_x = request.form.get('subtitle_x')
        subtitle_y = request.form.get('subtitle_y')
        # 确保坐标值为整数
        if subtitle_x:
            subtitle_x = int(subtitle_x)
        if subtitle_y:
            subtitle_y = int(subtitle_y)
    
    title_text = request.form.get('title_text')
    title_font = request.form.get('title_font')
    title_size = int(request.form.get('title_size', 60))
    title_color = request.form.get('title_color', '#FFFFFF')
    title_position = request.form.get('title_position', 'top')
    
    # 获取标题自定义坐标
    title_x = None
    title_y = None
    if title_position == 'custom':
        title_x = request.form.get('title_x')
        title_y = request.form.get('title_y')
        # 确保坐标值为整数
        if title_x:
            title_x = int(title_x)
        if title_y:
            title_y = int(title_y)
    
    settings = {
        'subtitle_font': subtitle_font,
        'subtitle_size': subtitle_size,
        'subtitle_color': subtitle_color,
        'subtitle_position': subtitle_position,
        'title_text': title_text,
        'title_font': title_font,
        'title_size': title_size,
        'title_color': title_color,
        'title_position': title_position
    }
    
    # 如果有自定义坐标，添加到设置中
    if subtitle_x is not None:
        settings['subtitle_x'] = subtitle_x
    if subtitle_y is not None:
        settings['subtitle_y'] = subtitle_y
    if title_x is not None:
        settings['title_x'] = title_x
    if title_y is not None:
        settings['title_y'] = title_y
    
    db.update_video_settings(project_id, settings)
    return redirect(url_for('edit_video_project', project_id=project_id, tab='subtitle'))

@app.route('/update_video_music/<int:project_id>', methods=['POST'])
def update_video_music(project_id):
    bg_music_volume = float(request.form.get('bg_music_volume', 0.5))
    settings = {'bg_music_volume': bg_music_volume}
    
    # Handle file upload
    if 'bg_music' in request.files:
        bg_music = request.files['bg_music']
        if bg_music.filename:
            # Ensure the upload directory exists
            upload_dir = os.path.join(app.static_folder, 'uploads')
            os.makedirs(upload_dir, exist_ok=True)
            
            # Add timestamp to filename to ensure uniqueness
            timestamp = int(time.time())
            original_filename = secure_filename(bg_music.filename)
            # Insert timestamp before the file extension
            name, ext = os.path.splitext(original_filename)
            unique_filename = f"{name}_{timestamp}{ext}"
            
            filepath = os.path.join(upload_dir, unique_filename)
            bg_music.save(filepath)
            
            # Save the path to the database
            settings['bg_music_path'] = f'uploads/{unique_filename}'
    
    db.update_video_settings(project_id, settings)
    return redirect(url_for('edit_video_project', project_id=project_id, tab='music'))

@app.route('/update_social_media/<int:project_id>', methods=['POST'])
def update_social_media(project_id):
    # 获取表单数据
    enable_upload = 1 if request.form.get('enable_upload') else 0
    title = request.form.get('title', '')
    description = request.form.get('description', '')
    tags = request.form.get('tags', '')
    remove_cover = request.form.get('remove_cover', '0')
    title_on_cover = 1 if request.form.get('title_on_cover') else 0
    show_chapter_info = 1 if request.form.get('show_chapter_info') else 0
    
    # 准备设置字典
    settings = {
        'enable_upload': enable_upload,
        'title': title,
        'description': description,
        'tags': tags,
        'title_on_cover': title_on_cover,
        'show_chapter_info': show_chapter_info
    }
    
    # 处理字体和样式设置
    if title_on_cover:
        settings['title_font'] = request.form.get('title_font', 'SimHei')
        settings['title_size'] = int(request.form.get('title_size', 40))
        settings['title_color'] = request.form.get('title_color', '#FFFFFF')
        settings['title_position'] = request.form.get('title_position', 'top')
        
        if settings['title_position'] == 'custom':
            settings['title_x'] = int(request.form.get('title_x', 0)) if request.form.get('title_x') else None
            settings['title_y'] = int(request.form.get('title_y', 0)) if request.form.get('title_y') else None
    
    # 处理章节信息设置
    if show_chapter_info:
        settings['chapter_font'] = request.form.get('chapter_font', 'SimHei')
        settings['chapter_size'] = int(request.form.get('chapter_size', 30))
        settings['chapter_color'] = request.form.get('chapter_color', '#FFFFFF')
        settings['chapter_position'] = request.form.get('chapter_position', 'bottom')
        settings['chapter_info_text'] = request.form.get('chapter_info_text', '章节信息')
        
        if settings['chapter_position'] == 'custom':
            settings['chapter_x'] = int(request.form.get('chapter_x', 0)) if request.form.get('chapter_x') else None
            settings['chapter_y'] = int(request.form.get('chapter_y', 0)) if request.form.get('chapter_y') else None
    
    # 处理自定义封面上传
    if 'custom_cover' in request.files and request.files['custom_cover'].filename:
        custom_cover = request.files['custom_cover']
        if custom_cover and allowed_file(custom_cover.filename, {'jpg', 'jpeg', 'png', 'webp'}):
            # 获取当前设置以检查是否需要删除旧封面
            current_settings = db.get_social_media_settings(project_id, 'douyin')
            
            # 确保上传目录存在
            upload_dir = os.path.join(app.static_folder, 'uploads')
            os.makedirs(upload_dir, exist_ok=True)
            
            filename = secure_filename(f"{project_id}_douyin_cover_{int(time.time())}.{custom_cover.filename.rsplit('.', 1)[1].lower()}")
            upload_path = os.path.join(upload_dir, filename)
            custom_cover.save(upload_path)
            
            settings['custom_cover_path'] = f"uploads/{filename}"
            
            # 删除旧封面
            if current_settings and current_settings.get('custom_cover_path'):
                try:
                    old_cover_path = os.path.join(app.static_folder, current_settings['custom_cover_path'].replace('uploads/', ''))
                    if os.path.exists(old_cover_path):
                        os.remove(old_cover_path)
                except Exception as e:
                    print(f"Error removing old cover: {e}")
    
    # 如果请求删除现有封面
    if remove_cover == '1':
        current_settings = db.get_social_media_settings(project_id, 'douyin')
        if current_settings and current_settings.get('custom_cover_path'):
            try:
                cover_path = os.path.join(app.static_folder, current_settings['custom_cover_path'].replace('uploads/', ''))
                if os.path.exists(cover_path):
                    os.remove(cover_path)
            except Exception as e:
                print(f"Error removing cover: {e}")
            
            settings['custom_cover_path'] = None
    
    # 更新设置
    db.update_social_media_settings(project_id, 'douyin', settings)
    
    # 重定向回编辑页面
    return redirect(url_for('edit_video_project', project_id=project_id, tab='social_media'))

@app.route('/update_render_settings/<int:project_id>', methods=['POST'])
def update_render_settings(project_id):
    video_width = int(request.form.get('video_width', 1280))
    video_height = int(request.form.get('video_height', 720))
    use_gpu = 1 if request.form.get('use_gpu') else 0
    subtitle_api = request.form.get('subtitle_api', 'jianying')  # 添加字幕API选择
    
    settings = {
        'video_width': video_width,
        'video_height': video_height,
        'use_gpu': use_gpu,
        'subtitle_api': subtitle_api  # 保存字幕API设置
    }
    
    # 处理从上传视频列表中选择背景视频
    bg_video_id = request.form.get('bg_video_id')
    if bg_video_id:
        selected_video = db.get_uploaded_video(int(bg_video_id))
        if selected_video:
            settings['bg_video_path'] = selected_video['file_path']
            settings['bg_video_thumbnail'] = selected_video['thumbnail_path']
            print(f"已选择背景视频: {selected_video['name']}")
    
    # 处理背景视频上传（如果没有选择现有视频）
    elif 'bg_video' in request.files:
        bg_video = request.files['bg_video']
        if bg_video.filename:
            # 确保上传目录存在
            upload_dir = app.config['VIDEO_UPLOAD_FOLDER']
            os.makedirs(upload_dir, exist_ok=True)
            
            # 保存文件
            timestamp = int(time.time())
            filename = secure_filename(bg_video.filename)
            filename = f"{timestamp}_{filename}"  # 添加时间戳避免文件名冲突
            filepath = os.path.join(upload_dir, filename)
            bg_video.save(filepath)
            
            # 获取文件信息
            file_size = os.path.getsize(filepath)
            duration, width, height = get_video_info(filepath)
            
            # 生成缩略图
            thumbnail_path = generate_video_thumbnail(filepath, upload_dir)
            
            # 自动保存到上传视频数据库
            video_name = os.path.splitext(bg_video.filename)[0]
            db.save_uploaded_video(
                name=video_name,
                filename=filename,
                file_path=filepath,
                file_size=file_size,
                duration=duration,
                width=width,
                height=height,
                thumbnail_path=thumbnail_path
            )
            
            settings['bg_video_path'] = filepath
            settings['bg_video_thumbnail'] = thumbnail_path
            print(f"已上传并保存背景视频: {video_name}")
    
    # 处理移除背景视频的请求
    if request.form.get('remove_bg_video') == '1':
        settings['bg_video_path'] = None
        # 同时移除缩略图
        settings['bg_video_thumbnail'] = None
    
    db.update_video_settings(project_id, settings)
    return redirect(url_for('edit_video_project', project_id=project_id, tab='render'))

@app.route('/render_video/<int:project_id>', methods=['POST'])
def render_video(project_id):
    project = db.get_video_project(project_id)
    if not project:
        return jsonify({'status': 'error', 'message': 'Project not found'}), 404
    
    # Create a new render task instead of immediate rendering
    task_title = f"渲染 - {project['title']}"
    # 从项目中获取小说ID和章节ID信息
    project_chapters = db.get_project_chapters(project_id)
    novel_id = project_chapters[0]['novel_id'] if project_chapters else None
    chapter_ids = [ch['chapter_id'] for ch in project_chapters] if project_chapters else None
    task_id = db.create_render_task(project_id, task_title, novel_id=novel_id, chapter_ids=chapter_ids)
    
    # 检查是否有需要立即启动的任务
    start_next_pending_task()
    
    return jsonify({'status': 'started', 'task_id': task_id})

@app.route('/render_progress/<int:render_id>')
def render_progress(render_id):
    status = db.get_render_status(render_id)
    if not status:
        return jsonify({'status': 'not_found'}), 404
    
    return jsonify(status)

@app.route('/download_video/<int:render_id>')
def download_video(render_id):
    status = db.get_render_status(render_id)
    if not status or status['status'] != 'completed' or not status['output_path']:
        return render_template('error.html', message='Video not found or not ready for download')
    
    return send_file(status['output_path'], as_attachment=True)

@app.route('/delete_video_project/<int:project_id>', methods=['POST'])
def delete_video_project(project_id):
    try:
        db.delete_video_project(project_id)
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/add_text_replacement/<int:project_id>', methods=['POST'])
def add_text_replacement(project_id):
    original_text = request.form.get('original_text')
    replacement_text = request.form.get('replacement_text')
    
    if not original_text or not replacement_text:
        return jsonify({'status': 'error', 'message': 'Both original and replacement text are required'}), 400
    
    try:
        db.add_text_replacement(original_text, replacement_text,project_id)
        # Redirect back to the previous page
        return redirect(request.referrer or url_for('novel_to_video'))
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/delete_text_replacement/<int:rule_id>', methods=['POST'])
def delete_text_replacement(rule_id):
    try:
        db.delete_text_replacement(rule_id)
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/initialize_tts_voices')
def init_tts_voices_route():
    """手动初始化TTS语音列表的路由"""
    try:
        # 获取当前语音数量
        current_voices = db.get_all_tts_voices()
        count_before = len(current_voices)
        
        # 调用初始化函数
        initialize_tts_voices()
        
        # 再次获取语音以确认已添加
        updated_voices = db.get_all_tts_voices()
        count_after = len(updated_voices)
        
        # 尝试从 edge-tts 获取最新可用语音
        try:
            import asyncio
            
            async def get_edge_tts_voices():
                try:
                    voices = await edge_tts.list_voices()
                    return voices
                except Exception as e:
                    print(f"获取edge-tts语音失败: {e}")
                    return []
            
            voice_list = asyncio.run(get_edge_tts_voices())
            added_count = 0
            
            for voice in voice_list:
                if 'zh-CN' in voice['ShortName']:
                    display_name = voice['ShortName'].replace('zh-CN-', '').replace('Neural', '')
                    voice_id = voice['ShortName']
                    language = '中文'
                    gender = voice.get('Gender', '未知')
                    if db.add_tts_voice(voice_id, display_name, language, gender):
                        added_count += 1
        except Exception as e:
            print(f"从edge-tts获取语音失败: {e}")
        
        # 最终检查语音数量
        final_voices = db.get_all_tts_voices()
        count_final = len(final_voices)
        
        return jsonify({
            'status': 'success',
            'message': f'语音初始化成功，原有语音: {count_before}，初始化后: {count_after}，最终: {count_final}，从Edge-TTS获取了{added_count}个新语音',
            'voices': [
                {
                    'id': v['id'],
                    'voice_id': v['voice_id'],
                    'display_name': v['display_name'],
                    'language': v['language'],
                    'gender': v['gender']
                } for v in final_voices
            ]
        })
    except Exception as e:
        return jsonify({'status': 'error', 'message': f'初始化语音失败: {str(e)}'}), 500

def initialize_tts_voices():
    """Initialize default TTS voices in the database"""
    # Add some default Microsoft Edge TTS voices
    voices = [
        {'voice_id': 'zh-CN-XiaoxiaoNeural', 'display_name': '晓晓', 'language': '中文', 'gender': '女'},
        {'voice_id': 'zh-CN-YunxiNeural', 'display_name': '云希', 'language': '中文', 'gender': '男'},
        {'voice_id': 'zh-CN-YunyangNeural', 'display_name': '云扬', 'language': '中文', 'gender': '男'},
        {'voice_id': 'zh-CN-XiaochenNeural', 'display_name': '晓辰', 'language': '中文', 'gender': '女'},
        {'voice_id': 'zh-CN-XiaohanNeural', 'display_name': '晓涵', 'language': '中文', 'gender': '女'},
        {'voice_id': 'zh-CN-XiaomoNeural', 'display_name': '晓墨', 'language': '中文', 'gender': '女'},
        {'voice_id': 'zh-CN-XiaoxuanNeural', 'display_name': '晓萱', 'language': '中文', 'gender': '女'},
        {'voice_id': 'zh-CN-XiaoruiNeural', 'display_name': '晓睿', 'language': '中文', 'gender': '女'},
        {'voice_id': 'zh-CN-YunfengNeural', 'display_name': '云枫', 'language': '中文', 'gender': '男'},
        {'voice_id': 'zh-CN-YunjianNeural', 'display_name': '云健', 'language': '中文', 'gender': '男'},
    ]
    
    success_count = 0
    for voice in voices:
        try:
            result = db.add_tts_voice(voice['voice_id'], voice['display_name'], voice['language'], voice['gender'])
            if result:
                success_count += 1
                print(f"成功添加语音: {voice['display_name']}")
        except Exception as e:
            print(f"添加语音时出错: {voice['voice_id']} - {str(e)}")
    
    print(f"初始化添加了 {success_count} 个默认语音")
    return success_count

def process_video_render(project_id, render_id):
    """Process video rendering in a background thread"""
    try:
        # Update status to in progress
        db.update_render_progress(render_id, 0.01, status='in_progress')
        
        # Get project data
        project = db.get_video_project(project_id)
        settings = db.get_video_settings(project_id)
        chapters = db.get_project_chapters(project_id)
        
        if not project or not settings or not chapters:
            raise Exception("Missing project data, settings, or chapters")
        
        # Create temporary directory for processing
        temp_dir = os.path.join(app.config['UPLOAD_FOLDER'], f'render_{render_id}')
        os.makedirs(temp_dir, exist_ok=True)
        
        # Step 1: Text preprocessing (10%)
        db.update_render_progress(render_id, 0.1, status='in_progress')
        chapter_texts = []
        
        for i, chapter in enumerate(chapters):
            novel_id = chapter['novel_id']
            chapter_id = chapter['chapter_id']
            chapter_data = db.get_chapter(novel_id, chapter_id)
            
            if chapter_data:
                # Apply text replacement rules
                text = chapter_data['content']
                replacement_rules = db.get_all_text_replacements()
                for rule in replacement_rules:
                    text = text.replace(rule['original_text'], rule['replacement_text'])
                
                chapter_texts.append({
                    'title': chapter_data['title'],
                    'content': text
                })
        
        if not chapter_texts:
            raise Exception("No valid chapter content found")
        
        # Step 2: Generate TTS audio (30%)
        db.update_render_progress(render_id, 0.2, status='in_progress')
        # This would use edge_tts to generate audio files
        # For now, we'll simulate this with a progress update
        
        # Step 3: Generate subtitles (50%)
        db.update_render_progress(render_id, 0.5, status='in_progress')
        # This would generate ASS subtitle files
        # For now, we'll simulate this with a progress update
        
        # Step 4: Video rendering with ffmpeg (90%)
        db.update_render_progress(render_id, 0.7, status='in_progress')
        # This would use ffmpeg to generate the final video
        # For now, we'll simulate this with a progress update
        
        # Final processing and cleanup
        db.update_render_progress(render_id, 0.9, status='in_progress')
        
        # Simulate completion - in a real implementation, output_path would point to the actual video file
        output_path = os.path.join(app.config['UPLOAD_FOLDER'], f'video_{project_id}_{render_id}.mp4')
        
        # Update with completed status
        db.update_render_progress(
            render_id, 
            1.0, 
            status='completed',
            output_path=output_path
        )
        
    except Exception as e:
        import traceback
        error_message = str(e) + "\n" + traceback.format_exc()
        db.update_render_progress(
            render_id, 
            0, 
            status='error',
            error_message=error_message
        )

# Add the new route to the navigation menu
@app.context_processor
def inject_nav_data():
    return {
        'novel_to_video_url': url_for('novel_to_video')
    }

# Task Manager routes
@app.route('/task_manager')
def task_manager():
    tasks = db.get_all_render_tasks()
    return render_template('task_manager.html', tasks=tasks)

@app.route('/get_tasks_status')
def get_tasks_status():
    """Get status of all tasks for AJAX updates"""
    try:
        tasks = db.get_all_render_tasks()
        return jsonify({'status': 'success', 'tasks': [dict(task) for task in tasks]})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/pause_render_task/<int:task_id>', methods=['POST'])
def pause_render_task(task_id):
    """Pause a running render task"""
    try:
        if task_id in task_threads and task_id in task_control:
            task_control[task_id]['pause'] = True
            db.update_task_status(task_id, 'paused')
            return jsonify({'status': 'success'})
        else:
            return jsonify({'status': 'error', 'message': '任务未运行或不存在'}), 404
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/resume_render_task/<int:task_id>', methods=['POST'])
def resume_render_task(task_id):
    """Resume a paused render task"""
    try:
        task = db.get_render_task(task_id)
        if not task:
            return jsonify({'status': 'error', 'message': '任务不存在'}), 404
        
        if task['status'] == 'paused':
            if task_id in task_threads and task_id in task_control:
                # Resume an existing paused thread
                task_control[task_id]['pause'] = False
                db.update_task_status(task_id, 'processing')
                return jsonify({'status': 'success'})
            else:
                # Restart the task
                db.update_task_status(task_id, 'pending')
                start_next_pending_task()
                return jsonify({'status': 'success'})
        else:
            return jsonify({'status': 'error', 'message': '任务不处于暂停状态'}), 400
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/delete_render_task/<int:task_id>', methods=['POST'])
def delete_render_task(task_id):
    """Delete a render task"""
    try:
        # Stop the task if it's running
        if task_id in task_threads and task_id in task_control:
            task_control[task_id]['stop'] = True
            # Wait for a moment to let the thread clean up
            time.sleep(0.5)
        
        db.delete_render_task(task_id)
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/update_task_priority/<int:task_id>', methods=['POST'])
def update_task_priority(task_id):
    """Update the priority of a task"""
    try:
        priority = int(request.form.get('priority', 3))
        if priority < 1:
            priority = 1
        elif priority > 5:
            priority = 5
        
        db.update_task_priority(task_id, priority)
        return jsonify({'status': 'success'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/download_task_video/<int:task_id>')
def download_task_video(task_id):
    """Download a completed task's video"""
    try:
        task = db.get_render_task(task_id)
        if not task or task['status'] != 'completed' or not task['output_path']:
            return render_template('error.html', message='视频不存在或未准备好下载')
        
        # Ensure the directory exists
        output_dir = os.path.dirname(task['output_path'])
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            print(f"Created directory: {output_dir}")
        
        # Also ensure the downloads/videos directory exists
        videos_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'videos')
        if not os.path.exists(videos_dir):
            os.makedirs(videos_dir, exist_ok=True)
            print(f"Created videos directory: {videos_dir}")
        
        # Check if the file exists
        if not os.path.exists(task['output_path']):
            print(f"错误：视频文件不存在: {task['output_path']}")
            
            # Try to get more detailed information
            expected_path = os.path.join(videos_dir, f"video_{task['project_id']}_{task_id}.mp4")
            if os.path.exists(expected_path):
                # Update the task with the correct path and return the file
                # Implementation for db.update_task_output_path
                try:
                    conn = sqlite3.connect(app.config['DATABASE'])
                    cursor = conn.cursor()
                    cursor.execute(
                        "UPDATE render_tasks SET output_path = ? WHERE id = ?",
                        (expected_path, task_id)
                    )
                    conn.commit()
                    conn.close()
                    print(f"更新任务输出路径: {expected_path}")
                except Exception as e:
                    print(f"更新任务输出路径失败: {e}")
                
                return send_file(expected_path, as_attachment=True)
            else:
                return render_template('error.html', message=f'视频文件不存在: {task["output_path"]}。可能是生成过程中出现问题。')
        
        return send_file(task['output_path'], as_attachment=True)
    except Exception as e:
        import traceback
        error_traceback = traceback.format_exc()
        print(f"下载失败详细信息: {error_traceback}")
        return render_template('error.html', message=f'下载失败: {str(e)}')

# Functions for task processing
def start_next_pending_task():
    """Start the next pending task based on priority"""
    # Check if maximum concurrent tasks limit reached
    processing_tasks = db.get_processing_tasks()
    if len(processing_tasks) >= 2:  # Maximum 2 concurrent tasks
        return False
    
    # Get the highest priority pending task
    pending_tasks = db.get_pending_tasks()
    if not pending_tasks:
        return False
    
    # Start the highest priority task
    task = pending_tasks[0]
    task_id = task['id']
    
    # Create control dict for this task
    task_control[task_id] = {
        'pause': False,
        'stop': False
    }
    
    # Start task in a separate thread
    thread = threading.Thread(target=process_render_task, args=(task_id,))
    thread.daemon = True
    thread.start()
    
    # Store thread reference
    task_threads[task_id] = thread
    
    return True

def replace_in_ass_file(file_path,replacements):
    try:
        # 读取文件内容
        if replacements is None:
            return
        with open(file_path, 'r', encoding='utf-8') as file:
         content = file.read()

        for rule in replacements:
            print(f"原文: {rule['original_text']}")
            print(f"替换为: {rule['replacement_text']}")
            content = content.replace(rule['original_text'], rule['replacement_text'])
                # 将修改后的内容写回文件
        with open(file_path, 'w', encoding='utf-8') as file:
            file.write(content)
    
    except FileNotFoundError:
        print(f"错误: 文件 '{file_path}' 未找到")
    except Exception as e:
        print(f"发生错误: {e}")


# def extract_chapter_range(chapter_texts):
#     """
#     从章节数据中提取章节范围
    
#     Args:
#         chapter_texts: 包含章节信息的列表或字典
#                       如果是字典，应包含'title'字段
#                       如果是列表，每个元素应是包含'title'字段的字典
    
#     Returns:
#         str: 章节范围字符串，如 "第1章 ~ 第10章"
#     """
#     chapter_numbers = []
    
#     # 处理输入数据
#     if isinstance(chapter_texts, dict):
#         # 如果输入是单个字典
#         titles = [chapter_texts.get('title', '')]
#     elif isinstance(chapter_texts, list):
#         # 如果输入是列表
#         titles = [item.get('title', '') if isinstance(item, dict) else str(item) for item in chapter_texts]
#     else:
#         return "无法解析章节信息"
    
#     # 中文数字转换函数
#     def chinese_to_number(chinese_num):
#         """将中文数字转换为阿拉伯数字"""
#         chinese_digits = {
#             '零': 0, '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
#             '六': 6, '七': 7, '八': 8, '九': 9, '十': 10,
#             '壹': 1, '贰': 2, '叁': 3, '肆': 4, '伍': 5,
#             '陆': 6, '柒': 7, '捌': 8, '玖': 9, '拾': 10,
#             '一百': 100, '二百': 200, '三百': 300, '四百': 400, '五百': 500,
#             '六百': 600, '七百': 700, '八百': 800, '九百': 900, '一千': 1000,
#         }
        
#         if chinese_num in chinese_digits:
#             return chinese_digits[chinese_num]
        
#         # 处理十几的情况，如"十一"、"十二"等
#         if chinese_num.startswith('十') and len(chinese_num) > 1:
#             return 10 + chinese_digits.get(chinese_num[1], 0)
        
#         # 处理二十、三十等
#         if chinese_num.endswith('十') and len(chinese_num) > 1:
#             return chinese_digits.get(chinese_num[0], 0) * 10
        
#         # 处理二十一、三十二等
#         if '十' in chinese_num and len(chinese_num) == 3:
#             parts = chinese_num.split('十')
#             return chinese_digits.get(parts[0], 0) * 10 + chinese_digits.get(parts[1], 0)
        
#         return None
    
    # 提取所有章节号
    # for title in titles:
    #     if title:
    #         # 使用正则表达式匹配章节号
    #         # 匹配 "第X章" 格式，其中X可以是数字或中文数字
    #         # 匹配阿拉伯数字
    #         matches = re.findall(r'第(\d+)章', title)
    #         for match in matches:
    #             chapter_numbers.append(int(match))
            
    #         # 匹配中文数字
    #         chinese_matches = re.findall(r'第([一二三四五六七八九十壹贰叁肆伍陆柒捌玖拾]+)章', title)
    #         for match in chinese_matches:
    #             num = chinese_to_number(match)
    #             if num is not None:
    #                 chapter_numbers.append(num)
    
    # # 如果没有找到章节号
    # if not chapter_numbers:
    #     return "未找到章节信息"
    
    # # 去重并排序
    # chapter_numbers = sorted(set(chapter_numbers))
    
    # # 生成范围字符串
    # if len(chapter_numbers) == 1:
    #     return f"第{chapter_numbers[0]}章"
    # else:
    #     return f"第{chapter_numbers[0]}章 ~ 第{chapter_numbers[-1]}章"        
def extract_chapter_range(chapter_texts):
    """
    从章节数据中提取章节范围
    
    Args:
        chapter_texts: 包含章节信息的列表或字典
                      如果是字典，应包含'title'字段
                      如果是列表，每个元素应是包含'title'字段的字典
    
    Returns:
        str: 章节范围字符串，如 "第1章 ~ 第10章"
    """
    chapter_numbers = []
    
    # 处理输入数据
    if isinstance(chapter_texts, dict):
        # 如果输入是单个字典
        titles = [chapter_texts.get('title', '')]
    elif isinstance(chapter_texts, list):
        # 如果输入是列表
        titles = [item.get('title', '') if isinstance(item, dict) else str(item) for item in chapter_texts]
    else:
        return "无法解析章节信息"
    
    # 优化的中文数字转换函数
    def chinese_to_number(chinese_num):
        """将中文数字转换为阿拉伯数字，支持复杂的中文数字组合"""
        # 基础数字映射
        chinese_digits = {
            '零': 0, '一': 1, '二': 2, '三': 3, '四': 4, '五': 5,
            '六': 6, '七': 7, '八': 8, '九': 9,
            '壹': 1, '贰': 2, '叁': 3, '肆': 4, '伍': 5,
            '陆': 6, '柒': 7, '捌': 8, '玖': 9,
        }
        
        # 单位映射
        units = {
            '十': 10, '拾': 10,
            '百': 100, '佰': 100,
            '千': 1000, '仟': 1000,
            '万': 10000, '萬': 10000,
        }
        
        # 清理输入
        chinese_num = chinese_num.strip()
        if not chinese_num:
            return None
        
        # 特殊情况：单独的"十"表示10
        if chinese_num in ['十', '拾']:
            return 10
        
        # 处理以"十"开头的情况，如"十一"、"十二"等
        if chinese_num.startswith(('十', '拾')):
            if len(chinese_num) == 1:
                return 10
            remainder = chinese_num[1:]
            if remainder in chinese_digits:
                return 10 + chinese_digits[remainder]
        
        result = 0
        temp_num = 0
        
        i = 0
        while i < len(chinese_num):
            char = chinese_num[i]
            
            if char in chinese_digits:
                temp_num = chinese_digits[char]
            elif char in units:
                unit_value = units[char]
                if unit_value == 10:
                    # 处理十位
                    if temp_num == 0:  # 如果前面没有数字，默认为1
                        temp_num = 1
                    result += temp_num * unit_value
                    temp_num = 0
                elif unit_value == 100:
                    # 处理百位
                    if temp_num == 0:  # 如果前面没有数字，默认为1
                        temp_num = 1
                    result += temp_num * unit_value
                    temp_num = 0
                elif unit_value == 1000:
                    # 处理千位
                    if temp_num == 0:  # 如果前面没有数字，默认为1
                        temp_num = 1
                    result += temp_num * unit_value
                    temp_num = 0
                elif unit_value == 10000:
                    # 处理万位
                    if temp_num == 0:
                        temp_num = result if result > 0 else 1
                    else:
                        temp_num = result + temp_num
                    result = temp_num * unit_value
                    temp_num = 0
            
            i += 1
        
        # 加上最后剩余的数字
        result += temp_num
        
        return result if result > 0 else None
    
    # 提取所有章节号
    for title in titles:
        if title:
            # 使用正则表达式匹配章节号
            # 匹配阿拉伯数字，允许空格
            matches = re.findall(r'第\s*(\d+)\s*章', title)
            for match in matches:
                chapter_numbers.append(int(match))
            
            # 匹配中文数字 - 扩展正则表达式以支持更复杂的中文数字
            chinese_pattern = r'第([零一二三四五六七八九十壹贰叁肆伍陆柒捌玖拾百佰千仟万萬]+)章'
            chinese_matches = re.findall(chinese_pattern, title)
            for match in chinese_matches:
                num = chinese_to_number(match)
                if num is not None:
                    chapter_numbers.append(num)
    
    # 如果没有找到章节号
    if not chapter_numbers:
        return "未找到章节信息"
    
    # 去重并排序
    chapter_numbers = sorted(set(chapter_numbers))
    
    # 生成范围字符串
    if len(chapter_numbers) == 1:
        return f"第{chapter_numbers[0]}章"
    else:
        return f"第{chapter_numbers[0]}章 ~ 第{chapter_numbers[-1]}章"    

def process_render_task(task_id):
    """Process a render task in a background thread with improved structure"""
    try:
        # Initialize task
        db.update_task_status(task_id, 'processing', progress=0.01)
        task_data = _get_task_data(task_id)
        temp_dir = _create_temp_directory(task_id)
        
        # Check for stop/pause conditions
        if _should_stop_task(task_id):
            return cleanup_task(task_id, temp_dir)
        
        # Process content
        db.update_task_status(task_id, 'processing', progress=0.05)
        chapter_texts = _process_chapter_texts(task_data, task_id, temp_dir)
        chapter_info_text =  extract_chapter_range(chapter_texts)
        
        # Generate audio
        db.update_task_status(task_id, 'processing', progress=0.2)
        audio_files = _generate_audio_files(chapter_texts, task_data, task_id, temp_dir)
        final_audio_file = _combine_audio_files(audio_files, task_id, temp_dir)
        
        project_id = task_data['project_id']
        text_replacements = db.get_all_text_replacements_with_projectid(project_id)
        # Generate subtitles
        db.update_task_status(task_id, 'processing', progress=0.55)
        subtitle_file, subtitle_success = _generate_subtitles(final_audio_file, task_data, task_id, temp_dir)
        
        if subtitle_success:
           replace_in_ass_file(subtitle_file, text_replacements)

        # Add background music
        db.update_task_status(task_id, 'processing', progress=0.6)
        final_audio_with_music = _add_background_music(final_audio_file, task_data, task_id, temp_dir)
        
        # Render video
        db.update_task_status(task_id, 'processing', progress=0.75)
        output_file = _render_video(final_audio_with_music, subtitle_file, subtitle_success, task_data, task_id)
        
        # Finalize task
        db.update_task_status(task_id, 'processing', progress=0.95)
        
        # Check if douyin upload is enabled and generate cover
        
        check_douyin_upload(task_id, project_id, output_file,chapter_info_text)
        
        _finalize_task(task_id, output_file, temp_dir)
        
    except Exception as e:
        _handle_task_error(task_id, e, temp_dir if 'temp_dir' in locals() else None)

def _get_task_data(task_id):
    """Get all data needed for the render task"""
    task = db.get_render_task(task_id)
    if not task or not task['project_id']:
        raise Exception("Task data is invalid")
    
    project_id = task['project_id']
    project = db.get_video_project(project_id)
    settings = db.get_video_settings(project_id)
    
    # Use chapters from task instead of all project chapters
    chapters = []
    if task.get('chapter_ids') and task.get('novel_id'):
        try:
            import json
            chapter_ids = json.loads(task['chapter_ids']) if isinstance(task['chapter_ids'], str) else task['chapter_ids']
            novel_id = task['novel_id']
            
            # Get each chapter by ID
            for chapter_id in chapter_ids:
                chapter = db.get_chapter(novel_id, chapter_id)
                if chapter:
                    # Add additional fields to match the expected format
                    chapter_data = dict(chapter)
                    chapter_data['novel_id'] = novel_id
                    chapter_data['chapter_id'] = chapter_id
                    chapters.append(chapter_data)
        except Exception as e:
            print(f"Error getting chapters from task: {e}")
            # Fallback to project chapters if task chapters fail
            chapters = db.get_project_chapters(project_id)
    else:
        # Fallback to project chapters if no chapter_ids in task
        chapters = db.get_project_chapters(project_id)
    
    if not project or not settings or not chapters:
        raise Exception("Missing project data, settings, or chapters")
    
    return {
        'task': task,
        'project_id': project_id,
        'project': project,
        'settings': settings,
        'chapters': chapters
    }

def _create_temp_directory(task_id):
    """Create and prepare temporary directories for processing"""
    # Create task-specific temp directory
    temp_dir = os.path.join(app.config['UPLOAD_FOLDER'], f'task_{task_id}')
    os.makedirs(temp_dir, exist_ok=True)
    
    # Ensure other required directories exist
    downloads_dir = app.config['UPLOAD_FOLDER']
    os.makedirs(downloads_dir, exist_ok=True)
    
    videos_dir = os.path.join(downloads_dir, 'videos')
    os.makedirs(videos_dir, exist_ok=True)
    
    return temp_dir

def _should_stop_task(task_id):
    """Check if the task should be stopped based on control flags"""
    return task_id in task_control and task_control[task_id]['stop']

def _process_chapter_texts(task_data, task_id, temp_dir):
    """Process chapter texts with replacement rules"""
    chapter_texts = []
    chapters = task_data['chapters']

    
    # Sort chapters by chapter_id (treating as strings but with numeric ordering)
    # chapters.sort(key=lambda x: int(x['chapter_id']) if x['chapter_id'].isdigit() else x['chapter_id'])
    

    
    for i, chapter in enumerate(chapters):
        # Check if task should be stopped
        if _should_stop_task(task_id):
            return []
        
        # Handle pause state
        _handle_pause_state(task_id)
        
        novel_id = chapter['novel_id']
        chapter_id = chapter['chapter_id']
        chapter_data = db.get_chapter(novel_id, chapter_id)
        
        if chapter_data:
            # test = '宝子们，由于作者优化修改章节，前面几章有做修改，陈息到京城那几章作者已删除'
            # Apply text replacement rules
            text = chapter_data['content']
            replacement_rules = db.get_all_text_replacements()
            for rule in replacement_rules:
                text = text.replace(rule['original_text'], rule['replacement_text'])

            # if i == 0:
            #     text = test + '\n\n' + text    
            
            chapter_texts.append({
                'title': chapter_data['title'],
                'content': text
            })
    
    if not chapter_texts:
        raise Exception("No valid chapter content found")
        
    return chapter_texts

def _handle_pause_state(task_id):
    """Wait while the task is in paused state"""
    while task_id in task_control and task_control[task_id]['pause']:
        time.sleep(1)
        if _should_stop_task(task_id):
            return

def _generate_audio_files(chapter_texts, task_data, task_id, temp_dir):
    """Generate TTS audio files for each chapter"""
    settings = task_data['settings']
    audio_files = []
    total_chapters = len(chapter_texts)
    
    for i, chapter in enumerate(chapter_texts):
        # Check for stop/pause
        if _should_stop_task(task_id):
            return []
        _handle_pause_state(task_id)
        
        # Update progress
        progress = 0.05 + (i / total_chapters) * 0.35
        db.update_task_status(task_id, 'processing', progress=progress)
        
        # Generate audio for this chapter
        chapter_audio_file = os.path.join(temp_dir, f'chapter_{i}.mp3')
        
        # Extract TTS settings
        voice_id = settings.get('voice_id') or 'zh-CN-XiaoxiaoNeural'  # 使用默认中文语音
        # 确保voice_id是字符串类型
        if not isinstance(voice_id, str):
            voice_id = 'zh-CN-XiaoxiaoNeural'
        voice_rate = settings.get('voice_rate', 1.0)
        
        # Calculate speech rate
        rate_value = int((voice_rate - 1) * 100)
        rate_text = f"+{rate_value}%" if rate_value >= 0 else f"{rate_value}%"
        
        # Generate TTS using edge_tts
        tts_text = chapter['content']
        
        # Run edge_tts async function
        # async def generate_speech():
        #     communicate = edge_tts.Communicate(
        #         text=tts_text,
        #         voice=voice_id,
        #         rate=rate_text
        #     )
        #     await communicate.save(chapter_audio_file)
        
        # asyncio.run(generate_speech())
        async def generate_speech():
            """
            Generates speech from text using edge_tts with retry mechanism.
            
            This async function attempts to convert the given text to speech using edge_tts,
            with configurable voice and rate parameters. It implements a retry mechanism
            (max_retries times) in case of failures, with 1 second delay between attempts.
            
            Args:
                tts_text: Text to convert to speech
                voice_id: Voice identifier for TTS
                rate_text: Speech rate adjustment
            
            Raises:
                Exception: If all retry attempts fail
            
            Note:
                Saves output to chapter_audio_file (defined in outer scope)
            """
            retry_count = 0
            max_retries = 3
            
            while retry_count < max_retries:
                try:
                    communicate = edge_tts.Communicate(
                        text=tts_text,
                        voice=voice_id,
                        rate=rate_text
                    )
                    # trojan
                    # communicate = edge_tts.Communicate(text=tts_text, voice=voice_id, rate=rate_text,  proxy="http://127.0.0.1:51837")
                    #clash verge
                    # communicate = edge_tts.Communicate(text=tts_text, voice=voice_id, rate=rate_text,  proxy="socks5://127.0.0.1:7897")
                    await communicate.save(chapter_audio_file)
                    break  # 成功时退出循环
                except Exception as e:
                    retry_count += 1
                    if retry_count >= max_retries:
                        print(f"生成语音失败，已重试{max_retries}次: {str(e)}")
                        raise  # 重试次数用完后抛出异常
                    print(f"生成语音出错，正在重试 ({retry_count}/{max_retries}): {str(e)}")
                    await asyncio.sleep(1)  # 重试前等待1秒
        
        asyncio.run(generate_speech())
        
        # Verify the audio file was created
        if not os.path.exists(chapter_audio_file):
            raise Exception(f"Failed to generate audio for chapter {i+1}")
        
        audio_files.append(chapter_audio_file)
    
    return audio_files

def _combine_audio_files(audio_files, task_id, temp_dir):
    """Combine multiple audio files into a single file"""
    final_audio_file = os.path.join(temp_dir, 'final_audio.mp3')
    
    if len(audio_files) == 1:
        # Only one chapter, use it directly
        shutil.copy2(audio_files[0], final_audio_file)
    else:
        # Combine multiple audio files
        concat_list_file = os.path.join(temp_dir, 'concat_list.txt')
        with open(concat_list_file, 'w', encoding='utf-8') as f:
            for audio_file in audio_files:
                f.write(f"file '{os.path.abspath(audio_file)}'\n")
        
        # Use ffmpeg to concatenate audio files
        ffmpeg_cmd = [
            'ffmpeg', '-y', '-f', 'concat', '-safe', '0', 
            '-i', concat_list_file, '-c', 'copy', final_audio_file
        ]
        subprocess.run(ffmpeg_cmd, check=True)
    
    return final_audio_file

def _generate_subtitles(audio_file, task_data, task_id, temp_dir):
    """Generate subtitle file from audio"""
    settings = task_data['settings']
    subtitle_file = os.path.join(temp_dir, f'subtitle_task_{task_id}.ass')
    
    # Check if audio file exists
    if not os.path.exists(audio_file):
        print(f"ERROR: Audio file does not exist, cannot generate subtitles: {audio_file}")
        return None, False
    
    try:
        # Select subtitle API
        subtitle_api = settings.get('subtitle_api', 'jianying')
        
        # Create subtitle directory if needed
        subtitle_dir = os.path.dirname(subtitle_file)
        if not os.path.exists(subtitle_dir):
            os.makedirs(subtitle_dir, exist_ok=True)
        
        # Generate subtitles
        subtitle_success = convert_mp3_to_subtitle(
            audio_file, 
            subtitle_file,
            api_type=subtitle_api,
            format="ass",
            subtitle_settings=settings
        )
        
        # Verify subtitle generation
        if subtitle_success and os.path.exists(subtitle_file):
            with open(subtitle_file, 'r', encoding='utf-8') as f:
                subtitle_content = f.read()
                if '[Script Info]' in subtitle_content and '[V4+ Styles]' in subtitle_content:
                    print("Subtitle file format correct")
                else:
                    print("Warning: Subtitle file format incomplete")
                    subtitle_success = False
        
        return subtitle_file, subtitle_success
    
    except Exception as e:
        print(f"Subtitle generation error: {str(e)}")
        return None, False

# def _add_background_music(audio_file, task_data, task_id, temp_dir):
#     """Add background music to the audio file if available"""
#     settings = task_data['settings']
#     final_audio_with_music = os.path.join(temp_dir, 'final_audio_with_music.mp3')
    
#     # If background music is set, mix it with the speech audio
#     if settings.get('bg_music_path'):
#         bg_music_path = os.path.join(app.static_folder, settings['bg_music_path'])
#         bg_music_volume = settings.get('bg_music_volume', 0.3)
        
#         if os.path.exists(bg_music_path):
#             try:
#                 # Get audio duration for looping
#                 cmd = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration', 
#                        '-of', 'default=noprint_wrappers=1:nokey=1', audio_file]
#                 audio_duration = float(subprocess.check_output(cmd).decode('utf-8').strip())
                
#                 # Mix background music with main audio - 使用更稳定的方法
#                 ffmpeg_cmd = [
#                     'ffmpeg', '-y', '-nostdin',
#                     '-i', audio_file,
#                     '-stream_loop', '-1',
#                     '-t', str(audio_duration),
#                     '-i', bg_music_path,
#                     '-filter_complex', f'[0:a]volume=1[a];[1:a]volume={bg_music_volume}[b];[a][b]amix=inputs=2:duration=first:dropout_transition=0',
#                     '-c:a', 'libmp3lame', '-b:a', '128k',  # 使用固定比特率而不是质量模式
#                     '-ar', '44100',  # 固定采样率
#                     '-ac', '2',  # 固定声道数
#                     final_audio_with_music
#                 ]
                
#                 print("开始执行音频混合...")
#                 print("命令:", ' '.join(ffmpeg_cmd))
                
#                 try:
#                     # 直接运行，不重定向输出，让ffmpeg自然完成
#                     result = subprocess.run(ffmpeg_cmd, check=True, stdin=subprocess.DEVNULL)
#                     print("音频混合完成")
#                 except subprocess.CalledProcessError as e:
#                     print(f"ffmpeg 执行失败，返回码: {e.returncode}")
#                     raise
                
#                 if not os.path.exists(final_audio_with_music):
#                     print("Warning: Mixed audio file was not created, using original")
#                     shutil.copy2(audio_file, final_audio_with_music)
#             except Exception as e:
#                 print(f"Error mixing audio with background music: {e}")
#                 shutil.copy2(audio_file, final_audio_with_music)
#         else:
#             print(f"Background music file not found: {bg_music_path}")
#             shutil.copy2(audio_file, final_audio_with_music)
#     else:
#         # No background music, use original audio
#         shutil.copy2(audio_file, final_audio_with_music)
    
#     return final_audio_with_music

# def _add_background_music(audio_file, task_data, task_id, temp_dir):
#     """Add background music to the audio file if available - Windows optimized"""
#     settings = task_data['settings']
#     final_audio_with_music = os.path.join(temp_dir, 'final_audio_with_music.mp3')
    
#     # If background music is set, mix it with the speech audio
#     if settings.get('bg_music_path'):
#         bg_music_path = os.path.join(app.static_folder, settings['bg_music_path'])
#         bg_music_volume = settings.get('bg_music_volume', 0.3)
        
#         if os.path.exists(bg_music_path):
#             try:
#                 # Get audio duration for looping
#                 cmd = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration',
#                        '-of', 'default=noprint_wrappers=1:nokey=1', audio_file]
#                 audio_duration = float(subprocess.check_output(cmd).decode('utf-8').strip())
                
#                 # 关键修复：Windows上的FFmpeg命令优化
#                 ffmpeg_cmd = [
#                     'ffmpeg', '-y', 
#                     '-nostdin',  # 防止等待stdin输入
#                     '-hide_banner',  # 减少输出
#                     '-loglevel', 'error',  # 只输出错误信息
#                     '-i', audio_file,
#                     '-stream_loop', '-1',
#                     '-t', str(audio_duration),
#                     '-i', bg_music_path,
#                     '-filter_complex', f'[0:a]volume=1[a];[1:a]volume={bg_music_volume}[b];[a][b]amix=inputs=2:duration=first:dropout_transition=0',
#                     '-c:a', 'libmp3lame', 
#                     '-b:a', '128k',
#                     '-ar', '44100',
#                     '-ac', '2',
#                     '-f', 'mp3',  # 明确指定输出格式
#                     final_audio_with_music
#                 ]
                
#                 print("开始执行音频混合...")
#                 print("命令:", ' '.join(ffmpeg_cmd))
                
#                 try:
#                     # Windows特定的subprocess调用 - 关键修复点
#                     result = subprocess.run(
#                         ffmpeg_cmd,
#                         check=True,
#                         stdin=subprocess.DEVNULL,  # 确保不等待stdin
#                         stdout=subprocess.DEVNULL,  # 不捕获stdout，让它直接输出
#                         stderr=subprocess.DEVNULL,  # 不捕获stderr，避免缓冲区问题
#                         creationflags=subprocess.CREATE_NO_WINDOW  # Windows下不创建新窗口
#                     )
#                     print("音频混合完成")
                    
#                 except subprocess.CalledProcessError as e:
#                     print(f"ffmpeg 执行失败，返回码: {e.returncode}")
#                     raise
                
#                 # 验证输出文件
#                 if not os.path.exists(final_audio_with_music):
#                     print("Warning: Mixed audio file was not created, using original")
#                     shutil.copy2(audio_file, final_audio_with_music)
#                 else:
#                     print(f"混合音频文件创建成功")
                
#             except Exception as e:
#                 print(f"Error mixing audio with background music: {e}")
#                 shutil.copy2(audio_file, final_audio_with_music)
#         else:
#             print(f"Background music file not found: {bg_music_path}")
#             shutil.copy2(audio_file, final_audio_with_music)
#     else:
#         # No background music, use original audio
#         shutil.copy2(audio_file, final_audio_with_music)
    
#     return final_audio_with_music


def _add_background_music(audio_file, task_data, task_id, temp_dir):
    """Add background music - 根本性解决FFmpeg卡死问题"""
    settings = task_data['settings']
    final_audio_with_music = os.path.join(temp_dir, 'final_audio_with_music.mp3')
    
    if settings.get('bg_music_path'):
        bg_music_path = os.path.join(app.static_folder, settings['bg_music_path'])
        bg_music_volume = settings.get('bg_music_volume', 0.3)
        
        if os.path.exists(bg_music_path):
            try:
                # Get audio duration
                cmd = ['ffprobe', '-v', 'error', '-show_entries', 'format=duration',
                       '-of', 'default=noprint_wrappers=1:nokey=1', audio_file]
                audio_duration = float(subprocess.check_output(cmd).decode('utf-8').strip())
                
                # 关键修复：分步骤处理，避免复杂的filter_complex导致的死锁
                print("开始执行音频混合...")
                
                # 步骤1：先处理背景音乐循环和时长
                temp_bg_music = os.path.join(temp_dir, 'temp_bg_music.mp3')
                bg_cmd = [
                    'ffmpeg', '-y', '-nostdin', '-hide_banner', '-loglevel', 'panic',
                    '-stream_loop', '-1',
                    '-t', str(audio_duration),
                    '-i', bg_music_path,
                    '-c:a', 'libmp3lame', '-b:a', '128k', '-ar', '44100', '-ac', '2',
                    temp_bg_music
                ]
                
                print("步骤1: 处理背景音乐...")
                subprocess.run(
                    bg_cmd,
                    check=True,
                    stdin=subprocess.DEVNULL,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
                
                # 步骤2：混合音频（使用更简单的命令）
                print("步骤2: 混合音频...")
                mix_cmd = [
                    'ffmpeg', '-y', '-nostdin', '-hide_banner', '-loglevel', 'panic',
                    '-i', audio_file,
                    '-i', temp_bg_music,
                    '-filter_complex', f'[0:a][1:a]amix=inputs=2:duration=first:weights=1 {bg_music_volume}',
                    '-c:a', 'libmp3lame', '-b:a', '128k', '-ar', '44100', '-ac', '2',
                    final_audio_with_music
                ]
                
                subprocess.run(
                    mix_cmd,
                    check=True,
                    stdin=subprocess.DEVNULL,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    creationflags=subprocess.CREATE_NO_WINDOW
                )
                
                # 清理临时文件
                try:
                    os.remove(temp_bg_music)
                except:
                    pass
                
                print("音频混合完成")
                
                # 验证输出文件
                if not os.path.exists(final_audio_with_music) or os.path.getsize(final_audio_with_music) < 1024:
                    print("输出文件无效，使用原始文件")
                    shutil.copy2(audio_file, final_audio_with_music)
                
            except Exception as e:
                print(f"音频混合出错: {e}")
                shutil.copy2(audio_file, final_audio_with_music)
        else:
            print(f"背景音乐文件不存在: {bg_music_path}")
            shutil.copy2(audio_file, final_audio_with_music)
    else:
        shutil.copy2(audio_file, final_audio_with_music)
    
    return final_audio_with_music
def _render_video(audio_file, subtitle_file, subtitle_success, task_data, task_id):
    """Render the final video with audio, subtitles, and background"""
    project_id = task_data['project_id']
    settings = task_data['settings']
  
    
    # Create output directory for videos if needed
    videos_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'videos')
    os.makedirs(videos_dir, exist_ok=True)
    
    # Define output file path
    output_file = os.path.join(videos_dir, f"video_{project_id}_{task_id}.mp4")
    
    # Video dimensions
    video_width = settings.get('video_width', 1280)
    video_height = settings.get('video_height', 720)
    
    # Title settings
    title_text = settings.get('title_text', task_data['project']['title'])
    title_font = settings.get('title_font', 'SimHei')
    title_size = settings.get('title_size', 60)
    title_color = settings.get('title_color', '#FFFFFF')
    title_position = settings.get('title_position', 'top')
    
    # 获取音频文件的精确时长
    def get_audio_duration(audio_path):
        try:
            cmd = ['ffprobe', '-v', 'quiet', '-show_entries', 'format=duration', 
                   '-of', 'csv=p=0', audio_path]
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return float(result.stdout.strip())
        except Exception as e:
            print(f"Error getting audio duration: {e}")
            return None
    
    # 获取音频时长
    audio_duration = get_audio_duration(audio_file)
    if audio_duration is None:
        raise Exception("Cannot determine audio duration")
    
    print(f"Audio duration: {audio_duration} seconds")
    
    # Check for background video
    bg_video_path = settings.get('bg_video_path')
    full_bg_video_path = None
    has_bg_video = False
    
    if bg_video_path:
        if bg_video_path.startswith('uploads/'):
            # 相对路径，转换为绝对路径
            full_bg_video_path = os.path.join(app.config['VIDEO_UPLOAD_FOLDER'], os.path.basename(bg_video_path))
        else:
            # 绝对路径或静态文件路径（兼容旧数据）
            if os.path.isabs(bg_video_path):
                full_bg_video_path = bg_video_path
            else:
                full_bg_video_path = os.path.join(app.static_folder, bg_video_path)
        
        has_bg_video = os.path.exists(full_bg_video_path)
    
    # Prepare ffmpeg command
    ffmpeg_cmd = ['ffmpeg', '-y']
    filter_complex = []
    
    # Add input sources based on whether we have background video
    if has_bg_video:
        # Use background video
        ffmpeg_cmd.extend([
             '-stream_loop', '-1',  # Add this line to enable infinite loop
             '-i', full_bg_video_path
        ])
        
        # Scale video to target size
        filter_complex.append(f"[0:v]scale={video_width}:{video_height},setsar=1:1")
        
        # Add title based on position settings
        escaped_title = title_text.replace("'", "\\'")
        
        if title_position == 'custom' and 'title_x' in settings and 'title_y' in settings:
            # Custom position
            x_pos = settings['title_x']
            y_pos = settings['title_y']
            title_cmd = f",drawtext=text='{escaped_title}':fontfile={title_font}:fontsize={title_size}:fontcolor={title_color}:x={x_pos}:y={y_pos}"
        else:
            # Preset position
            if title_position == 'top':
                y_pos = 30
            elif title_position == 'middle':
                y_pos = video_height // 2 - title_size // 2
            else:  # bottom
                y_pos = video_height - 30 - title_size
                
            title_cmd = f",drawtext=text='{escaped_title}':fontfile={title_font}:fontsize={title_size}:fontcolor={title_color}:x=(w-text_w)/2:y={y_pos}"
        
        filter_complex.append(title_cmd)
        
        # Add subtitle file if available
        if subtitle_success and subtitle_file and os.path.exists(subtitle_file):
            subtitle_path = subtitle_file.replace('\\', '/').replace('\\', '/')
            filter_complex.append(f",subtitles='{subtitle_path}'")
        
        # Complete filter chain
        filter_complex_str = "".join(filter_complex) + "[v]"
        
        # Add audio input
        ffmpeg_cmd.extend(['-i', audio_file])
        
        # Map outputs with precise duration control
        ffmpeg_cmd.extend([
            '-filter_complex', filter_complex_str,
            '-map', '[v]', 
            '-map', '1:a',
            '-t', str(audio_duration),  # 使用精确的音频时长而不是 -shortest
            '-avoid_negative_ts', 'make_zero'  # 避免时间戳问题
        ])
    else:
        # Create solid color background with exact duration
        ffmpeg_cmd.extend([
            '-f', 'lavfi', 
            '-i', f"color=c=black:s={video_width}x{video_height}:d={audio_duration}:r=30",  # 使用精确时长
            '-i', audio_file
        ])
        
        # Build filters for title and subtitles
        filter_vf = []
        
        # Add title
        escaped_title = title_text.replace("'", "\\'")
        if title_position == 'top':
            y_pos = 30
        elif title_position == 'middle':
            y_pos = video_height // 2 - title_size // 2
        else:  # bottom
            y_pos = video_height - 30 - title_size
            
        title_cmd = f"drawtext=text='{escaped_title}':fontfile={title_font}:fontsize={title_size}:fontcolor={title_color}:x=(w-text_w)/2:y={y_pos}"
        filter_vf.append(title_cmd)
        
        # Add subtitles
        if subtitle_success and subtitle_file and os.path.exists(subtitle_file):
            subtitle_path = subtitle_file.replace('\\', '/').replace('\\', '/')
            filter_vf.append(f"subtitles='{subtitle_path}'")
        
        # Complete filter string
        if filter_vf:
            filter_vf_str = ",".join(filter_vf)
            ffmpeg_cmd.extend([
                '-vf', filter_vf_str,
                '-map', '0:v', 
                '-map', '1:a',
                '-t', str(audio_duration),  # 使用精确的音频时长
                '-avoid_negative_ts', 'make_zero'
            ])
    
    # Add encoding settings
    use_gpu = settings.get('use_gpu', 0)
    
    if use_gpu:
        # Try to detect and use hardware acceleration
        hw_encoders = _detect_hardware_encoders()
        
        if hw_encoders:
            # Use first available hardware encoder
            gpu_type, encoder = hw_encoders[0]
            
            if gpu_type == 'nvidia':
                ffmpeg_cmd.extend([
                    '-c:v', encoder,
                    '-preset', 'medium',
                    '-b:v', '2M',
                    '-c:a', 'aac',
                    '-b:a', '192k',
                    '-movflags', '+faststart',  # 优化视频文件结构
                    output_file
                ])
            elif gpu_type == 'amd':
                ffmpeg_cmd.extend([
                    '-c:v', encoder,
                    '-quality', 'speed',
                    '-b:v', '2M',
                    '-c:a', 'aac',
                    '-b:a', '192k',
                    '-movflags', '+faststart',
                    output_file
                ])
            elif gpu_type == 'intel':
                ffmpeg_cmd.extend([
                    '-c:v', encoder,
                    '-preset', 'medium',
                    '-b:v', '2M',
                    '-c:a', 'aac',
                    '-b:a', '192k',
                    '-movflags', '+faststart',
                    output_file
                ])
        else:
            # Fall back to software encoding
            ffmpeg_cmd.extend([
                '-c:v', 'libx264',
                '-preset', 'medium',
                '-crf', '23',
                '-c:a', 'aac',
                '-b:a', '192k',
                '-movflags', '+faststart',
                output_file
            ])
    else:
        # Use software encoding as specified
        ffmpeg_cmd.extend([
            '-c:v', 'libx264',
            '-preset', 'medium',
            '-crf', '23',
            '-c:a', 'aac',
            '-b:a', '192k',
            '-movflags', '+faststart',
            output_file
        ])
    
    # Execute ffmpeg command
    try:
        print(f"Running FFmpeg command: {' '.join(ffmpeg_cmd)}")
        result = subprocess.run(ffmpeg_cmd, check=True, capture_output=True, text=True)
        
        # 验证输出视频时长
        output_duration = get_audio_duration(output_file)
        if output_duration:
            print(f"Output video duration: {output_duration} seconds")
            duration_diff = abs(output_duration - audio_duration)
            if duration_diff > 0.1:  # 允许0.1秒的误差
                print(f"Warning: Duration mismatch! Difference: {duration_diff} seconds")
            else:
                print("Duration match confirmed!")
        
    except subprocess.CalledProcessError as e:
        print(f"FFmpeg execution failed: {e}")
        print(f"Error output: {e.stderr if hasattr(e, 'stderr') else 'Not available'}")
        raise
    
    return output_file

def _detect_hardware_encoders():
    """Detect available hardware encoders for video acceleration"""
    hw_encoders = []
    
    try:
        # Check NVIDIA GPU (NVENC)
        nvenc_check = subprocess.run(['ffmpeg', '-hide_banner', '-encoders'], capture_output=True, text=True)
        
        if 'h264_nvenc' in nvenc_check.stdout:
            test_cmd = ['ffmpeg', '-f', 'lavfi', '-i', 'color=c=black:s=64x64:r=1', 
                        '-c:v', 'h264_nvenc', '-t', '0.1', '-f', 'null', '-']
            try:
                test_result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=3)
                if test_result.returncode == 0:
                    hw_encoders.append(('nvidia', 'h264_nvenc'))
            except Exception:
                pass
        
        # Check AMD GPU (AMF)
        if 'h264_amf' in nvenc_check.stdout:
            test_cmd = [
                'ffmpeg', '-f', 'lavfi', '-i', 'color=c=black:s=320x240:r=30', 
                '-c:v', 'h264_amf', '-b:v', '1M', '-quality', 'speed',
                '-profile:v', 'main', '-t', '0.1', '-f', 'null', '-'
            ]
            try:
                test_result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=5)
                if test_result.returncode == 0:
                    hw_encoders.append(('amd', 'h264_amf'))
                elif 'AMD' in subprocess.run(['systeminfo'], capture_output=True, text=True).stdout:
                    hw_encoders.append(('amd', 'h264_amf'))
            except Exception:
                if 'AMD' in subprocess.run(['systeminfo'], capture_output=True, text=True).stdout:
                    hw_encoders.append(('amd', 'h264_amf'))
        
        # Check Intel GPU (QSV)
        if 'h264_qsv' in nvenc_check.stdout:
            test_cmd = ['ffmpeg', '-f', 'lavfi', '-i', 'color=c=black:s=64x64:r=1', 
                        '-c:v', 'h264_qsv', '-t', '0.1', '-f', 'null', '-']
            try:
                test_result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=3)
                if test_result.returncode == 0:
                    hw_encoders.append(('intel', 'h264_qsv'))
            except Exception:
                pass
    
    except Exception:
        pass
    
    return hw_encoders

def _finalize_task(task_id, output_file, temp_dir):
    """Finalize the task, update status and clean up"""
    # Update task status to completed
    db.update_task_status(
        task_id, 
        'completed',
        progress=1.0,
        output_path=output_file
    )
    
    # Keep temp directory for debugging as requested
    print(f"Keeping temporary directory: {temp_dir}")
    
    # Remove task from control and threads dictionaries
    if task_id in task_control:
        del task_control[task_id]
    if task_id in task_threads:
        del task_threads[task_id]
    
    # Start next pending task if any
    start_next_pending_task()

def _handle_task_error(task_id, error, temp_dir=None):
    """Handle errors during task processing"""
    import traceback
    error_traceback = traceback.format_exc()
    
    # Print detailed error information
    print("\n\n============= ERROR DETAILS =============")
    print(f"Task ID: {task_id}")
    print(f"Error message: {str(error)}")
    print("Complete traceback:")
    print(error_traceback)
    if temp_dir:
        print(f"Temp directory (preserved): {temp_dir}")
    print("=========================================\n\n")
    
    # Update task status
    error_message = str(error) + "\n" + error_traceback
    db.update_task_status(
        task_id, 
        'failed',
        progress=0, 
        error_message=error_message
    )
    
    # Clean up task control 
    if task_id in task_control:
        del task_control[task_id]
    if task_id in task_threads:
        del task_threads[task_id]
    
    # Start next pending task
    start_next_pending_task()

def cleanup_task(task_id, temp_dir=None):
    """Clean up resources when a task is stopped manually"""
    try:
        # Update task status
        db.update_task_status(task_id, 'cancelled', error_message="任务被用户手动停止")
        
        # Remove from dictionaries
        if task_id in task_control:
            del task_control[task_id]
        if task_id in task_threads:
            del task_threads[task_id]
        
        # Keep temp directory as requested
        if temp_dir and os.path.exists(temp_dir):
            print(f"Keeping temporary directory as requested: {temp_dir}")
            # shutil.rmtree(temp_dir)
    except Exception as e:
        print(f"Error cleaning up task {task_id}: {e}")
    
    # Start next pending task
    start_next_pending_task()

# Add the task manager route to the navigation menu
@app.context_processor
def inject_nav_data():
    return {
        'novel_to_video_url': url_for('novel_to_video'),
        'task_manager_url': url_for('task_manager')
    }

# Start the task processor when the app starts
@app.before_first_request
def before_first_request():
    # Start scheduler for novel updates
    start_scheduler()
    
    # Start any pending render tasks
    threading.Thread(target=start_pending_tasks).daemon = True
    threading.Thread(target=start_pending_tasks).start()

def start_pending_tasks():
    """Start pending tasks when the app starts"""
    time.sleep(5)  # Wait for the app to fully initialize
    while True:
        try:
            start_next_pending_task()
        except Exception as e:
            print(f"Error starting pending tasks: {e}")
        time.sleep(10)  # Check for pending tasks every 10 seconds

@app.route('/test_tts_voices')
def test_tts_voices():
    """测试语音列表获取"""
    try:
        voices = db.get_all_tts_voices()
        return jsonify({
            'status': 'success',
            'count': len(voices),
            'voices': [
                {
                    'id': v['id'],
                    'voice_id': v['voice_id'],
                    'display_name': v['display_name'],
                    'language': v['language'],
                    'gender': v['gender']
                } for v in voices
            ]
        })
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)}), 500

# Add function for subtitle creation
def create_srt(asr_data, output_file):
    """Create SRT subtitle file from ASR data."""
    with open(output_file, 'w', encoding='utf-8') as f:
        for i, segment in enumerate(asr_data.segments):
            start_time = segment.start_time / 1000  # convert to seconds
            end_time = segment.end_time / 1000
            
            # Format time as SRT timestamp (HH:MM:SS,MS)
            start_str = f"{int(start_time // 3600):02d}:{int(start_time % 3600 // 60):02d}:{int(start_time % 60):02d},{int(start_time % 1 * 1000):03d}"
            end_str = f"{int(end_time // 3600):02d}:{int(end_time % 3600 // 60):02d}:{int(end_time % 60):02d},{int(end_time % 1 * 1000):03d}"
            
            # Write SRT entry
            f.write(f"{i+1}\n")
            f.write(f"{start_str} --> {end_str}\n")
            f.write(f"{segment.text}\n\n")

def create_ass(asr_data, output_file, font_name="SimHei", font_size=20, color="&HFFFFFF", alignment=2, position=None, video_width=1280, video_height=720):
    """Create ASS subtitle file from ASR data with styling.
    alignment: 1=左下, 2=中下, 3=右下, 4=左中, 5=中, 6=右中, 7=左上, 8=中上, 9=右上
    position: 自定义坐标的元组 (x, y)
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        # Write ASS file header
        f.write("[Script Info]\n")
        f.write("Title: Generated by NovelCloud\n")
        f.write("ScriptType: v4.00+\n")
        f.write("WrapStyle: 0\n")
        f.write("ScaledBorderAndShadow: yes\n")
        f.write("YCbCr Matrix: TV.601\n")
        f.write(f"PlayResX: {video_width}\n")
        f.write(f"PlayResY: {video_height}\n\n")
        
        # Write styles
        f.write("[V4+ Styles]\n")
        f.write("Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding\n")
        f.write(f"Style: Default,{font_name},{font_size},{color},&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,2,2,{alignment},10,10,20,1\n\n")
        
        # Write events
        f.write("[Events]\n")
        f.write("Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text\n")
        
        # Add each subtitle as an event
        for i, segment in enumerate(asr_data.segments):
            start_time = segment.start_time / 1000  # convert to seconds
            end_time = segment.end_time / 1000
            
            # Format time as ASS timestamp (H:MM:SS.cs)
            start_str = f"{int(start_time // 3600)}:{int(start_time % 3600 // 60):02d}:{int(start_time % 60):02d}.{int(start_time % 1 * 100):02d}"
            end_str = f"{int(end_time // 3600)}:{int(end_time % 3600 // 60):02d}:{int(end_time % 60):02d}.{int(end_time % 1 * 100):02d}"
            
            # Write event line
            text = segment.text
            # 如果设置了自定义位置，添加位置标签
            if position:
                x, y = position
                text = f"{{\\pos({x},{y})}}{text}"
            
            f.write(f"Dialogue: 0,{start_str},{end_str},Default,,0,0,0,,{text}\n")
        
    print(f"ASS字幕文件已创建: {output_file}")
    return True

def html_to_ass_color(html_color):
    if html_color.startswith('#'):
        # 移除#并转换为RGB组件
        rgb = html_color[1:]
        # 反转顺序并添加&H前缀
        return f"&H{rgb[4:6]}{rgb[2:4]}{rgb[0:2]}"
    return html_color  

def run_asr_with_timeout(asr, input_file, api_type, callback=None, max_retries=3, upload_timeout=120):
    """
    使用超时机制运行ASR转写，并在超时后自动重试
    
    Args:
        asr: ASR对象（JianYingASR或BcutASR实例）
        input_file: 输入音频文件路径
        api_type: API类型 ("jianying" 或 "bcut")
        callback: 进度回调函数
        max_retries: 最大重试次数
        upload_timeout: 上传超时时间（秒）
        
    Returns:
        ASRData或None: 成功返回ASR数据，失败返回None
    """
    import concurrent.futures
    import time
    from app.core.bk_asr.jianying import JianYingASR
    from app.core.bk_asr.bcut import BcutASR
    
    retry_count = 0
    success = False
    current_asr = asr
    
    while retry_count < max_retries and not success:
        try:
            # 使用concurrent.futures添加超时功能
            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(current_asr.run, callback=callback)
                try:
                    asr_data = future.result(timeout=upload_timeout)
                    if not asr_data or not hasattr(asr_data, 'segments') or len(asr_data.segments) == 0:
                        print(f"警告: 转写结果为空，无法生成字幕文件 (尝试 {retry_count+1}/{max_retries})")
                        retry_count += 1
                        if retry_count < max_retries:
                            print(f"正在尝试使用备选API重试...")
                            # 如果JianYing失败，尝试使用Bcut
                            if api_type.lower() != "bcut" and api_type.lower() != "b":
                                print("切换到必剪(B站)API进行转录")
                                current_asr = BcutASR(input_file, use_cache=True)
                            time.sleep(2)  # 短暂等待后重试
                        else:
                            return None
                    else:
                        success = True
                        return asr_data
                except concurrent.futures.TimeoutError:
                    print(f"上传超时 ({upload_timeout}秒)，取消当前任务并重试...")
                    future.cancel()
                    retry_count += 1
                    if retry_count < max_retries:
                        print(f"正在尝试使用备选API重试...")
                        # 如果JianYing失败，尝试使用Bcut
                        if api_type.lower() != "bcut" and api_type.lower() != "b":
                            print("切换到必剪(B站)API进行转录")
                            current_asr = BcutASR(input_file, use_cache=True)
                        time.sleep(2)  # 短暂等待后重试
                    else:
                        print(f"已达到最大重试次数 ({max_retries})，转写失败")
                        return None
        except Exception as e:
            retry_count += 1
            print(f"转写过程异常 (尝试 {retry_count}/{max_retries}): {e}")
            if retry_count < max_retries:
                print(f"正在尝试重试...")
                # 如果JianYing失败，尝试使用Bcut
                if api_type.lower() != "bcut" and api_type.lower() != "b":
                    print("切换到必剪(B站)API进行转录")
                    current_asr = BcutASR(input_file, use_cache=True)
                time.sleep(2)  # 短暂等待后重试
            else:
                print(f"已达到最大重试次数 ({max_retries})，转写失败")
                return None
    
    return None    

def convert_mp3_to_subtitle(input_file, output_file=None, api_type="jianying", format="ass", subtitle_settings=None):
    """Convert MP3 file to subtitle file using selected API.
    subtitle_settings: 可以包含字体名称(font_name)、大小(font_size)、颜色(color)、对齐方式(alignment)和位置(position)等信息
    """
    if not os.path.exists(input_file):
        print(f"Error: Input file '{input_file}' does not exist.")
        return False
    
    print(f"Processing audio file: {input_file}")
    
    try:
        # 确保输出目录存在
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            print(f"创建目录: {output_dir}")
         
        # 定义进度回调函数
        progress_callback = lambda progress, message: print(f"转写进度: {progress}%, {message}")

        # 根据选择的API类型创建不同的ASR对象
        if api_type.lower() == "local":
            print("使用本地转写API进行转录")
            try:
                # 使用transcribe_sync方法进行本地转写
                asyncio.run(transcribe_sync(None, input_file))
                
                # 读取生成的SRT文件（假设与输入文件同名，但扩展名为.srt）
                srt_file = os.path.splitext(input_file)[0] + ".srt"
                if os.path.exists(srt_file):
                    print(f"本地转写成功，字幕文件：{srt_file}")
                    # 将SRT文件复制到指定的输出文件路径
                    # 处理自定义位置
                    position = None
                    if subtitle_settings and 'subtitle_position' in subtitle_settings and subtitle_settings['subtitle_position'] == 'custom':
                        if 'subtitle_x' in subtitle_settings and 'subtitle_y' in subtitle_settings:
                            position = (subtitle_settings['subtitle_x'], subtitle_settings['subtitle_y'])
                            print(f"使用自定义字幕位置: {position}")
                    
                    convert_srt_to_ass(srt_file, output_file, 
                                      video_width=subtitle_settings.get('video_width', 1280),
                                      video_height=subtitle_settings.get('video_height', 720),
                                      font_name=subtitle_settings.get('subtitle_font', 'SimHei'),
                                      font_size=subtitle_settings.get('subtitle_size', 20),
                                      color=html_to_ass_color(subtitle_settings.get('subtitle_color', '#FFFFFF')),
                                      alignment=2,
                                      position=position)
                    return True
                else:
                    print("本地转写未生成字幕文件")
                    return False
            except Exception as e:
                print(f"本地转写过程出现异常: {e}")
                return False
        elif api_type.lower() == "bcut" or api_type.lower() == "b":
            print("使用必剪(B站)API进行转录")
            asr = BcutASR(input_file, use_cache=True)
        else:
            print("使用剪映API进行转录")
            asr = JianYingASR(input_file, use_cache=True)

        # 使用超时机制运行ASR
        try:
            asr_data = run_asr_with_timeout(
                asr=asr,
                input_file=input_file,
                api_type=api_type,
                callback=progress_callback,
                max_retries=3,
                upload_timeout=600  # 2分钟超时
            )
            
            if not asr_data:
                print("转写失败，无法生成字幕")
                return False
                
        except Exception as e:
            print(f"转写过程出现未处理异常: {e}")
            return False
        
        # 设置输出文件路径
        if output_file is None:
            if format.lower() == "ass":
                output_file = os.path.splitext(input_file)[0] + ".ass"
            else:
                output_file = os.path.splitext(input_file)[0] + ".srt"
        
        # 根据格式创建不同类型的字幕文件
        try:
            if format.lower() == "ass":
                # 默认设置
                font_name = "SimHei"
                font_size = 20
                color = "&HFFFFFF"
                alignment = 2  # 默认底部居中
                position = None  # 默认无自定义位置
                video_width = 1280  # 默认宽度
                video_height = 720  # 默认高度
                
                # 如果提供了字幕设置，使用自定义设置
                if subtitle_settings:
                    font_name = subtitle_settings.get('subtitle_font', font_name)
                    font_size = subtitle_settings.get('subtitle_size', font_size)
                    color = subtitle_settings.get('subtitle_color', color)
                    alignment = subtitle_settings.get('alignment', alignment)
                    video_width = subtitle_settings.get('video_width', video_width)
                    video_height = subtitle_settings.get('video_height', video_height)
                    
                    # 处理自定义位置
                    if 'subtitle_position' in subtitle_settings and subtitle_settings['subtitle_position'] == 'custom':
                        if 'subtitle_x' in subtitle_settings and 'subtitle_y' in subtitle_settings:
                            position = (subtitle_settings['subtitle_x'], subtitle_settings['subtitle_y'])
                            print(f"使用自定义字幕位置: {position}")
                    elif 'subtitle_position' in subtitle_settings:
                        # 根据预设位置设置对齐方式
                        pos = subtitle_settings['subtitle_position']
                        if pos == 'top':
                            alignment = 8  # 顶部居中
                        elif pos == 'middle':
                            alignment = 5  # 中间居中
                        else:  # bottom
                            alignment = 2  # 底部居中
                
                create_ass(asr_data, output_file, font_name, font_size, html_to_ass_color(color), alignment, position, video_width, video_height)
                print(f"ASS字幕文件已创建: {output_file}")
            else:
                create_srt(asr_data, output_file)
                print(f"SRT字幕文件已创建: {output_file}")
                
            # 最后检查文件是否实际存在
            if os.path.exists(output_file) and os.path.getsize(output_file) > 0:
                return True
            else:
                print(f"警告: 字幕文件未能成功创建或为空: {output_file}")
                return False
        except Exception as e:
            print(f"创建字幕文件时出错: {str(e)}")
            return False
            
    except Exception as e:
        print(f"转录失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

# Debug route to check video settings
@app.route('/debug_video_settings/<int:project_id>')
def debug_video_settings(project_id):
    try:
        # Get project settings
        settings = db.get_video_settings(project_id)
        
        # Check if bg_video_path exists
        bg_video_path = settings.get('bg_video_path')
        
        # Check if the file exists
        file_exists = False
        full_path = None
        if bg_video_path:
            full_path = os.path.join(app.static_folder, bg_video_path)
            file_exists = os.path.exists(full_path)
        
        # Print debug info
        debug_info = {
            'project_id': project_id,
            'bg_video_path_in_db': bg_video_path,
            'full_path': full_path,
            'file_exists': file_exists,
            'static_folder': app.static_folder,
            'all_settings': dict(settings) if settings else None
        }
        
        return jsonify(debug_info)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Debug route to test file uploads
@app.route('/test_upload', methods=['GET', 'POST'])
def test_upload():
    if request.method == 'POST':
        try:
            # Check if a file was uploaded
            if 'test_file' not in request.files:
                return jsonify({'status': 'error', 'message': 'No file part'})
            
            test_file = request.files['test_file']
            if not test_file.filename:
                return jsonify({'status': 'error', 'message': 'No file selected'})
            
            # Ensure upload directory exists
            upload_dir = os.path.join(app.static_folder, 'uploads', 'test')
            os.makedirs(upload_dir, exist_ok=True)
            
            # Save the file with timestamp
            timestamp = int(time.time())
            filename = secure_filename(test_file.filename)
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(upload_dir, filename)
            test_file.save(filepath)
            
            # Check if file was saved successfully
            if os.path.exists(filepath):
                relative_path = f"uploads/test/{filename}"
                return jsonify({
                    'status': 'success',
                    'message': 'File uploaded successfully',
                    'filename': filename,
                    'relative_path': relative_path,
                    'full_path': filepath,
                    'file_size': os.path.getsize(filepath),
                    'static_folder': app.static_folder
                })
            else:
                return jsonify({'status': 'error', 'message': 'File upload failed - file not found after saving'})
                
        except Exception as e:
            return jsonify({'status': 'error', 'message': f'Upload error: {str(e)}'})
    
    # GET request shows a simple upload form
    return '''
    <!doctype html>
    <title>Test File Upload</title>
    <h1>Test File Upload</h1>
    <form method="post" enctype="multipart/form-data">
      <input type="file" name="test_file">
      <input type="submit" value="Upload">
    </form>
    '''



def get_system_font_path(font_name):
    """查找系统字体文件的完整路径"""
    fonts_dir = "C:\\Windows\\Fonts"
    # 可能的文件扩展名
    extensions = ['.ttf', '.ttc', '.otf']
    
    # 常见的中文字体文件名映射
    font_filename_map = {
        '华文行楷': ['STXINGKA', 'HWXK', 'HWXK.TTF', 'STXINGKA.TTF', 'huawenxingkai'],
        # 可以添加更多字体的映射
    }
    
    # 1. 直接尝试原始名称
    for ext in extensions:
        path = os.path.join(fonts_dir, f"{font_name}{ext}")
        if os.path.exists(path):
            return path
            
    # 2. 尝试映射的文件名
    if font_name in font_filename_map:
        for filename in font_filename_map[font_name]:
            for ext in extensions:
                path = os.path.join(fonts_dir, f"{filename}{ext}")
                if os.path.exists(path):
                    return path
                # 尝试不带扩展名的完整文件名
                if os.path.exists(os.path.join(fonts_dir, filename)):
                    return os.path.join(fonts_dir, filename)
    
    return None


# Debug route to manually set background video path
@app.route('/set_bg_video/<int:project_id>', methods=['GET', 'POST'])
def set_bg_video_path(project_id):
    if request.method == 'POST':
        try:
            # Get the path from the form
            video_path = request.form.get('video_path')
            
            if not video_path:
                return jsonify({'status': 'error', 'message': 'No path provided'})
            
            # Update the settings
            settings = {'bg_video_path': video_path}
            db.update_video_settings(project_id, settings)
            
            # Verify the update
            updated_settings = db.get_video_settings(project_id)
            
            return jsonify({
                'status': 'success', 
                'message': 'Background video path updated',
                'project_id': project_id,
                'video_path': video_path,
                'settings_after_update': updated_settings.get('bg_video_path') if updated_settings else None
            })
                
        except Exception as e:
            return jsonify({'status': 'error', 'message': f'Update error: {str(e)}'})
    
    # GET request shows a form to set path
    return f'''
    <!doctype html>
    <title>Set Background Video Path</title>
    <h1>Set Background Video Path for Project {project_id}</h1>
    <form method="post">
      <p>Enter video path relative to static folder (e.g. uploads/videos/123456_video.mp4):</p>
      <input type="text" name="video_path" style="width: 400px;">
      <input type="submit" value="Update">
    </form>
    <p><a href="/debug_video_settings/{project_id}">Check current settings</a></p>
    '''

def generate_douyin_cover(task_id, project_id,chapterinfo_text):
    """
    Generate a cover image for Douyin based on social media settings
    
    Args:
        task_id: The ID of the current rendering task
        project_id: The ID of the video project
        
    Returns:
        str: Path to the generated cover image, or None if generation failed
    """
    try:
        # Get social media settings for Douyin
        social_settings = db.get_social_media_settings(project_id, platform='douyin')
        
        # Check if Douyin upload is enabled
        if not social_settings or not social_settings.get('enable_upload'):
            print(f"Douyin upload not enabled for project {project_id}")
            return None
        
        # Get project title and chapter info
        project_data = db.get_video_project(project_id)
        if not project_data:
            print(f"Cannot find project {project_id} data")
            return None
            
        project_title = project_data.get('title', '')
        
        # Create a directory for the cover if needed
        covers_dir = os.path.join(app.config['UPLOAD_FOLDER'], 'covers')
        os.makedirs(covers_dir, exist_ok=True)
        
        # Initialize background image
        if social_settings.get('custom_cover_path'):
            # Use the custom background image if available
            bg_path = os.path.join(app.static_folder, social_settings['custom_cover_path'])
            # Check if the file exists with this path
            if not os.path.exists(bg_path) and 'uploads/' in social_settings['custom_cover_path']:
                # Try alternative path by removing 'uploads/' prefix
                alt_bg_path = os.path.join(app.static_folder, 
                                social_settings['custom_cover_path'].replace('uploads/', ''))
                if os.path.exists(alt_bg_path):
                    bg_path = alt_bg_path
                    
            if os.path.exists(bg_path):
                try:
                    img = Image.open(bg_path)
                    print(f"Using custom background image: {bg_path}")
                    # Get dimensions from the background image
                    width, height = img.size
                except Exception as e:
                    print(f"Error opening background image: {e}, using default")
                    # Create a default black background if image can't be opened
                    width, height = 720, 1280  # Typical Douyin portrait video dimensions
                    img = Image.new('RGB', (width, height), color=(0, 0, 0))  # Black background
            else:
                print(f"Custom background image not found: {bg_path}, using default")
                width, height = 720, 1280  # Typical Douyin portrait video dimensions
                img = Image.new('RGB', (width, height), color=(0, 0, 0))  # Black background
        else:
            # Create a default black background if no custom image
            width, height = 720, 1280  # Typical Douyin portrait video dimensions
            img = Image.new('RGB', (width, height), color=(0, 0, 0))  # Black background
        
        draw = ImageDraw.Draw(img)
        
        # Load title font if specified, otherwise use default
        title_text = social_settings.get('title', project_title)
        title_font_name = social_settings.get('title_font', 'SimHei')
        title_size = social_settings.get('title_size', 40)
        title_color = social_settings.get('title_color', '#FFFFFF')
        title_position = social_settings.get('title_position', 'top')
        title_x = social_settings.get('title_x')
        title_y = social_settings.get('title_y')
        
        # Try to load specified font, fall back to default if not available
        try:
            # Try to use full path for system fonts first
            system_fonts_path = get_system_font_path(title_font_name)
            # system_fonts_path = os.path.join("C:\\Windows\\Fonts", f"{title_font_name}.ttf")
            if os.path.exists(system_fonts_path):
                title_font = ImageFont.truetype(system_fonts_path, title_size)
            else:
                title_font = ImageFont.truetype(title_font_name, title_size)
        except Exception as e:
            print(f"Failed to load font {title_font_name}: {e}, trying system font path")
            try:
                # Try Windows system font path
                title_font = ImageFont.truetype("C:\\Windows\\Fonts\\SimHei.ttf", title_size)
            except Exception as e:
                print(f"Failed to load system font: {e}, using default")
                title_font = ImageFont.load_default()
        
        # Convert HTML color to RGB
        try:
            from PIL import ImageColor
            title_rgb = ImageColor.getrgb(title_color)
        except:
            title_rgb = (255, 255, 255)  # Default to white
        
        # Calculate text dimensions
        title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_height = title_bbox[3] - title_bbox[1]
        
        # Position the title text
        if title_x is not None and title_y is not None:
            # Use specified coordinates
            x_pos = title_x
            y_pos = title_y
        else:
            # Calculate position based on setting
            if title_position == 'top':
                x_pos = (width - title_width) // 2
                y_pos = height // 10
            elif title_position == 'center':
                x_pos = (width - title_width) // 2
                y_pos = (height - title_height) // 2
            else:  # bottom
                x_pos = (width - title_width) // 2
                y_pos = height * 8 // 10
        
        # Draw the title
        draw.text((x_pos, y_pos), title_text, font=title_font, fill=title_rgb)
        
        # Add chapter info if enabled
        if social_settings.get('show_chapter_info'):
            # Get chapter info text
            chapter_text = chapterinfo_text 
            chapter_font_name = social_settings.get('chapter_font', 'SimHei')
            chapter_size = social_settings.get('chapter_size', 30)
            chapter_color = social_settings.get('chapter_color', '#FFFFFF')
            chapter_position = social_settings.get('chapter_position', 'bottom')
            chapter_x = social_settings.get('chapter_x')
            chapter_y = social_settings.get('chapter_y')
            
            # Try to load chapter font
            try:
                # Try to use full path for system fonts first
                system_fonts_path = get_system_font_path(chapter_font_name)
                if os.path.exists(system_fonts_path):
                    chapter_font = ImageFont.truetype(system_fonts_path, chapter_size)
                else:
                    chapter_font = ImageFont.truetype(chapter_font_name, chapter_size)
            except Exception as e:
                print(f"Failed to load font {chapter_font_name}: {e}, trying system font path")
                try:
                    # Try Windows system font path
                    chapter_font = ImageFont.truetype("C:\\Windows\\Fonts\\SimHei.ttf", chapter_size)
                except Exception as e:
                    print(f"Failed to load system font: {e}, using default")
                    chapter_font = ImageFont.load_default()
            
            # Convert HTML color to RGB
            try:
                chapter_rgb = ImageColor.getrgb(chapter_color)
            except:
                chapter_rgb = (255, 255, 255)  # Default to white
            
            # Calculate text dimensions
            chapter_bbox = draw.textbbox((0, 0), chapter_text, font=chapter_font)
            chapter_width = chapter_bbox[2] - chapter_bbox[0]
            chapter_height = chapter_bbox[3] - chapter_bbox[1]
            
            # Position the chapter text
            if chapter_x is not None and chapter_y is not None:
                # Use specified coordinates
                x_pos = (width - chapter_width) // 2
                y_pos = chapter_y
            else:
                # Calculate position based on setting
                if chapter_position == 'top':
                    x_pos = (width - chapter_width) // 2
                    y_pos = height // 10 + title_height + 30
                elif chapter_position == 'center':
                    x_pos = (width - chapter_width) // 2
                    y_pos = (height - chapter_height) // 2 + title_height + 30
                else:  # bottom
                    x_pos = (width - chapter_width) // 2
                    y_pos = height * 9 // 10
            
            # Draw the chapter info
            draw.text((x_pos, y_pos), chapter_text, font=chapter_font, fill=chapter_rgb)
        
        # Save the image
        cover_path = os.path.join(covers_dir, f"douyin_cover_{project_id}_{int(time.time())}.png")
        img.save(cover_path)
        
        print(f"Generated Douyin cover: {cover_path}")
        return cover_path
    
    except Exception as e:
        print(f"Error generating Douyin cover: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_douyin_upload(task_id, project_id, output_file,chapterinfo_text):
    """
    Check if this video should be uploaded to Douyin and prepare resources
    
    Args:
        task_id: The ID of the current rendering task
        project_id: The ID of the video project
        output_file: Path to the rendered video file
        
    Returns:
        bool: True if the video should be uploaded to Douyin, False otherwise
    """
    try:
        # Get social media settings for Douyin
        social_settings = db.get_social_media_settings(project_id, platform='douyin')
        
        # Check if Douyin upload is enabled
        if not social_settings or not social_settings.get('enable_upload'):
            print(f"Douyin upload not enabled for project {project_id}")
            return False
        
        # Generate cover image for Douyin
        cover_path = generate_douyin_cover(task_id, project_id,chapterinfo_text)

        tags = []
        if social_settings.get('tags'):
          tags.append(social_settings['tags'])

        title = social_settings.get('title', '')
        description = social_settings.get('description', '')  
        cookie_dict = json.loads(social_settings['cookies'])  
        publish_datetimes = generate_schedule_time_next_day(1, 1, daily_times=[16])
        description = f"{chapterinfo_text}\n{description}"
        if os.path.exists(cover_path):
            print(f"使用预览图: {cover_path}")
            app = DouYinVideo(title, output_file, description, tags, publish_datetimes[0], cookie_dict, cover_path)                      # 执行上传
            cookie_data = asyncio.run(app.main(), debug=False)  
            cookies_str = json.dumps(cookie_data)
            db.update_social_media_settings_cookies(project_id, cookies_str)
        else:
            print(f"预览图不存在，使用默认封面: {cover_path}")
            app = DouYinVideo(title, output_file, description, tags, publish_datetimes[0], cookie_dict)
            cookie_data = asyncio.run(app.main(), debug=False)  
            cookies_str = json.dumps(cookie_data)
            db.update_social_media_settings_cookies(project_id, cookies_str)   

        
        # Add upload history entry
        upload_id = db.add_upload_history(project_id, 'douyin', status='preparing')
        
        # Update upload status
        db.update_upload_progress(upload_id, 0.1, status='preparing')
        
        # Here you could add code to actually upload to Douyin
        # or queue the upload task for later processing
        
        return True
    
    except Exception as e:
        print(f"Error checking Douyin upload: {e}")
        return False
    

def douyin_cookie_gen_update_db(project_id):
    """
    更新数据库中的抖音cookie
    
    Args:
        cookies: 抖音cookie字符串
    """
    try:
        # 连接到数据库
        conn = sqlite3.connect(app.config['DATABASE'])
        cursor = conn.cursor()
        

        cookie_data = asyncio.run(douyin_cookie_gen_without_login())
                        # Change to check if cookie_data is a dictionary with a 'cookies' key
        if isinstance(cookie_data, dict) and 'cookies' in cookie_data:
            cookies_str = json.dumps(cookie_data)
            cursor.execute(
                "UPDATE social_media_settings SET cookies = ? WHERE project_id = ? AND platform = 'douyin'",
                (cookies_str, project_id)
            )
            conn.commit()
            print(f"已存储抖音cookies到数据库，项目ID: {project_id}")
            print(f"Cookies内容: {cookies_str}")
        
        # 提交更改并关闭连接
        conn.commit()
        conn.close()
        
        print("抖音cookie已更新到数据库")
    except Exception as e:
        print(f"更新抖音cookie到数据库时出错: {e}")    



@app.route('/douyin_login/<int:project_id>', methods=['POST'])
@app.route('/douyin_login', methods=['POST'])
def douyin_login(project_id=None):
    """
    处理抖音登录请求并检查指定项目的cookie
    
    可以通过URL路径或表单提交获取project_id
    
    Returns:
        JSON: {"success": true, "has_cookie": true/false} 表示登录状态及cookie状态
    """
    try:
        # 如果没有通过URL路径获取project_id，则从表单获取
        if project_id is None:
            project_id = request.form.get('project_id')
        
        if not project_id:
            return jsonify({
                "success": False,
                "error": "缺少项目ID",
                "has_cookie": False
            })
        
        print(f"检查抖音账号，项目ID: {project_id}")
        
        # 查询数据库中是否存在该项目的抖音cookie
        conn = sqlite3.connect(app.config['DATABASE'])
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='social_media_settings'")
        table_exists = cursor.fetchone() is not None
        
        has_cookie = False
        settings_json = None
        settings_info = {}
        
        if table_exists:
            # 查看表结构
            cursor.execute("PRAGMA table_info(social_media_settings)")
            columns = [col[1] for col in cursor.fetchall()]
            settings_info["columns"] = columns
            
            # 根据项目ID和平台查询social_media_settings
            cursor.execute(
                "SELECT * FROM social_media_settings WHERE project_id = ? AND platform = 'douyin'",
                (project_id,)
            )
            result = cursor.fetchone()
            
            if result:
                # 获取所有列名
                cursor.execute("PRAGMA table_info(social_media_settings)")
                columns = [col[1] for col in cursor.fetchall()]
                
                # 将结果转换为字典
                row_dict = {columns[i]: result[i] for i in range(len(columns))}
                settings_info["row_data"] = row_dict
                
                # Check if the cookie column exists directly in the table
                if 'cookies' in row_dict:
                    has_cookie = row_dict['cookies'] is not None
                    if has_cookie == False:
                        print(f"抖音cookies不存在，项目ID: {project_id}")
                        # 生成新的cookie并更新数据库
                        douyin_cookie_gen_update_db(project_id)
                    else:
                        print(f"抖音cookies已存在，项目ID: {project_id}")
                        print(f"Cookies内容: {row_dict['cookies']}") 
                        cookie_dict = json.loads(row_dict['cookies'])    
                        result = asyncio.run(douyin_check_cookie(cookie_dict)) 
                        if result == False:
                            print(f"抖音cookies有效，项目ID: {project_id}")
                            douyin_cookie_gen_update_db(project_id)
                            has_cookie = True
                          
                else:
                    print(f"表中没有cookies列，项目ID: {project_id}")
                    has_cookie = True            

        
        conn.close()
        
        # 返回登录状态和cookie状态  
        return jsonify({
            "success": True,
            "has_cookie": has_cookie,
            "project_id": project_id,
            "settings_found": settings_json is not None,
            "table_exists": table_exists,
            "settings_info": settings_info
        })
    except Exception as e:
        print(f"抖音登录出错: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            "success": False,
            "error": str(e),
            "has_cookie": False
        })
    

@app.route('/generate_cover_preview', methods=['POST'])
def generate_cover_preview():
    """Generate a temporary preview image for the cover based on current settings"""
    try:
        # Get form data
        data = request.form.to_dict()
        project_id = data.get('project_id')
        
        if not project_id:
            return jsonify({'success': False, 'error': 'Missing project ID'})
            
        # Create temporary settings dict from form data
        temp_settings = {
            'title': data.get('title', ''),
            'title_font': data.get('title_font', 'SimHei'),
            'title_size': int(data.get('title_size', 40)),
            'title_color': data.get('title_color', '#FFFFFF'),
            'title_position': data.get('title_position', 'top'),
            'title_x': int(data.get('title_x', 0)) if data.get('title_x') else None,
            'title_y': int(data.get('title_y', 0)) if data.get('title_y') else None,
            'title_on_cover': int(data.get('title_on_cover', 1)),
            'show_chapter_info': int(data.get('show_chapter_info', 0)),
            'chapter_font': data.get('chapter_font', 'SimHei'),
            'chapter_size': int(data.get('chapter_size', 30)),
            'chapter_color': data.get('chapter_color', '#FFFFFF'),
            'chapter_position': data.get('chapter_position', 'bottom'),
            'chapter_x': int(data.get('chapter_x', 0)) if data.get('chapter_x') else None,
            'chapter_y': int(data.get('chapter_y', 0)) if data.get('chapter_y') else None,
            'chapter_info_text': data.get('chapter_info_text', '示例章节信息')
        }
        
        # Check if custom cover was uploaded
        custom_cover_path = None
        if 'custom_cover' in request.files:
            file = request.files['custom_cover']
            if file and file.filename and allowed_file(file.filename, ALLOWED_IMAGE_EXTENSIONS):
                filename = secure_filename(file.filename)
                filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
                file.save(filepath)
                custom_cover_path = f'uploads/{filename}'
                temp_settings['custom_cover_path'] = custom_cover_path
        elif data.get('existing_cover_path'):
            temp_settings['custom_cover_path'] = data.get('existing_cover_path')
            
        # Get project data
        project_data = db.get_video_project(project_id)
        if not project_data:
            return jsonify({'success': False, 'error': 'Project not found'})
            
        project_title = project_data.get('title', '')
        if not temp_settings['title']:
            temp_settings['title'] = project_title
            
        # Generate temporary preview image
        preview_path = generate_temp_cover_preview(project_id, temp_settings)
        
        if preview_path:
            # Return relative path to the image for frontend use
            preview_url = url_for('static', filename=preview_path.replace(app.static_folder, '').lstrip('/').replace('\\', '/'))
            return jsonify({'success': True, 'preview_url': preview_url})
        else:
            return jsonify({'success': False, 'error': 'Failed to generate preview'})
            
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'success': False, 'error': str(e)})

def generate_temp_cover_preview(project_id, settings):
    """
    Generate a temporary cover preview image based on provided settings
    
    Args:
        project_id: The ID of the video project
        settings: Dictionary containing cover image settings
        
    Returns:
        str: Path to the generated preview image, or None if generation failed
    """
    try:
        # Create a directory for preview images if needed
        preview_dir = os.path.join(app.static_folder, 'temp_previews')
        os.makedirs(preview_dir, exist_ok=True)
        
        # Initialize background image
        if settings.get('custom_cover_path'):
            # Use the custom background image if available
            bg_path = os.path.join(app.static_folder, settings['custom_cover_path'])
            # Check if the file exists with this path
            if not os.path.exists(bg_path) and 'uploads/' in settings['custom_cover_path']:
                # Try alternative path by removing 'uploads/' prefix
                alt_bg_path = os.path.join(app.static_folder, 
                                settings['custom_cover_path'].replace('uploads/', ''))
                if os.path.exists(alt_bg_path):
                    bg_path = alt_bg_path
                    
            if os.path.exists(bg_path):
                try:
                    img = Image.open(bg_path)
                    # Get dimensions from the background image
                    width, height = img.size
                except Exception as e:
                    print(f"Error opening background image: {e}, using default")
                    # Create a default black background if image can't be opened
                    width, height = 720, 1280  # Typical Douyin portrait video dimensions
                    img = Image.new('RGB', (width, height), color=(0, 0, 0))  # Black background
            else:
                print(f"Custom background image not found: {bg_path}, using default")
                width, height = 720, 1280  # Typical Douyin portrait video dimensions
                img = Image.new('RGB', (width, height), color=(0, 0, 0))  # Black background
        else:
            # Create a default black background if no custom image
            width, height = 720, 1280  # Typical Douyin portrait video dimensions
            img = Image.new('RGB', (width, height), color=(0, 0, 0))  # Black background
        
        draw = ImageDraw.Draw(img)
        
        # Add title if enabled
        if settings.get('title_on_cover', 1):
            # Load title font if specified, otherwise use default
            title_text = settings.get('title', '')
            title_font_name = settings.get('title_font', 'SimHei')
            title_size = settings.get('title_size', 40)
            title_color = settings.get('title_color', '#FFFFFF')
            title_position = settings.get('title_position', 'top')
            title_x = settings.get('title_x')
            title_y = settings.get('title_y')
            
            # Try to load specified font, fall back to default if not available
            try:
                # Try to use full path for system fonts first
                system_fonts_path = get_system_font_path(title_font_name)
                if os.path.exists(system_fonts_path):
                    title_font = ImageFont.truetype(system_fonts_path, title_size)
                else:
                    title_font = ImageFont.truetype(title_font_name, title_size)
            except Exception as e:
                print(f"Failed to load font {title_font_name}: {e}, trying system font path")
                try:
                    # Try Windows system font path
                    title_font = ImageFont.truetype("C:\\Windows\\Fonts\\SimHei.ttf", title_size)
                except Exception as e:
                    print(f"Failed to load system font: {e}, using default")
                    title_font = ImageFont.load_default()
            
            # Convert HTML color to RGB
            try:
                from PIL import ImageColor
                title_rgb = ImageColor.getrgb(title_color)
            except:
                title_rgb = (255, 255, 255)  # Default to white
            
            # Calculate text dimensions
            title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
            title_width = title_bbox[2] - title_bbox[0]
            title_height = title_bbox[3] - title_bbox[1]
            
            # Position the title text
            if title_x is not None and title_y is not None:
                # Use specified coordinates
                x_pos = title_x
                y_pos = title_y
            else:
                # Calculate position based on setting
                if title_position == 'top':
                    x_pos = (width - title_width) // 2
                    y_pos = height // 10
                elif title_position == 'center' or title_position == 'middle':
                    x_pos = (width - title_width) // 2
                    y_pos = (height - title_height) // 2
                else:  # bottom
                    x_pos = (width - title_width) // 2
                    y_pos = height * 8 // 10
            
            # Draw the title
            draw.text((x_pos, y_pos), title_text, font=title_font, fill=title_rgb)
        
        # Add chapter info if enabled
        if settings.get('show_chapter_info', 0):
            # Get chapter info text
            chapter_text = settings.get('chapter_info_text', '示例章节信息')
            chapter_font_name = settings.get('chapter_font', 'SimHei')
            chapter_size = settings.get('chapter_size', 30)
            chapter_color = settings.get('chapter_color', '#FFFFFF')
            chapter_position = settings.get('chapter_position', 'bottom')
            chapter_x = settings.get('chapter_x')
            chapter_y = settings.get('chapter_y')
            
            # Try to load chapter font
            try:
                # Try to use full path for system fonts first
                system_fonts_path = get_system_font_path(chapter_font_name)
                if os.path.exists(system_fonts_path):
                    chapter_font = ImageFont.truetype(system_fonts_path, chapter_size)
                else:
                    chapter_font = ImageFont.truetype(chapter_font_name, chapter_size)
            except Exception as e:
                print(f"Failed to load font {chapter_font_name}: {e}, trying system font path")
                try:
                    # Try Windows system font path
                    chapter_font = ImageFont.truetype("C:\\Windows\\Fonts\\SimHei.ttf", chapter_size)
                except Exception as e:
                    print(f"Failed to load system font: {e}, using default")
                    chapter_font = ImageFont.load_default()
            
            # Convert HTML color to RGB
            try:
                chapter_rgb = ImageColor.getrgb(chapter_color)
            except:
                chapter_rgb = (255, 255, 255)  # Default to white
            
            # Calculate text dimensions
            chapter_bbox = draw.textbbox((0, 0), chapter_text, font=chapter_font)
            chapter_width = chapter_bbox[2] - chapter_bbox[0]
            chapter_height = chapter_bbox[3] - chapter_bbox[1]
            
            # Position the chapter text
            if chapter_x is not None and chapter_y is not None:
                # Use specified coordinates
                x_pos = chapter_x
                y_pos = chapter_y
            else:
                # Calculate position based on setting
                if chapter_position == 'top':
                    x_pos = (width - chapter_width) // 2
                    y_pos = height // 10 + 30
                elif chapter_position == 'center' or chapter_position == 'middle':
                    x_pos = (width - chapter_width) // 2
                    y_pos = (height - chapter_height) // 2
                else:  # bottom
                    x_pos = (width - chapter_width) // 2
                    y_pos = height * 9 // 10
            
            # Draw the chapter info
            draw.text((x_pos, y_pos), chapter_text, font=chapter_font, fill=chapter_rgb)

        
        print(f"Generated temporary cover preview for project {project_id}")
        # Save the image with timestamp to avoid caching issues
        timestamp = int(time.time())
        preview_path = os.path.join(preview_dir, f"cover_preview_{project_id}_{timestamp}.png")
        img.save(preview_path)
        
        return preview_path
    
    except Exception as e:
        print(f"Error generating cover preview: {e}")
        import traceback
        traceback.print_exc()
        return None
    
async def transcribe_sync(self, file_path: str):
    """同步转写音频文件"""
    file = Path(file_path)
    if not file.is_file():
        raise FileNotFoundError(f"The file {file_path} does not exist.")
    
    await transcribe_check(file)
    await asyncio.gather(
        transcribe_send(file),
        transcribe_recv(file)
    )    

def pause_all_update_tasks():
    """暂停所有更新任务线程"""
    for task_id, thread in scheduled_task_threads.items():
        if isinstance(thread, ScheduledUpdateThread):
            print(f"暂停更新任务 {task_id}")
            thread.pause()

def resume_all_update_tasks():
    """恢复所有更新任务线程"""
    for task_id, thread in scheduled_task_threads.items():
        if isinstance(thread, ScheduledUpdateThread):
            print(f"恢复更新任务 {task_id}")
            thread.resume()

def auto_render_video_after_update(task_id, novel_id, project_id):
    """自动渲染视频的函数，在更新检测到后被调用"""
    global is_generating_video
    
    try:
        # 处理渲染任务
        process_render_task(task_id)
        print(f"视频渲染任务 {task_id} 已完成")
    except Exception as e:
        print(f"视频渲染失败: {e}")
    finally:
        print(f"自动渲染视频任务 {task_id} 结束")
        # 恢复所有更新任务
        # with video_generation_lock:
            # is_generating_video = False
            # resume_all_update_tasks()

# 在适当位置添加路由，可以在scheduled_updates.html页面上添加功能控制
@app.route('/toggle_auto_video/<task_id>', methods=['POST'])
def toggle_auto_video(task_id):
    """开启或关闭自动视频生成功能"""
    try:
        enable = request.form.get('enable', '0')
        enable = bool(int(enable))
        
        # 检查任务是否存在
        if task_id in scheduled_task_threads:
            thread = scheduled_task_threads[task_id]
            if isinstance(thread, ScheduledUpdateThread):
                thread.auto_generate_video = enable
                print(f"任务 {task_id} 的自动视频生成功能已{'启用' if enable else '禁用'}")
                
                # 保存设置到数据库
                db.update_scheduled_task_auto_video(task_id, enable)
                
        return redirect(url_for('scheduled_updates'))
    except Exception as e:
        print(f"切换自动视频生成功能时出错: {e}")
        return render_template('error.html', message=f'操作失败: {str(e)}')

@app.route('/view_pending_updates/<novel_id>')
def view_pending_updates(novel_id):
    """查看小说的待更新章节"""
    try:
        # 获取小说信息
        novel = db.get_novel(novel_id)
        if not novel:
            return render_template('error.html', message='小说不存在')
        
        # 获取待更新章节
        pending_updates = db.get_pending_chapter_updates(novel_id)
        
        # 获取关联的视频项目
        projects = db.get_video_projects_by_novel(novel_id)
        
        return render_template('pending_updates.html', 
                              novel=novel, 
                              pending_updates=pending_updates,
                              projects=projects,
                              count=len(pending_updates))
    except Exception as e:
        print(f"查看待更新章节时出错: {e}")
        return render_template('error.html', message=f'获取待更新章节失败: {str(e)}')

@app.route('/process_pending_updates/<novel_id>', methods=['POST'])
def process_pending_updates(novel_id):
    """手动处理待更新章节"""
    try:
        # 获取小说信息
        novel = db.get_novel(novel_id)
        if not novel:
            return jsonify({'status': 'error', 'message': '小说不存在'}), 404
        
        # 获取待更新章节
        pending_updates = db.get_pending_chapter_updates(novel_id)
        if not pending_updates:
            return jsonify({'status': 'info', 'message': '没有待处理的章节更新'})
        
        # 获取所有待处理的章节ID
        chapter_ids = [update['chapter_id'] for update in pending_updates]
        
        # 获取关联的视频项目
        projects = db.get_video_projects_by_novel(novel_id)
        if not projects:
            # 将所有章节标记为已处理
            for chapter_id in chapter_ids:
                db.update_pending_chapter_status(novel_id, chapter_id, 'processed')
            db.clear_pending_chapter_updates(novel_id, 'processed')
            return jsonify({'status': 'info', 'message': '没有关联的视频项目，已清理待更新章节'})
        
        # 为每个关联的项目更新章节
        for project in projects:
            project_id = project['id']
            
            # 清理项目中现有的章节
            db.clear_project_chapters(project_id)
            
            # 添加新章节到项目
            db.add_chapters_to_project(project_id, novel_id, chapter_ids)
        
        # 将所有章节标记为已处理
        for chapter_id in chapter_ids:
            db.update_pending_chapter_status(novel_id, chapter_id, 'processed')
        
        # 清理已处理的章节
        processed_count = db.clear_pending_chapter_updates(novel_id, 'processed')
        
        # 创建渲染任务
        if request.form.get('generate_video', '0') == '1':
            for project in projects:
                project_id = project['id']
                task_title = f"手动生成 - {novel['title']} 更新后视频"
                task_id = db.create_render_task(project_id, task_title, novel_id=novel_id, chapter_ids=chapter_ids)
                
                # 启动渲染任务
                start_next_pending_task()
        
        return jsonify({
            'status': 'success', 
            'message': f'已处理 {len(chapter_ids)} 章更新，清理了 {processed_count} 条记录'
        })
    except Exception as e:
        print(f"处理待更新章节时出错: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({'status': 'error', 'message': f'处理待更新章节失败: {str(e)}'}), 500

def convert_srt_to_ass(srt_file, output_file, video_width=1280, video_height=720, font_name="SimHei", font_size=20, color="&HFFFFFF", alignment=2, position=None, auto_wrap=True, wrap_length=16):
    """
    Convert SRT subtitle file to ASS format with default styling.
    
    Args:
        srt_file: Path to the input SRT file
        output_file: Path to the output ASS file
        video_width: Video width in pixels
        video_height: Video height in pixels
        font_name: Font name to use
        font_size: Font size
        color: Font color in ASS format (&HBBGGRR)
        alignment: Text alignment (1=left bottom, 2=center bottom, 3=right bottom, etc.)
        position: Custom position tuple (x, y)
        auto_wrap: Whether to automatically wrap long subtitle lines
        wrap_length: Length threshold for auto-wrapping text
    
    Returns:
        bool: True if conversion was successful, False otherwise
    """
    try:
        if not os.path.exists(srt_file):
            print(f"Error: SRT file not found: {srt_file}")
            return False
            
        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_file)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
        
        # Parse SRT file
        with open(srt_file, 'r', encoding='utf-8') as f:
            srt_content = f.read()
        
        # Create ASS content
        bottom_margin = 20
        
        # Create ASS header
        ass_content = f'''[Script Info]
ScriptType: v4.00+
PlayResX: {video_width}
PlayResY: {video_height}
ScaledBorderAndShadow: yes
Timer: 100.0000
WrapStyle: 2
Collisions: Normal

[V4+ Styles]
Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
Style: Default,{font_name},{font_size},{color},&H000000FF,&H00000000,&H00000000,0,0,0,0,100,100,0,0,1,2,0,{alignment},10,10,{bottom_margin},1
'''
        
        # Add events section
        ass_content += '''
[Events]
Format: Layer, Start, End, Style, Name, MarginL, MarginR, MarginV, Effect, Text
'''
        
        # Parse SRT content
        pattern = re.compile(r'(\d+)\s+(\d{2}:\d{2}:\d{2},\d{3})\s+-->\s+(\d{2}:\d{2}:\d{2},\d{3})\s+(.*?)(?=\n\n|\Z)', re.DOTALL)
        matches = pattern.findall(srt_content)
        
        for match in matches:
            index = match[0]
            start_time = match[1]
            end_time = match[2]
            text = match[3].strip().replace('\n', ' ')
            
            # Convert time format
            def convert_time(time_str):
                h, m, s = time_str.replace(',', '.').split(':')
                total_seconds = float(h) * 3600 + float(m) * 60 + float(s)
                h = int(total_seconds // 3600)
                m = int((total_seconds % 3600) // 60)
                s = total_seconds % 60
                return f"{h:d}:{m:02d}:{s:05.2f}"

            start_time_ass = convert_time(start_time)
            end_time_ass = convert_time(end_time)
            
            # Apply auto-wrapping if enabled
            if auto_wrap and len(text) > wrap_length:
                # Add line break near the middle of the text
                mid_point = len(text) // 2
                # Try to find a space near the middle to break at
                space_before = text.rfind(' ', 0, mid_point)
                space_after = text.find(' ', mid_point)
                
                if space_before != -1 and (mid_point - space_before) < 10:
                    # If there's a space shortly before the middle, break there
                    text = text[:space_before] + '\\N' + text[space_before+1:]
                elif space_after != -1 and (space_after - mid_point) < 10:
                    # If there's a space shortly after the middle, break there
                    text = text[:space_after] + '\\N' + text[space_after+1:]
                else:
                    # Otherwise just break at the middle
                    text = text[:mid_point] + '\\N' + text[mid_point:]
            
            # 如果设置了自定义位置，添加位置标签
            if position:
                x, y = position
                text = f"{{\\pos({x},{y})}}{text}"
            
            # Add subtitle line
            ass_content += f'Dialogue: 0,{start_time_ass},{end_time_ass},Default,,0,0,0,,{text}\n'
        
        # Write ASS file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(ass_content)
        
        print(f"Successfully converted SRT to ASS with auto-wrap: {output_file}")
        return True
    
    except Exception as e:
        print(f"Error converting SRT to ASS: {e}")
        import traceback
        traceback.print_exc()
        return False

# Video upload management routes
@app.route('/video_upload')
def video_upload():
    """视频上传管理页面"""
    videos = db.get_all_uploaded_videos()
    return render_template('video_upload.html', videos=videos)

@app.route('/upload_video', methods=['POST'])
def upload_video():
    """处理视频上传"""
    try:
        name = request.form.get('name', '').strip()
        if not name:
            return jsonify({'success': False, 'message': '请输入视频名称'}), 400
        
        if 'video' not in request.files:
            return jsonify({'success': False, 'message': '请选择视频文件'}), 400
        
        file = request.files['video']
        if file.filename == '':
            return jsonify({'success': False, 'message': '请选择视频文件'}), 400
        
        # 检查文件类型
        allowed_extensions = {'mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm'}
        if not allowed_file(file.filename, allowed_extensions):
            return jsonify({'success': False, 'message': '不支持的视频格式'}), 400
        
        # 创建专门的视频上传目录
        upload_dir = app.config['VIDEO_UPLOAD_FOLDER']
        os.makedirs(upload_dir, exist_ok=True)
        
        # 生成安全的文件名
        filename = secure_filename(file.filename)
        # 添加时间戳避免重名
        timestamp = str(int(time.time()))
        name_part, ext_part = os.path.splitext(filename)
        filename = f"{name_part}_{timestamp}{ext_part}"
        
        file_path = os.path.join(upload_dir, filename)
        
        # 保存文件
        file.save(file_path)
        
        # 获取文件信息
        file_size = os.path.getsize(file_path)
        
        # 获取视频信息（时长、分辨率等）
        duration, width, height = get_video_info(file_path)
        
        # 生成缩略图
        thumbnail_path = generate_video_thumbnail(file_path, upload_dir)
        
        # 生成相对路径用于数据库存储
        relative_file_path = f"uploads/{filename}"
        relative_thumbnail_path = f"uploads/{os.path.basename(thumbnail_path)}" if thumbnail_path else None
        
        # 保存到数据库（使用相对路径）
        video_id = db.save_uploaded_video(
            name=name,
            filename=filename,
            file_path=relative_file_path,  # 保存相对路径
            file_size=file_size,
            duration=duration,
            width=width,
            height=height,
            thumbnail_path=relative_thumbnail_path  # 保存相对路径
        )
        
        return jsonify({'success': True, 'video_id': video_id})
        
    except Exception as e:
        print(f"Error uploading video: {e}")
        return jsonify({'success': False, 'message': f'上传失败: {str(e)}'}), 500

@app.route('/get_video/<int:video_id>')
def get_video(video_id):
    """获取视频信息"""
    try:
        video = db.get_uploaded_video(video_id)
        if not video:
            return jsonify({'success': False, 'message': '视频不存在'}), 404
        
        return jsonify({'success': True, 'video': video})
        
    except Exception as e:
        print(f"Error getting video: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/update_video/<int:video_id>', methods=['POST'])
def update_video(video_id):
    """更新视频信息"""
    try:
        data = request.get_json()
        name = data.get('name', '').strip()
        
        if not name:
            return jsonify({'success': False, 'message': '请输入视频名称'}), 400
        
        success = db.update_uploaded_video(video_id, name=name)
        if not success:
            return jsonify({'success': False, 'message': '视频不存在'}), 404
        
        return jsonify({'success': True})
        
    except Exception as e:
        print(f"Error updating video: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/delete_video/<int:video_id>', methods=['DELETE'])
def delete_video(video_id):
    """删除视频"""
    try:
        video = db.get_uploaded_video(video_id)
        if not video:
            return jsonify({'success': False, 'message': '视频不存在'}), 404
        
        # 删除文件
        try:
            # 将相对路径转换为绝对路径
            if video['file_path']:
                if video['file_path'].startswith('uploads/'):
                    # 相对路径，转换为绝对路径
                    full_file_path = os.path.join(app.config['VIDEO_UPLOAD_FOLDER'], video['filename'])
                else:
                    # 绝对路径（兼容旧数据）
                    full_file_path = video['file_path']
                
                if os.path.exists(full_file_path):
                    os.remove(full_file_path)
            
            # 删除缩略图
            if video['thumbnail_path']:
                if video['thumbnail_path'].startswith('uploads/'):
                    # 相对路径，转换为绝对路径
                    full_thumbnail_path = os.path.join(app.config['VIDEO_UPLOAD_FOLDER'], os.path.basename(video['thumbnail_path']))
                else:
                    # 绝对路径（兼容旧数据）
                    full_thumbnail_path = video['thumbnail_path']
                
                if os.path.exists(full_thumbnail_path):
                    os.remove(full_thumbnail_path)
        except Exception as e:
            print(f"Error deleting files: {e}")
        
        # 从数据库删除记录
        success = db.delete_uploaded_video(video_id)
        if not success:
            return jsonify({'success': False, 'message': '删除失败'}), 500
        
        return jsonify({'success': True})
        
    except Exception as e:
        print(f"Error deleting video: {e}")
        return jsonify({'success': False, 'message': str(e)}), 500

def get_video_info(video_path):
    """获取视频信息（时长、分辨率）"""
    try:
        cap = cv2.VideoCapture(video_path)
        
        # 获取帧率和总帧数
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
        
        # 计算时长
        duration = frame_count / fps if fps > 0 else 0
        
        # 获取分辨率
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        
        cap.release()
        
        return duration, width, height
        
    except Exception as e:
        print(f"Error getting video info: {e}")
        return None, None, None

def generate_video_thumbnail(video_path, output_dir):
    """生成视频缩略图"""
    try:
        cap = cv2.VideoCapture(video_path)
        
        # 跳到视频的1/4处获取帧
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        target_frame = total_frames // 4
        cap.set(cv2.CAP_PROP_POS_FRAMES, target_frame)
        
        ret, frame = cap.read()
        if ret:
            # 生成缩略图文件名
            video_name = os.path.splitext(os.path.basename(video_path))[0]
            thumbnail_filename = f"{video_name}_thumb.jpg"
            thumbnail_path = os.path.join(output_dir, thumbnail_filename)
            
            # 调整缩略图大小
            height, width = frame.shape[:2]
            if width > 400:
                ratio = 400 / width
                new_width = 400
                new_height = int(height * ratio)
                frame = cv2.resize(frame, (new_width, new_height))
            
            # 保存缩略图
            cv2.imwrite(thumbnail_path, frame)
            cap.release()
            
            return thumbnail_path
        
        cap.release()
        return None
        
    except Exception as e:
        print(f"Error generating thumbnail: {e}")
        return None
    

@app.route('/uploaded_videos/<filename>')
def uploaded_video(filename):
    """提供上传视频文件的访问"""
    try:
        video_path = os.path.join(app.config['VIDEO_UPLOAD_FOLDER'], filename)
        if os.path.exists(video_path):
            return send_file(video_path)
        else:
            abort(404)
    except Exception as e:
        print(f"Error serving video file: {e}")
        abort(404)
        
if __name__ == '__main__':
    app.run(debug=False, host='0.0.0.0', port=8080)     #
