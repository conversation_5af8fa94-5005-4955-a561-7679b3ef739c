#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的构建脚本，只包含必要的依赖
"""

import os
import sys
from pathlib import Path
import PyInstaller.__main__

def build_simple_exe():
    """构建简化的可执行文件"""
    
    # 获取项目根目录
    project_root = Path(__file__).parent
    
    # 主脚本
    main_script = str(project_root / "main.py")
    
    # 只包含必要的隐藏导入
    hidden_imports = [
        # 基础库
        "requests",
        "requests.adapters",
        "requests.auth",
        "requests.cookies",
        "requests.exceptions",
        "requests.models",
        "requests.sessions",
        "requests.utils",
        "urllib3",
        "urllib3.util",
        "urllib3.util.retry",
        "urllib3.exceptions",
        "lxml",
        "lxml.etree",
        "lxml.html",
        "sqlite3",
        
        # Flask相关
        "flask",
        "flask.templating",
        "werkzeug",
        "werkzeug.serving",
        "jinja2",
        "jinja2.ext",
        "markupsafe",
        
        # SocketIO
        "flask_socketio",
        "socketio",
        "engineio",
        
        # 项目模块
        "novel_sources",
        "novel_sources.fanqie",
        "novel_sources.fanqie_api",
        "novel_sources.qimao",
        "database",
        
        # 其他必要库
        "tqdm",
        "threading",
        "json",
        "datetime",
        "time",
        "os",
        "sys",
        "pathlib",
    ]
    
    # 构建PyInstaller命令参数
    args = [
        main_script,
        "--name=小说云简化版",
        "--onefile",
        "--clean",
        "--noconfirm",
    ]
    
    # 添加数据文件
    templates_dir = project_root / "templates"
    static_dir = project_root / "static"
    
    if templates_dir.exists():
        separator = ";" if os.name == 'nt' else ":"
        args.extend(["--add-data", f"{templates_dir}{separator}templates"])
    
    if static_dir.exists():
        separator = ";" if os.name == 'nt' else ":"
        args.extend(["--add-data", f"{static_dir}{separator}static"])
    
    # 添加数据库文件
    db_file = project_root / "novels.db"
    if db_file.exists():
        separator = ";" if os.name == 'nt' else ":"
        args.extend(["--add-data", f"{db_file}{separator}."])
    
    # 添加隐藏导入
    for hidden_import in hidden_imports:
        args.extend(["--hidden-import", hidden_import])
    
    # 添加视频处理相关的必需库
    video_imports = [
        "cv2",
        "PIL",
        "PIL.Image",
        "PIL.ImageDraw",
        "PIL.ImageFont",
        "pydub",
        "pydub.AudioSegment",
        "edge_tts",
        "asyncio",
        "subprocess",
        "tempfile",
        "platform",
        "shutil",
        "pathlib",
        "glob",
        "hashlib",
        "urllib",
        "urllib.parse",
    ]
    
    hidden_imports.extend(video_imports)
    
    # 排除不需要的大型模块
    excludes = [
        "torch",
        "torchvision",
        "tensorflow",
        "keras",
        "scipy",
        "matplotlib",
        "pandas",
        "sklearn",
        "transformers",
        "datasets",
        "librosa",
        "onnxruntime",
        "sympy",
    ]
    
    for exclude in excludes:
        args.extend(["--exclude-module", exclude])
    
    args.extend([
        "--distpath", str(project_root / "dist"),
        "--workpath", str(project_root / "build"),
        "--specpath", str(project_root),
    ])
    
    print("开始构建简化版可执行文件...")
    print("命令参数:", args)
    
    try:
        PyInstaller.__main__.run(args)
        print("构建完成！")
        print(f"可执行文件位置: {project_root / 'dist' / '小说云简化版.exe'}")
    except Exception as e:
        print(f"构建失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    build_simple_exe()