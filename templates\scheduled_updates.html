{% extends "base.html" %}

{% block title %}定时更新 - 小说云{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <h1 class="mb-4">
            <i class="bi bi-clock-history me-2"></i>定时更新
        </h1>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">添加新的定时更新任务</h5>
            </div>
            <div class="card-body">
                <form id="addTaskForm" action="{{ url_for('add_scheduled_task') }}" method="post">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="novel_id" class="form-label">选择小说</label>
                            <select class="form-select" id="novel_id" name="novel_id" required>
                                <option value="" selected disabled>请选择小说</option>
                                {% for novel in novels %}
                                <option value="{{ novel.id }}">{{ novel.title }} ({{ novel.source }})</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="schedule_time" class="form-label">预定更新时间</label>
                            <input type="datetime-local" class="form-control" id="schedule_time" name="schedule_time" required>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="interval_seconds" class="form-label">更新间隔 (秒)</label>
                            <input type="number" class="form-control" id="interval_seconds" name="interval_seconds" min="30" value="3600" required>
                            <div class="form-text">建议设置为30秒以上的间隔时间</div>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">自动生成视频</label>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="auto_generate_video" name="auto_generate_video" value="1" checked>
                                <label class="form-check-label" for="auto_generate_video">更新后自动生成视频</label>
                            </div>
                            <div class="form-text">启用后，当小说有新章节更新时，将自动创建视频生成任务</div>
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-1"></i>添加任务
                    </button>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">当前定时更新任务</h5>
            </div>
            <div class="card-body">
                {% if tasks %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>小说</th>
                                <th>来源</th>
                                <th>预定时间</th>
                                <th>更新间隔</th>
                                <th>上次运行</th>
                                <th>状态</th>
                                <th>自动生成视频</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for task in tasks %}
                            <tr data-task-id="{{ task.id }}">
                                <td>{{ task.id }}</td>
                                <td>
                                    <a href="{{ url_for('view_novel', novel_id=task.novel_id) }}" target="_blank">
                                        {{ task.novel_title }}
                                    </a>
                                    <a href="{{ url_for('view_pending_updates', novel_id=task.novel_id) }}" class="badge bg-info text-decoration-none ms-1" target="_blank">
                                        <i class="bi bi-hourglass-split"></i> 待更新
                                    </a>
                                </td>
                                <td>{{ task.novel_source }}</td>
                                <td>{{ task.schedule_time }}</td>
                                <td>{{ task.interval_seconds }}秒</td>
                                <td>{{ task.last_run_time or '未运行' }}</td>
                                <td>
                                    <span class="badge {% if task.is_active %}bg-success{% else %}bg-danger{% endif %} task-status">
                                        {% if task.is_active %}活动{% else %}暂停{% endif %}
                                    </span>
                                    <span class="badge bg-info running-status d-none">运行中</span>
                                    <span class="badge bg-warning paused-status d-none">已暂停</span>
                                </td>
                                <td>
                                    <form action="{{ url_for('toggle_auto_video', task_id=task.id) }}" method="post" style="display:inline">
                                        <input type="hidden" name="enable" value="{{ 0 if task.auto_generate_video else 1 }}">
                                        <button type="submit" class="btn btn-sm {% if task.auto_generate_video %}btn-success{% else %}btn-secondary{% endif %}">
                                            {% if task.auto_generate_video %}
                                                <i class="bi bi-check-circle-fill"></i> 已启用
                                            {% else %}
                                                <i class="bi bi-x-circle"></i> 已禁用
                                            {% endif %}
                                        </button>
                                    </form>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-primary edit-task" 
                                                data-bs-toggle="modal" data-bs-target="#editTaskModal" 
                                                data-task-id="{{ task.id }}"
                                                data-novel-id="{{ task.novel_id }}"
                                                data-novel-title="{{ task.novel_title }}"
                                                data-schedule-time="{{ task.schedule_time }}"
                                                data-interval-seconds="{{ task.interval_seconds }}"
                                                data-is-active="{{ task.is_active }}"
                                                data-auto-video="{{ task.auto_generate_video }}">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <form action="{{ url_for('toggle_task') }}" method="post" style="display:inline">
                                            <input type="hidden" name="task_id" value="{{ task.id }}">
                                            <input type="hidden" name="is_active" value="{{ 0 if task.is_active else 1 }}">
                                            <button type="submit" class="btn btn-sm {% if task.is_active %}btn-warning{% else %}btn-success{% endif %} toggle-task-btn">
                                                <i class="bi {% if task.is_active %}bi-pause-fill{% else %}bi-play-fill{% endif %}"></i>
                                            </button>
                                        </form>
                                        <button type="button" class="btn btn-sm btn-warning pause-task-btn d-none" data-task-id="{{ task.id }}">
                                            <i class="bi bi-pause-fill"></i>
                                        </button>
                                        <button type="button" class="btn btn-sm btn-success resume-task-btn d-none" data-task-id="{{ task.id }}">
                                            <i class="bi bi-play-fill"></i>
                                        </button>
                                        <form action="{{ url_for('delete_task') }}" method="post" style="display:inline" onsubmit="return confirm('确定要删除此任务吗？');">
                                            <input type="hidden" name="task_id" value="{{ task.id }}">
                                            <button type="submit" class="btn btn-sm btn-danger">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i>暂无定时更新任务，请添加一个任务。
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 编辑任务模态框 -->
<div class="modal fade" id="editTaskModal" tabindex="-1" aria-labelledby="editTaskModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editTaskModalLabel">编辑定时更新任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('update_task') }}" method="post">
                <div class="modal-body">
                    <input type="hidden" id="edit_task_id" name="task_id" value="">
                    
                    <div class="mb-3">
                        <label class="form-label">小说</label>
                        <input type="text" class="form-control" id="edit_novel_title" readonly>
                        <input type="hidden" id="edit_novel_id" name="novel_id" value="">
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_schedule_time" class="form-label">预定更新时间</label>
                        <input type="datetime-local" class="form-control" id="edit_schedule_time" name="schedule_time" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_interval_seconds" class="form-label">更新间隔 (秒)</label>
                        <input type="number" class="form-control" id="edit_interval_seconds" name="interval_seconds" min="30" required>
                        <div class="form-text">建议设置为30秒以上的间隔时间</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="edit_auto_video" name="auto_generate_video" value="1">
                            <label class="form-check-label" for="edit_auto_video">更新后自动生成视频</label>
                        </div>
                        <div class="form-text">启用后，当小说有新章节更新时，将自动创建视频生成任务</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存更改</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 设置默认的预定时间为当前时间
        const now = new Date();
        const tzOffset = now.getTimezoneOffset() * 60000; // 时区偏移量，单位：毫秒
        const localISOTime = (new Date(now - tzOffset)).toISOString().slice(0, 16);
        document.getElementById('schedule_time').value = localISOTime;
        
        // 编辑任务模态框
        const editButtons = document.querySelectorAll('.edit-task');
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const taskId = this.getAttribute('data-task-id');
                const novelId = this.getAttribute('data-novel-id');
                const novelTitle = this.getAttribute('data-novel-title');
                const scheduleTime = this.getAttribute('data-schedule-time');
                const intervalSeconds = this.getAttribute('data-interval-seconds');
                const isActive = this.getAttribute('data-is-active') === "1";
                const autoVideo = this.getAttribute('data-auto-video') === "1";
                
                document.getElementById('edit_task_id').value = taskId;
                document.getElementById('edit_novel_id').value = novelId;
                document.getElementById('edit_novel_title').value = novelTitle;
                document.getElementById('edit_schedule_time').value = scheduleTime.replace(' ', 'T');
                document.getElementById('edit_interval_seconds').value = intervalSeconds;
                document.getElementById('edit_auto_video').checked = autoVideo;
            });
        });
        
        // 初始化获取所有任务的运行状态
        const checkTaskStatuses = function() {
            const taskRows = document.querySelectorAll('tr[data-task-id]');
            
            taskRows.forEach(row => {
                const taskId = row.getAttribute('data-task-id');
                
                fetch(`/task_status/${taskId}`)
                    .then(response => response.json())
                    .then(data => {
                        updateTaskStatusUI(taskId, data);
                    })
                    .catch(error => console.error('Error fetching task status:', error));
            });
        };
        
        // 更新任务状态UI
        const updateTaskStatusUI = function(taskId, statusData) {
            const row = document.querySelector(`tr[data-task-id="${taskId}"]`);
            if (!row) return;
            
            const taskStatus = row.querySelector('.task-status');
            const runningStatus = row.querySelector('.running-status');
            const pausedStatus = row.querySelector('.paused-status');
            const toggleBtn = row.querySelector('.toggle-task-btn');
            const pauseBtn = row.querySelector('.pause-task-btn');
            const resumeBtn = row.querySelector('.resume-task-btn');
            
            if (statusData.status === 'running') {
                // 任务正在运行中
                toggleBtn.classList.add('d-none');
                
                if (statusData.paused) {
                    // 任务已暂停
                    runningStatus.classList.add('d-none');
                    pausedStatus.classList.remove('d-none');
                    pauseBtn.classList.add('d-none');
                    resumeBtn.classList.remove('d-none');
                } else {
                    // 任务正在运行
                    runningStatus.classList.remove('d-none');
                    pausedStatus.classList.add('d-none');
                    pauseBtn.classList.remove('d-none');
                    resumeBtn.classList.add('d-none');
                }
            } else {
                // 任务未运行
                toggleBtn.classList.remove('d-none');
                runningStatus.classList.add('d-none');
                pausedStatus.classList.add('d-none');
                pauseBtn.classList.add('d-none');
                resumeBtn.classList.add('d-none');
            }
        };
        
        // 暂停任务
        document.querySelectorAll('.pause-task-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const taskId = this.getAttribute('data-task-id');
                
                fetch(`/pause_task/${taskId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // 更新UI显示
                        const statusData = {
                            status: 'running',
                            paused: true
                        };
                        updateTaskStatusUI(taskId, statusData);
                    }
                })
                .catch(error => console.error('Error pausing task:', error));
            });
        });
        
        // 恢复任务
        document.querySelectorAll('.resume-task-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const taskId = this.getAttribute('data-task-id');
                
                fetch(`/resume_task/${taskId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // 更新UI显示
                        const statusData = {
                            status: 'running',
                            paused: false
                        };
                        updateTaskStatusUI(taskId, statusData);
                    }
                })
                .catch(error => console.error('Error resuming task:', error));
            });
        });
        
        // 每5秒检查一次任务状态
        checkTaskStatuses();
        setInterval(checkTaskStatuses, 5000);
    });
</script>
{% endblock %} 