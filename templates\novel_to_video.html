{% extends "base.html" %}

{% block title %}小说转视频 - 小说云{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/novel_to_video.css') }}">
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0"><i class="bi bi-camera-video me-2"></i>小说转视频</h1>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#newProjectModal">
                        <i class="bi bi-plus-circle me-2"></i>新建项目
                    </button>
                </div>
                <div class="card-body">
                    <p class="card-text">将小说章节转换为带字幕的视频，支持语音合成、自定义字幕和背景音乐。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 项目列表 -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header">
                    <h2 class="h5 mb-0"><i class="bi bi-list me-2"></i>视频项目列表</h2>
                </div>
                <div class="card-body">
                    {% if projects %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>项目名称</th>
                                    <th>创建时间</th>
                                    <th>更新时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for project in projects %}
                                <tr>
                                    <td>{{ project.title }}</td>
                                    <td>{{ project.created_at }}</td>
                                    <td>{{ project.updated_at }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if project.status == 'completed' else 'warning' if project.status == 'in_progress' else 'info' }}">
                                            {{ '已完成' if project.status == 'completed' else '处理中' if project.status == 'in_progress' else '草稿' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('edit_video_project', project_id=project.id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button class="btn btn-sm btn-outline-success" onclick="renderVideo({{ project.id }})">
                                                <i class="bi bi-play"></i>
                                            </button>
                                            <button class="btn btn-sm btn-outline-danger" onclick="deleteProject({{ project.id }})">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>还没有创建任何视频项目，点击上方"新建项目"按钮开始创建
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新建项目模态框 -->
<div class="modal fade" id="newProjectModal" tabindex="-1" aria-labelledby="newProjectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="newProjectModalLabel">新建视频项目</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="newProjectForm" action="{{ url_for('create_video_project') }}" method="post">
                    <div class="mb-3">
                        <label for="projectTitle" class="form-label">项目名称</label>
                        <input type="text" class="form-control" id="projectTitle" name="title" required>
                    </div>
                    <div class="mb-3">
                        <label for="novelSelect" class="form-label">选择小说</label>
                        <select class="form-select" id="novelSelect" name="novel_id">
                            <option value="">-- 不关联小说 --</option>
                            {% for novel in novels %}
                            <option value="{{ novel.id }}">{{ novel.title }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="document.getElementById('newProjectForm').submit()">创建</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function renderVideo(projectId) {
        if(confirm('确定要开始渲染视频吗？这可能需要一些时间。')) {
            fetch(`/render_video/${projectId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'started') {
                    window.location.href = `/edit_video_project/${projectId}?tab=render`;
                } else {
                    alert('启动渲染失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发生错误，请重试');
            });
        }
    }

    function deleteProject(projectId) {
        if(confirm('确定要删除这个项目吗？此操作不可撤销。')) {
            fetch(`/delete_video_project/${projectId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    window.location.reload();
                } else {
                    alert('删除失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('发生错误，请重试');
            });
        }
    }
</script>
{% endblock %} 