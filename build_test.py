#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的构建脚本，用于快速测试
"""

import os
import sys
from pathlib import Path
import PyInstaller.__main__

def build_test_exe():
    """构建测试可执行文件"""
    
    # 获取项目根目录
    project_root = Path(__file__).parent
    
    # 测试脚本
    test_script = str(project_root / "test_exe.py")
    
    # 基本的隐藏导入
    hidden_imports = [
        "requests",
        "lxml",
        "lxml.etree",
        "sqlite3",
        "flask",
        "novel_sources",
        "novel_sources.fanqie",
        "novel_sources.fanqie_api",
        "database",
    ]
    
    # 构建PyInstaller命令参数
    args = [
        test_script,
        "--name=测试小说云",
        "--onefile",
        "--clean",
        "--noconfirm",
    ]
    
    # 添加数据文件
    if (project_root / "templates").exists():
        separator = ";" if os.name == 'nt' else ":"
        args.extend(["--add-data", f"{project_root / 'templates'}{separator}templates"])
    
    if (project_root / "static").exists():
        separator = ";" if os.name == 'nt' else ":"
        args.extend(["--add-data", f"{project_root / 'static'}{separator}static"])
    
    if (project_root / "novels.db").exists():
        separator = ";" if os.name == 'nt' else ":"
        args.extend(["--add-data", f"{project_root / 'novels.db'}{separator}."])
    
    # 添加隐藏导入
    for hidden_import in hidden_imports:
        args.extend(["--hidden-import", hidden_import])
    
    args.extend([
        "--distpath", str(project_root / "dist"),
        "--workpath", str(project_root / "build"),
        "--specpath", str(project_root),
    ])
    
    print("开始构建测试可执行文件...")
    print("命令参数:", args)
    
    try:
        PyInstaller.__main__.run(args)
        print("构建完成！")
        print(f"可执行文件位置: {project_root / 'dist' / '测试小说云.exe'}")
    except Exception as e:
        print(f"构建失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    build_test_exe()