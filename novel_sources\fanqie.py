import sys
import os
import time
import json
import requests
from tqdm import tqdm
from .fanqie_api import get_fanqie_content

# 仅导入必要功能，自定义直接调用REST API实现
class FanqieDownloader:
    def __init__(self, novel_id, db, progress_tracker=None):
        self.novel_id = novel_id
        self.db = db
        self.progress_tracker = progress_tracker
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        # 默认封面和作者信息
        self.default_cover = "https://p1-tt.byteimg.com/origin/pgc-image/9c064790db244e46926be19364c2f214.jpg"
        self.default_author = "未知作者"
    
    @staticmethod
    def search(keyword):
        """Search for novels on Fanqie"""
        try:
            url = f"https://novel.snssdk.com/api/search/new/novel/new_novel_search_list_v1?aid=1967&param=%7B%22search_key%22%3A%22{keyword}%22%2C%22current_page%22%3A1%2C%22filter_by%22%3A0%2C%22order_by%22%3A0%7D"
            response = requests.get(url, timeout=10)
            data = response.json()
            
            results = []
            if data.get('code') == 0 and 'data' in data:
                for item in data['data'].get('novels', []):
                    results.append({
                        'id': item.get('book_id'),
                        'title': item.get('book_name'),
                        'author': item.get('author') or "未知作者",
                        'cover_url': item.get('thumb_url') or "https://p1-tt.byteimg.com/origin/pgc-image/9c064790db244e46926be19364c2f214.jpg",
                        'description': item.get('abstract') or "暂无简介",
                        'source': 'fanqie'
                    })
            return results
        except Exception as e:
            print(f"Error searching Fanqie: {e}")
            return []
    
    def get_novel_info(self):
        """Get novel information from Fanqie"""
        try:
            # 初始化基本结构，确保总是存在
            novel_info = {
                'id': self.novel_id,
                'title': None,
                'author': self.default_author,
                'cover_url': self.default_cover,
                'description': "暂无简介",
                'source': 'fanqie'
            }
            
            # 使用网页解析方式获取信息，而不是 API
            url = f'https://fanqienovel.com/page/{self.novel_id}'
            response = requests.get(url, headers=self.headers, timeout=10)
            
            # 使用lxml解析HTML
            try:
                from lxml import etree
                ele = etree.HTML(response.text)
                
                # 获取章节列表
                chapters = {}
                a_elements = ele.xpath('//div[@class="chapter"]/div/a')
                
                if a_elements:
                    for a in a_elements:
                        href = a.xpath('@href')
                        if href:
                            chapters[a.xpath('text()')[0]] = href[0].split('/')[-1]
                
                # 获取标题
                title = ele.xpath('//h1/text()')
                if title:
                    novel_info['title'] = title[0]
                
                # 获取状态
                status = ele.xpath('//span[@class="info-label-yellow"]/text()')
                if status:
                    novel_info['description'] = f"状态: {status[0]}" + (f"\n{novel_info['description']}" if novel_info['description'] else "")
                
                # 尝试获取作者信息
                author_elements = ele.xpath('//div[contains(@class, "author-name")]/text()') or ele.xpath('//span[contains(@class, "author")]/text()')
                if author_elements:
                    novel_info['author'] = author_elements[0].strip()
                
                # 尝试获取封面图片
                cover_elements = ele.xpath('//div[contains(@class, "book-cover")]/img/@src') or ele.xpath('//img[contains(@class, "cover")]/@src')
                if cover_elements:
                    novel_info['cover_url'] = cover_elements[0]
                
                # 尝试获取简介
                desc_elements = ele.xpath('//div[contains(@class, "book-desc")]/text()') or ele.xpath('//div[contains(@class, "abstract")]/text()')
                if desc_elements:
                    novel_info['description'] = "\n".join([d.strip() for d in desc_elements if d.strip()])
                
            except ImportError:
                # 如果lxml不可用，使用简单的字符串处理方法
                import re
                
                # 提取标题
                title_match = re.search(r'<h1[^>]*>([^<]+)</h1>', response.text)
                if title_match:
                    novel_info['title'] = title_match.group(1).strip()
                
                # 提取作者
                author_match = re.search(r'<div[^>]*class="[^"]*author-name[^"]*"[^>]*>([^<]+)</div>', response.text) or \
                              re.search(r'<span[^>]*class="[^"]*author[^"]*"[^>]*>([^<]+)</span>', response.text)
                if author_match:
                    novel_info['author'] = author_match.group(1).strip()
                
                # 提取封面
                cover_match = re.search(r'<div[^>]*class="[^"]*book-cover[^"]*"[^>]*>.*?<img[^>]*src="([^"]+)"', response.text, re.DOTALL) or \
                             re.search(r'<img[^>]*class="[^"]*cover[^"]*"[^>]*src="([^"]+)"', response.text)
                if cover_match:
                    novel_info['cover_url'] = cover_match.group(1)
                
                # 提取简介
                desc_match = re.search(r'<div[^>]*class="[^"]*book-desc[^"]*"[^>]*>(.*?)</div>', response.text, re.DOTALL) or \
                            re.search(r'<div[^>]*class="[^"]*abstract[^"]*"[^>]*>(.*?)</div>', response.text, re.DOTALL)
                if desc_match:
                    # 移除HTML标签
                    desc = re.sub(r'<[^>]+>', '', desc_match.group(1))
                    novel_info['description'] = desc.strip()
                
                # 提取状态
                status_match = re.search(r'<span[^>]*class="[^"]*info-label-yellow[^"]*"[^>]*>([^<]+)</span>', response.text)
                if status_match:
                    status = status_match.group(1).strip()
                    novel_info['description'] = f"状态: {status}\n{novel_info['description']}"
            
            # 如果仍然没有获取到标题，使用ID作为标题
            if not novel_info['title']:
                novel_info['title'] = f"番茄小说(ID:{self.novel_id})"
            
            return novel_info
            
        except Exception as e:
            print(f"Error getting novel info: {e}")
            # 即使出错也返回基本信息，确保下载可以继续
            return {
                'id': self.novel_id,
                'title': f"番茄小说(ID:{self.novel_id})",
                'author': self.default_author,
                'cover_url': self.default_cover,
                'description': "获取信息失败",
                'source': 'fanqie'
            }
    
    def get_chapters(self):
        """Get all chapters for the novel"""
        try:
            # 使用网页抓取方式获取章节列表而不是API
            url = f'https://fanqienovel.com/page/{self.novel_id}'
            response = requests.get(url, headers=self.headers, timeout=10)
            
            chapters = []
            
            # 使用lxml解析HTML
            try:
                from lxml import etree
                ele = etree.HTML(response.text)
                
                # 抓取章节元素
                a_elements = ele.xpath('//div[@class="chapter"]/div/a')
                
                # 处理章节数据
                for idx, a in enumerate(a_elements):
                    href = a.xpath('@href')
                    title = a.xpath('text()')
                    
                    if href and title:
                        chapter_id = href[0].split('/')[-1]
                        chapters.append({
                            'chapter_id': chapter_id,
                            'title': title[0],
                            'order_index': idx
                        })
                
                if chapters:  # 如果成功获取章节，直接返回
                    return chapters
                    
            except ImportError:
                # 如果lxml不可用，使用简单的字符串处理方法
                import re
                
                # 寻找章节链接
                pattern = r'<a[^>]*href="/reader/(\d+)"[^>]*>([^<]+)</a>'
                matches = re.findall(pattern, response.text)
                
                for idx, (chapter_id, title) in enumerate(matches):
                    chapters.append({
                        'chapter_id': chapter_id,
                        'title': title.strip(),
                        'order_index': idx
                    })
                
                if chapters:  # 如果成功获取章节，直接返回
                    return chapters
            
            # 如果无法解析，返回空列表
            return chapters
            
        except Exception as e:
            print(f"Error getting chapters: {e}")
            return []
    
    def get_chapter_content(self, chapter_id):
        """Get content for a specific chapter"""
        # 使用新的重试逻辑
        retries = 3
        last_error = None
        
        while retries > 0:
            try:
                # 使用导入的 get_fanqie_content 函数
                content = get_fanqie_content(chapter_id, self.db)
                if content == 'err':  # 检查是否错误
                    raise Exception('Download failed')
                
                # 添加随机延迟
                import random
                time.sleep(random.randint(100, 200) / 1000)
                
                return content
                
            except Exception as e:
                last_error = e
                retries -= 1
                if retries == 0:
                    print(f'下载章节失败 (ID: {chapter_id}): {str(e)}')
                    break
                time.sleep(1)
        
        if last_error:
            return f"[获取章节内容出错: {str(last_error)}]"
        return f"[无法获取章节内容: {chapter_id}]"
    
    def download(self):
        """Download the entire novel"""
        # 获取小说信息
        novel_info = self.get_novel_info()
        if novel_info.get('title') == f"番茄小说(ID:{self.novel_id})":
            return False
        if not novel_info or not novel_info.get('title'):
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = '获取小说信息失败'
            print(f"无法获取小说信息，使用ID '{self.novel_id}' 作为标题")
            # 使用默认信息，确保下载流程继续
            novel_info = {
                'id': self.novel_id,
                'title': f"番茄小说(ID:{self.novel_id})",
                'author': self.default_author,
                'cover_url': self.default_cover,
                'description': "获取信息失败",
                'source': 'fanqie'
            }
        
        # 保存小说信息到数据库
        self.db.save_novel(novel_info)
        
        # 获取所有章节
        chapters = self.get_chapters()
        if not chapters:
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = '获取章节列表失败'
            print(f"无法获取章节列表: {novel_info['title']}")
            return False
        
        # 更新进度跟踪器
        if self.progress_tracker:
            self.progress_tracker[self.novel_id]['total'] = len(chapters)
            self.progress_tracker[self.novel_id]['current'] = 0
            self.progress_tracker[self.novel_id]['title'] = novel_info['title']
        
        print(f"开始下载: {novel_info['title']} (共{len(chapters)}章)")
        
        # 下载每一章
        failed_chapters = []
        successful_chapters = 0
        
        for chapter in tqdm(chapters, desc=f"下载 {novel_info['title']}"):
            try:
                # 获取章节内容
                content = self.get_chapter_content(chapter['chapter_id'])
                # 使用多种方式判断内容是否有效
                is_valid_content = (content and 
                                  not content.startswith('["\u65e0\u6cd5\u83b7\u53d6\u6807\u9898"]') and
                                  not content.startswith(r'["\\u65e0\\u6cd5\\u83b7\\u53d6\\u6807\\u9898"]') and
                                  not '无法获取标题' in content[:50] and
                                  not '\\u65e0\\u6cd5\\u83b7\\u53d6\\u6807\\u9898' in content[:50])
                
                if is_valid_content:
                    # 保存章节
                    chapter_data = {
                        'chapter_id': chapter['chapter_id'],
                        'title': chapter['title'],
                        'content': content,
                        'order_index': chapter['order_index']
                    }
                    self.db.save_chapter(self.novel_id, chapter_data)
                    successful_chapters += 1
                    print(f"已下载: {chapter['title']}")
                else:
                    failed_chapters.append({
                        'chapter_id': chapter['chapter_id'],
                        'title': chapter['title']
                    })
                    print(f"章节下载失败: {chapter['title']}，停止下载")
                    # 内容无效时立即停止下载
                    if self.progress_tracker:
                        self.progress_tracker[self.novel_id]['status'] = 'error'
                        self.progress_tracker[self.novel_id]['message'] = f'内容无效，下载停止于章节: {chapter["title"]}'
                    break
            except Exception as e:
                # 记录失败的章节并继续
                failed_chapters.append({
                    'chapter_id': chapter['chapter_id'],
                    'title': chapter['title']
                })
                print(f"章节保存失败: {chapter['title']} - {str(e)}")
            
            # 更新进度
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['current'] += 1
                current = self.progress_tracker[self.novel_id]['current']
                total = self.progress_tracker[self.novel_id]['total']
                self.progress_tracker[self.novel_id]['percentage'] = int((current / total) * 100)
            
            # 休眠以避免频率限制
            time.sleep(0.5)
        
        # 下载完成后的总结
        if self.progress_tracker:
            if failed_chapters:
                self.progress_tracker[self.novel_id]['status'] = 'partial'
                self.progress_tracker[self.novel_id]['failed_chapters'] = len(failed_chapters)
                self.progress_tracker[self.novel_id]['message'] = f'部分章节下载失败({len(failed_chapters)}章)'
            else:
                self.progress_tracker[self.novel_id]['status'] = 'completed'
                self.progress_tracker[self.novel_id]['message'] = '下载完成'
        
        # 打印下载结果摘要
        print(f"\n下载完成: {novel_info['title']}")
        print(f"成功: {successful_chapters}/{len(chapters)} 章")
        
        if failed_chapters:
            print(f"失败: {len(failed_chapters)}/{len(chapters)} 章")
            print("失败章节列表:")
            for fc in failed_chapters[:10]:  # 只显示前10个
                print(f" - {fc['title']} (ID: {fc['chapter_id']})")
            if len(failed_chapters) > 10:
                print(f" ... 以及其他 {len(failed_chapters)-10} 章")
        
        return successful_chapters > 0  # 至少成功下载一章就返回True 

    def generate_default_cover(self, title, author=None):
        """
        Generate a simple cover image with the novel title and author
        Returns: BytesIO object containing the image data
        """
        try:
            # 尝试导入必要的库
            from PIL import Image, ImageDraw, ImageFont
            from io import BytesIO
            import random
            import math
            
            # 创建默认封面尺寸和颜色
            width, height = 800, 1200
            
            # 使用渐变背景色 - 随机选择颜色组合
            bg_colors = [
                [(66, 133, 244), (52, 168, 83)],  # 蓝到绿
                [(234, 67, 53), (251, 188, 5)],   # 红到黄
                [(52, 168, 83), (66, 133, 244)],  # 绿到蓝
                [(156, 39, 176), (236, 64, 122)], # 紫到粉
                [(3, 169, 244), (0, 188, 212)]    # 天蓝到青色
            ]
            
            bg_color = random.choice(bg_colors)
            
            # 创建一个新的图像
            img = Image.new('RGB', (width, height), color=(255, 255, 255))
            draw = ImageDraw.Draw(img)
            
            # 绘制渐变背景
            for y in range(height):
                r = int(bg_color[0][0] + (bg_color[1][0] - bg_color[0][0]) * y / height)
                g = int(bg_color[0][1] + (bg_color[1][1] - bg_color[0][1]) * y / height)
                b = int(bg_color[0][2] + (bg_color[1][2] - bg_color[0][2]) * y / height)
                for x in range(width):
                    draw.point((x, y), fill=(r, g, b))
            
            # 添加一些随机装饰元素
            for _ in range(10):
                x = random.randint(0, width)
                y = random.randint(0, height)
                size = random.randint(50, 200)
                opacity = random.randint(10, 60)
                r, g, b = 255, 255, 255
                for i in range(size):
                    alpha = opacity * (1 - i/size)
                    draw.ellipse((x-i, y-i, x+i, y+i), 
                                outline=(r, g, b, int(alpha)), 
                                width=1)
            
            # 尝试加载字体，使用默认字体如果无法加载
            try:
                title_font = ImageFont.truetype("arial.ttf", 60)
                author_font = ImageFont.truetype("arial.ttf", 40)
            except:
                # 使用默认字体
                title_font = ImageFont.load_default()
                author_font = ImageFont.load_default()
            
            # 计算标题位置并绘制
            title_text = title
            if len(title_text) > 15:
                # 长标题分成多行
                words = title_text.split()
                lines = []
                current_line = ""
                
                for word in words:
                    if len(current_line + " " + word) <= 15:
                        current_line += " " + word if current_line else word
                    else:
                        lines.append(current_line)
                        current_line = word
                
                if current_line:
                    lines.append(current_line)
                
                title_text = "\n".join(lines)
            
            # 绘制标题（白色，带轮廓）
            title_position = (width//2, height//2 - 50)
            # 添加黑色轮廓
            for offset_x, offset_y in [(-2,-2), (-2,2), (2,-2), (2,2)]:
                draw.text((title_position[0]+offset_x, title_position[1]+offset_y), 
                         title_text, font=title_font, fill=(0,0,0), align="center", 
                         anchor="mm")
            # 绘制白色文本
            draw.text(title_position, title_text, font=title_font, fill=(255,255,255), 
                     align="center", anchor="mm")
            
            # 绘制作者名（如果有）
            if author:
                author_position = (width//2, height//2 + 80)
                # 添加黑色轮廓
                for offset_x, offset_y in [(-1,-1), (-1,1), (1,-1), (1,1)]:
                    draw.text((author_position[0]+offset_x, author_position[1]+offset_y), 
                             f"作者: {author}", font=author_font, fill=(0,0,0), 
                             align="center", anchor="mm")
                # 绘制白色文本
                draw.text(author_position, f"作者: {author}", font=author_font, 
                         fill=(255,255,255), align="center", anchor="mm")
            
            # 添加装饰性的"番茄小说"标记
            source_position = (width//2, height - 100)
            for offset_x, offset_y in [(-1,-1), (-1,1), (1,-1), (1,1)]:
                draw.text((source_position[0]+offset_x, source_position[1]+offset_y), 
                         "番茄小说", font=author_font, fill=(0,0,0), 
                         align="center", anchor="mm")
            draw.text(source_position, "番茄小说", font=author_font, 
                     fill=(255,255,255), align="center", anchor="mm")
            
            # 保存到内存对象
            output = BytesIO()
            img.save(output, format='JPEG', quality=95)
            output.seek(0)
            
            return output
            
        except Exception as e:
            print(f"Error generating default cover: {e}")
            # 返回None，调用者应该处理这种情况
            return None
    
    def get_cover_data(self, novel_info):
        """Get cover image data either from URL or generate a default one"""
        try:
            # 尝试从URL获取封面
            cover_url = novel_info.get('cover_url')
            if cover_url and cover_url != self.default_cover:
                response = requests.get(cover_url, timeout=10)
                if response.status_code == 200:
                    cover_data = BytesIO(response.content)
                    cover_data.seek(0)
                    return cover_data
            
            # 如果URL获取失败或使用默认封面，则生成一个
            title = novel_info.get('title', f"番茄小说(ID:{self.novel_id})")
            author = novel_info.get('author', self.default_author)
            
            return self.generate_default_cover(title, author)
            
        except Exception as e:
            print(f"Error getting cover data: {e}")
            # 尝试生成默认封面
            try:
                title = novel_info.get('title', f"番茄小说(ID:{self.novel_id})")
                author = novel_info.get('author', self.default_author)
                return self.generate_default_cover(title, author)
            except Exception as e2:
                print(f"Error generating default cover: {e2}")
                return None
                
    def update(self):
        """更新已下载的小说，添加新章节"""
        # 获取小说信息
        novel_info = self.get_novel_info()
        if not novel_info:
            error_msg = f'更新失败: 无法获取小说信息 (ID: {self.novel_id})'
            print(error_msg)
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = error_msg
            return False
            
        print(f"正在更新小说: {novel_info['title']} 作者: {novel_info['author']}")
        
        # 获取数据库中已有的章节
        local_chapters = self.db.get_chapters(self.novel_id)
        local_chapter_ids = set(chapter['chapter_id'] for chapter in local_chapters)
        
        # 获取网络上所有章节
        online_chapters = self.get_chapters()
        if not online_chapters:
            error_msg = f'无法获取章节列表 (ID: {self.novel_id})'
            print(error_msg)
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = error_msg
            return False
            
        # 找出新章节（网络章节中不在本地的）
        new_chapters = [chapter for chapter in online_chapters if chapter['chapter_id'] not in local_chapter_ids]
        
        if self.progress_tracker:
            self.progress_tracker[self.novel_id]['total'] = len(new_chapters)
            self.progress_tracker[self.novel_id]['current'] = 0
            self.progress_tracker[self.novel_id]['new_chapters'] = len(new_chapters)
            
        if not new_chapters:
            print(f"小说 '{novel_info['title']}' 已是最新，没有新章节")
            if self.progress_tracker:
                self.progress_tracker[self.novel_id]['status'] = 'completed'
                self.progress_tracker[self.novel_id]['message'] = '小说已是最新，没有新章节'
            return False
            
        print(f"发现 {len(new_chapters)} 个新章节，开始下载...")
        
        # 下载新章节
        success_count = 0
        error_count = 0
        failed_chapters = []
        
        for chapter in tqdm(new_chapters, desc=f"更新 {novel_info['title']}"):
            try:
                # 获取章节详情
                chapter_title = chapter['title']
                chapter_id = chapter['chapter_id']
                
                # 更新进度信息
                if self.progress_tracker:
                    self.progress_tracker[self.novel_id]['message'] = f"下载新章节 {success_count+1}/{len(new_chapters)}: {chapter_title}"
                
                # 获取章节内容
                content = self.get_chapter_content(chapter_id)
                # 使用多种方式判断内容是否有效
                is_valid_content = (content and 
                                  not content.startswith('["\u65e0\u6cd5\u83b7\u53d6\u6807\u9898"]') and
                                  not content.startswith(r'["\\u65e0\\u6cd5\\u83b7\\u53d6\\u6807\\u9898"]') and
                                  not '无法获取标题' in content[:50] and
                                  not '\\u65e0\\u6cd5\\u83b7\\u53d6\\u6807\\u9898' in content[:50])
                
                if is_valid_content:
                    # 保存章节
                    chapter_data = {
                        'chapter_id': chapter_id,
                        'title': chapter_title,
                        'content': content,
                        'order_index': chapter['order_index']
                    }
                    self.db.save_chapter(self.novel_id, chapter_data)
                    success_count += 1
                    print(f"已下载: {chapter_title}")
                else:
                    failed_chapters.append({
                        'chapter_id': chapter['chapter_id'],
                        'title': chapter['title']
                    })
                    error_count += 1
                    print(f"获取失败: {chapter_title}，停止更新")
                    # 内容无效时立即停止更新
                    if self.progress_tracker:
                        self.progress_tracker[self.novel_id]['status'] = 'error'
                        self.progress_tracker[self.novel_id]['message'] = f'内容无效，更新停止于章节: {chapter_title}'
                    break
                
                # 更新进度
                if self.progress_tracker:
                    self.progress_tracker[self.novel_id]['current'] += 1
                
                # 延迟以避免请求过快
                time.sleep(0.5)
            except Exception as e:
                failed_chapters.append({
                    'chapter_id': chapter['chapter_id'],
                    'title': chapter['title']
                })
                error_count += 1
                print(f"下载章节 {chapter.get('title', '未知')} 时出错: {e}")
        
        # 报告更新完成情况
        if self.progress_tracker:
            if success_count == 0:
                self.progress_tracker[self.novel_id]['status'] = 'error'
                self.progress_tracker[self.novel_id]['message'] = f'未能下载任何新章节，发生 {error_count} 个错误'
            else:
                if success_count == len(new_chapters):
                    self.progress_tracker[self.novel_id]['status'] = 'completed'
                    self.progress_tracker[self.novel_id]['message'] = f'成功下载所有 {success_count} 个新章节！'
                else:
                    self.progress_tracker[self.novel_id]['status'] = 'partial'
                    self.progress_tracker[self.novel_id]['message'] = f'下载了 {success_count}/{len(new_chapters)} 个新章节，发生 {error_count} 个错误'
        
        # 打印下载结果摘要
        print(f"\n更新完成: {novel_info['title']}")
        print(f"成功: {success_count}/{len(new_chapters)} 章")
        
        if failed_chapters:
            print(f"失败: {len(failed_chapters)}/{len(new_chapters)} 章")
            print("失败章节列表:")
            for fc in failed_chapters[:10]:  # 只显示前10个
                print(f" - {fc['title']} (ID: {fc['chapter_id']})")
            if len(failed_chapters) > 10:
                print(f" ... 以及其他 {len(failed_chapters)-10} 章")
        
        return success_count > 0 