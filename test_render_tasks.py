#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试渲染任务功能的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import NovelDatabase
import json

def test_render_tasks():
    """测试渲染任务的创建和管理功能"""
    
    # 初始化数据库
    db = NovelDatabase('novels.db')
    
    print("=== 测试渲染任务功能 ===")
    
    # 1. 创建测试小说
    print("\n1. 创建测试小说...")
    novel_data = {
        'id': 'test_novel_001',
        'title': '测试小说',
        'author': '测试作者',
        'cover_url': '',
        'source': 'manual',
        'description': '这是一个测试小说'
    }
    
    # 检查小说是否已存在
    if not db.novel_exists(novel_data['id']):
        success = db.save_novel(novel_data)
        print(f"创建小说: {'成功' if success else '失败'}")
    else:
        print("小说已存在，跳过创建")
    
    # 2. 添加测试章节
    print("\n2. 添加测试章节...")
    chapters = [
        {'chapter_id': '1', 'title': '第一章 开始', 'content': '这是第一章的内容...', 'order_index': 1},
        {'chapter_id': '2', 'title': '第二章 发展', 'content': '这是第二章的内容...', 'order_index': 2},
        {'chapter_id': '3', 'title': '第三章 高潮', 'content': '这是第三章的内容...', 'order_index': 3},
    ]
    
    for chapter in chapters:
        success = db.save_chapter(novel_data['id'], chapter)
        print(f"保存章节 {chapter['chapter_id']}: {'成功' if success else '失败'}")
    
    # 3. 创建视频项目
    print("\n3. 创建视频项目...")
    project_id = db.create_video_project("测试视频项目", novel_data['id'])
    print(f"创建项目ID: {project_id}")
    
    # 4. 添加章节到项目
    print("\n4. 添加章节到项目...")
    chapter_ids = ['1', '2', '3']
    success = db.add_chapters_to_project(project_id, novel_data['id'], chapter_ids)
    print(f"添加章节到项目: {'成功' if success else '失败'}")
    
    # 5. 创建渲染任务（新功能测试）
    print("\n5. 创建渲染任务...")
    task_title = "测试渲染任务"
    task_id = db.create_render_task(project_id, task_title, novel_id=novel_data['id'], chapter_ids=chapter_ids)
    print(f"创建任务ID: {task_id}")
    
    # 6. 获取任务详情
    print("\n6. 获取任务详情...")
    task = db.get_render_task(task_id)
    if task:
        print(f"任务标题: {task['title']}")
        print(f"项目标题: {task['project_title']}")
        print(f"小说标题: {task.get('novel_title', '未知')}")
        print(f"小说ID: {task.get('novel_id', '未知')}")
        print(f"章节ID: {task.get('chapter_ids', '未知')}")
        print(f"章节范围: {task.get('chapter_range', '未知')}")
        print(f"状态: {task.get('status', '未知')}")
        print(f"进度: {task.get('progress', 0)}")
        
        if task.get('chapters'):
            print(f"章节详情: {len(task['chapters'])} 个章节")
            for i, ch in enumerate(task['chapters']):
                print(f"  - 章节{i+1}: {ch.get('title', '未知标题')}")
    else:
        print("获取任务详情失败")
    
    # 7. 更新任务状态
    print("\n7. 更新任务状态...")
    success = db.update_render_task_status(task_id, 'processing', progress=0.5)
    print(f"更新任务状态: {'成功' if success else '失败'}")
    
    # 8. 获取所有渲染任务
    print("\n8. 获取所有渲染任务...")
    all_tasks = db.get_all_render_tasks()
    print(f"总任务数: {len(all_tasks)}")
    for task in all_tasks:
        print(f"  - 任务{task['id']}: {task['title']} | 章节范围: {task['chapter_range']} | 状态: {task['status']}")
    
    # 9. 获取待处理任务
    print("\n9. 获取待处理任务...")
    pending_tasks = db.get_pending_render_tasks()
    print(f"待处理任务数: {len(pending_tasks)}")
    
    # 10. 完成任务
    print("\n10. 完成任务...")
    success = db.update_render_task_status(task_id, 'completed', progress=1.0, output_path='/path/to/output.mp4')
    print(f"完成任务: {'成功' if success else '失败'}")
    
    # 11. 再次获取任务详情
    print("\n11. 再次获取任务详情...")
    task = db.get_render_task(task_id)
    if task:
        print(f"最终状态: {task['status']}")
        print(f"最终进度: {task['progress']}")
        print(f"输出路径: {task['output_path']}")
        print(f"完成时间: {task['completed_at']}")
    
    print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_render_tasks()