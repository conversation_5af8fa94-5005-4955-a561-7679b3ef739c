{% extends "base.html" %}

{% block title %}待更新章节 - {{ novel.title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <h1>待更新章节 - {{ novel.title }}</h1>
    
    <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">待更新章节 ({{ count }})</h5>
            <div>
                {% if count > 0 %}
                <form id="processForm" action="{{ url_for('process_pending_updates', novel_id=novel.id) }}" method="post" class="d-inline">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="checkbox" id="generateVideo" name="generate_video" value="1" checked>
                        <label class="form-check-label" for="generateVideo">生成视频</label>
                    </div>
                    <button type="submit" class="btn btn-primary btn-sm" id="processBtn">
                        处理更新
                    </button>
                </form>
                {% endif %}
                <a href="{{ url_for('view_novel', novel_id=novel.id) }}" class="btn btn-secondary btn-sm">
                    返回小说
                </a>
            </div>
        </div>
        <div class="card-body">
            {% if count > 0 %}
                <div class="alert alert-info">
                    <p>系统将在检测到更新后等待一段时间（默认10分钟），确认没有新章节更新后再自动处理。</p>
                    <p>您也可以点击"处理更新"按钮立即处理这些待更新章节。</p>
                </div>
                
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>章节ID</th>
                            <th>章节标题</th>
                            <th>更新时间</th>
                            <th>最后检查时间</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for update in pending_updates %}
                        <tr>
                            <td>{{ update.chapter_id }}</td>
                            <td>
                                {% if update.chapter_title %}
                                    {{ update.chapter_title }}
                                {% else %}
                                    未知章节
                                {% endif %}
                            </td>
                            <td>{{ update.update_time }}</td>
                            <td>{{ update.last_check_time }}</td>
                            <td>
                                {% if update.status == 'pending' %}
                                <span class="badge bg-warning">待处理</span>
                                {% elif update.status == 'processed' %}
                                <span class="badge bg-success">已处理</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ update.status }}</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="alert alert-info">
                    当前没有待更新的章节。
                </div>
            {% endif %}
        </div>
    </div>
    
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">关联的视频项目</h5>
        </div>
        <div class="card-body">
            {% if projects %}
                <div class="list-group">
                    {% for project in projects %}
                    <a href="{{ url_for('edit_video_project', project_id=project.id) }}" class="list-group-item list-group-item-action">
                        {{ project.title }}
                        <span class="badge bg-info float-end">{{ project.id }}</span>
                    </a>
                    {% endfor %}
                </div>
            {% else %}
                <div class="alert alert-warning">
                    该小说没有关联的视频项目。
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const processForm = document.getElementById('processForm');
        const processBtn = document.getElementById('processBtn');
        
        if (processForm) {
            processForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                processBtn.disabled = true;
                processBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> 处理中...';
                
                fetch(processForm.action, {
                    method: 'POST',
                    body: new FormData(processForm),
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success' || data.status === 'info') {
                        alert(data.message);
                        window.location.reload();
                    } else {
                        alert('错误: ' + data.message);
                        processBtn.disabled = false;
                        processBtn.innerHTML = '处理更新';
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('处理请求时出错');
                    processBtn.disabled = false;
                    processBtn.innerHTML = '处理更新';
                });
            });
        }
    });
</script>
{% endblock %} 