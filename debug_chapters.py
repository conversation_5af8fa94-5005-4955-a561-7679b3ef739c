#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试章节数据的脚本
"""

import sys
import os
import sqlite3
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_database():
    """调试数据库结构和数据"""
    try:
        conn = sqlite3.connect('novels.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        print("=== 数据库表结构 ===")
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        for table in tables:
            print(f"表: {table['name']}")
        
        print("\n=== 渲染任务数据 ===")
        cursor.execute("SELECT * FROM render_tasks")
        tasks = cursor.fetchall()
        print(f"找到 {len(tasks)} 个任务")
        for task in tasks:
            print(f"任务ID: {task['id']}, 项目ID: {task['project_id']}, 标题: {task['title']}")
        
        print("\n=== 视频项目数据 ===")
        cursor.execute("SELECT * FROM video_projects")
        projects = cursor.fetchall()
        print(f"找到 {len(projects)} 个项目")
        for project in projects:
            print(f"项目ID: {project['id']}, 标题: {project['title']}")
        
        print("\n=== 项目章节关联数据 ===")
        cursor.execute("SELECT * FROM video_project_chapters")
        project_chapters = cursor.fetchall()
        print(f"找到 {len(project_chapters)} 个项目章节关联")
        for pc in project_chapters:
            print(f"项目ID: {pc['project_id']}, 小说ID: {pc['novel_id']}, 章节ID: {pc['chapter_id']}, 顺序: {pc['order_index']}")
        
        print("\n=== 小说数据 ===")
        cursor.execute("SELECT * FROM novels")
        novels = cursor.fetchall()
        print(f"找到 {len(novels)} 个小说")
        for novel in novels:
            print(f"小说ID: {novel['id']}, 标题: {novel['title']}")
            
            # 检查对应的章节表
            table_name = f"novel_{novel['id']}"
            try:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table_name}")
                count_result = cursor.fetchone()
                print(f"  章节表 {table_name}: {count_result['count']} 个章节")
                
                # 显示前几个章节
                cursor.execute(f"SELECT chapter_id, title FROM {table_name} ORDER BY order_index LIMIT 5")
                chapters = cursor.fetchall()
                for chapter in chapters:
                    print(f"    章节ID: {chapter['chapter_id']}, 标题: {chapter['title']}")
                    
            except sqlite3.OperationalError as e:
                print(f"  章节表 {table_name} 不存在或出错: {e}")
        
        conn.close()
        return True
    except Exception as e:
        print(f"调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("调试数据库...")
    debug_database()