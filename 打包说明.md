# 小说云 - 打包成exe说明

## 打包方法

### 方法一：使用构建脚本（推荐）

1. 双击运行 `package.bat`
2. 脚本会自动安装依赖并打包
3. 生成的exe文件在 `dist/小说云.exe`

### 方法二：使用简单批处理

1. 双击运行 `build_simple.bat`
2. 直接使用PyInstaller命令打包
3. 生成的exe文件在 `dist/小说云.exe`

### 方法三：手动打包

打开命令行，执行：
```bash
pyinstaller --name="小说云" --windowed --onefile --clean --noconfirm --add-data "templates;templates" --add-data "static;static" --add-data "cookies;cookies" --add-data "downloads;downloads" --add-data "uploaded_videos;uploaded_videos" main.py
```

## 打包前准备

1. 确保已安装所有依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. 确保项目结构完整：
   - templates/ 文件夹存在
   - static/ 文件夹存在
   - cookies/ 文件夹存在
   - downloads/ 文件夹存在
   - uploaded_videos/ 文件夹存在

## 注意事项

1. **文件大小**：打包后的exe文件会比较大（约100-200MB），这是正常的
2. **运行环境**：打包后的exe可以在没有Python环境的Windows电脑上运行
3. **数据文件**：首次运行时，程序会自动创建必要的文件夹和数据库文件
4. **防火墙**：运行时可能需要允许程序通过防火墙
5. **管理员权限**：建议以管理员权限运行，避免权限问题

## 常见问题

### 1. 打包失败
- 检查是否安装了所有依赖
- 确保项目路径中没有中文或特殊字符
- 尝试删除build和dist文件夹后重新打包

### 2. 运行后无法找到模板文件
- 确保打包时包含了templates文件夹
- 检查--add-data参数是否正确

### 3. 文件太大
- 可以使用--onedir模式代替--onefile，生成文件夹形式的程序
- 使用UPX压缩工具减小体积

## 文件说明

- `build_exe.py` - 高级构建脚本，包含完整的打包配置
- `package.bat` - 一键打包脚本，自动安装依赖
- `build_simple.bat` - 简单打包脚本，直接使用PyInstaller
- `dist/小说云.exe` - 最终生成的可执行文件