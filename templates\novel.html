{% extends "base.html" %}

{% block title %}{{ novel.title }} - 小说云{% endblock %}

{% block content %}
<div class="card shadow-sm mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-3 col-lg-2 mb-3 mb-md-0">
                <div class="novel-detail-cover-container">
                    {% if novel.cover_url %}
                    <img src="{{ novel.cover_url }}" class="img-fluid rounded novel-detail-cover" alt="{{ novel.title }}">
                    {% else %}
                    <div class="no-cover-detail rounded">
                        <i class="bi bi-book"></i>
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="col-md-9 col-lg-10">
                <h1 class="mb-2">{{ novel.title }}</h1>
                <p class="text-muted mb-2">作者: {{ novel.author }}</p>
                <p class="mb-3">
                    <span class="badge rounded-pill bg-{{ 'success' if novel.source == 'fanqie' else ('info' if novel.source == 'qimao' else 'secondary') }}">
                        {{ '番茄小说' if novel.source == 'fanqie' else ('七猫小说' if novel.source == 'qimao' else '手动创建') }}
                    </span>
                </p>
                {% if novel.description %}
                <div class="card mb-3">
                    <div class="card-body">
                        <h5 class="card-title">简介</h5>
                        <p class="card-text">{{ novel.description }}</p>
                    </div>
                </div>
                {% endif %}
                {% if chapters %}
                <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                    <a href="{{ url_for('read_chapter', novel_id=novel.id, chapter_id=chapters[0].chapter_id) }}" class="btn btn-primary">
                        <i class="bi bi-book-half me-1"></i>开始阅读
                    </a>
                    {% if novel.source != 'manual' %}
                    <button id="updateNovelBtn" data-novel-id="{{ novel.id }}" class="btn btn-success">
                        <i class="bi bi-arrow-repeat me-1"></i>更新章节
                    </button>
                    {% endif %}
                    <button id="deleteNovelBtn" data-novel-id="{{ novel.id }}" data-novel-title="{{ novel.title }}" class="btn btn-danger">
                        <i class="bi bi-trash me-1"></i>删除小说
                    </button>
                </div>
                {% if novel.source != 'manual' %}
                <div id="updateStatus" class="mt-2 d-none">
                    <div class="alert alert-info" role="alert">
                        <div class="d-flex align-items-center">
                            <div class="spinner-border spinner-border-sm me-2" role="status">
                                <span class="visually-hidden">更新中...</span>
                            </div>
                            <div>
                                正在更新章节... <span id="updateProgress"></span>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="card shadow-sm">
    <div class="card-header">
        <h2 class="h5 mb-0"><i class="bi bi-list-ul me-2"></i>章节列表</h2>
    </div>
    <div class="card-body">
        {% if chapters %}
        <div class="row">
            {% for chapter in chapters %}
            <div class="col-md-6 col-lg-4 mb-2">
                <a href="{{ url_for('read_chapter', novel_id=novel.id, chapter_id=chapter.chapter_id) }}" class="text-decoration-none">
                    <div class="card chapter-card">
                        <div class="card-body py-2">
                            <p class="text-truncate mb-0">{{ chapter.title }}</p>
                        </div>
                    </div>
                </a>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle me-2"></i>没有找到章节内容
        </div>
        {% endif %}
    </div>
</div>

<div class="card shadow-sm mt-4">
    <div class="card-header">
        <h2 class="h5 mb-0"><i class="bi bi-plus-circle me-2"></i>添加自定义章节</h2>
    </div>
    <div class="card-body">
        <form action="{{ url_for('add_custom_chapter', novel_id=novel.id) }}" method="post">
            <div class="mb-3">
                <label for="chapterTitle" class="form-label">章节标题</label>
                <input type="text" class="form-control" id="chapterTitle" name="title" required>
            </div>
            <div class="mb-3">
                <label for="chapterContent" class="form-label">章节内容</label>
                <textarea class="form-control" id="chapterContent" name="content" rows="10" required></textarea>
            </div>
            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-save me-1"></i>保存章节
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const updateBtn = document.getElementById('updateNovelBtn');
        const updateStatus = document.getElementById('updateStatus');
        const updateProgress = document.getElementById('updateProgress');
        const deleteBtn = document.getElementById('deleteNovelBtn');
        
        if (updateBtn) {
            updateBtn.addEventListener('click', function() {
                const novelId = this.dataset.novelId;
                updateBtn.disabled = true;
                updateStatus.classList.remove('d-none');
                
                // Start the update process
                fetch(`/update_novel/${novelId}`, {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'started') {
                        checkUpdateProgress(novelId);
                    } else {
                        updateStatus.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                        updateBtn.disabled = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    updateStatus.innerHTML = '<div class="alert alert-danger">更新失败，请重试</div>';
                    updateBtn.disabled = false;
                });
            });
        }
        
        if (deleteBtn) {
            deleteBtn.addEventListener('click', function() {
                const novelId = this.dataset.novelId;
                const novelTitle = this.dataset.novelTitle;
                
                // Show confirmation dialog
                if (confirm(`确定要删除小说《${novelTitle}》吗？\n\n此操作将删除：\n- 小说的所有章节内容\n- 相关的定时更新任务\n- 相关的视频项目和设置\n- 所有相关数据\n\n此操作不可撤销！`)) {
                    deleteBtn.disabled = true;
                    deleteBtn.innerHTML = '<i class="bi bi-spinner-border spinner-border-sm me-1"></i>删除中...';
                    
                    // Send delete request
                    fetch(`/delete_novel/${novelId}`, {
                        method: 'POST'
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            alert(data.message);
                            // Redirect to home page
                            window.location.href = '/';
                        } else {
                            alert(`删除失败: ${data.message}`);
                            deleteBtn.disabled = false;
                            deleteBtn.innerHTML = '<i class="bi bi-trash me-1"></i>删除小说';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('删除失败，请重试');
                        deleteBtn.disabled = false;
                        deleteBtn.innerHTML = '<i class="bi bi-trash me-1"></i>删除小说';
                    });
                }
            });
        }
        
        function checkUpdateProgress(novelId) {
            const progressInterval = setInterval(() => {
                fetch(`/update_progress/${novelId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'not_found') {
                        clearInterval(progressInterval);
                        updateStatus.innerHTML = '<div class="alert alert-danger">更新过程丢失，请重试</div>';
                        updateBtn.disabled = false;
                        return;
                    }
                    
                    if (data.status === 'error') {
                        clearInterval(progressInterval);
                        updateStatus.innerHTML = `<div class="alert alert-danger">更新失败: ${data.message}</div>`;
                        updateBtn.disabled = false;
                        return;
                    }
                    
                    if (data.status === 'completed') {
                        clearInterval(progressInterval);
                        updateStatus.innerHTML = `<div class="alert alert-success">更新成功! ${data.new_chapters || 0} 个新章节已添加</div>`;
                        updateBtn.disabled = false;
                        
                        // Reload the page to show new chapters
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                        return;
                    }
                    
                    // Update progress information
                    if (data.current && data.total) {
                        updateProgress.textContent = `${data.current}/${data.total}`;
                    } else if (data.message) {
                        updateProgress.textContent = data.message;
                    }
                })
                .catch(error => {
                    console.error('Error checking progress:', error);
                });
            }, 1000);
        }
    });
</script>
{% endblock %} 