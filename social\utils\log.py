from pathlib import Path
from sys import stdout
from loguru import logger
import os

from conf import BASE_DIR


def log_formatter(record: dict) -> str:
    """
    Formatter for log records.
    :param dict record: Log object containing log metadata & message.
    :returns: str
    """
    colors = {
        "TRACE": "#cfe2f3",
        "INFO": "#9cbfdd",
        "DEBUG": "#8598ea",
        "WARNING": "#dcad5a",
        "SUCCESS": "#3dd08d",
        "ERROR": "#ae2c2c"
    }
    color = colors.get(record["level"].name, "#b3cfe7")
    return f"<fg #70acde>{{time:YYYY-MM-DD HH:mm:ss}}</fg #70acde> | <fg {color}>{{level}}</fg {color}>: <light-white>{{message}}</light-white>\n"


def create_logger(log_name: str, file_path: str):
    """
    Create custom logger for different business modules.
    :param str log_name: name of log
    :param str file_path: Optional path to log file
    :returns: Configured logger
    """
    def filter_record(record):
        return record["extra"].get("business_name") == log_name

    try:
        log_dir = Path(BASE_DIR / file_path).parent
        log_dir.mkdir(exist_ok=True)
        logger.add(Path(BASE_DIR / file_path), filter=filter_record, level="INFO", rotation="10 MB", retention="10 days", backtrace=True, diagnose=True)
    except Exception as e:
        # 如果无法创建日志文件，则只使用控制台日志
        print(f"无法创建日志文件: {e}")
    return logger.bind(business_name=log_name)


# Remove all existing handlers
logger.remove()

try:
    # Add a standard console handler
    logger.add(stdout, colorize=True, format=log_formatter)

    # 确保logs目录存在
    logs_dir = BASE_DIR / 'logs'
    logs_dir.mkdir(exist_ok=True)

    douyin_logger = create_logger('douyin', 'logs/douyin.log')
    tencent_logger = create_logger('tencent', 'logs/tencent.log')
    xhs_logger = create_logger('xhs', 'logs/xhs.log')
    tiktok_logger = create_logger('tiktok', 'logs/tiktok.log')
    bilibili_logger = create_logger('bilibili', 'logs/bilibili.log')
    kuaishou_logger = create_logger('kuaishou', 'logs/kuaishou.log')
except Exception as e:
    # 如果初始化日志失败，创建一个空的logger对象
    print(f"日志初始化失败: {e}")
    douyin_logger = logger.bind(business_name='douyin')
    tencent_logger = logger.bind(business_name='tencent')
    xhs_logger = logger.bind(business_name='xhs')
    tiktok_logger = logger.bind(business_name='tiktok')
    bilibili_logger = logger.bind(business_name='bilibili')
    kuaishou_logger = logger.bind(business_name='kuaishou')
