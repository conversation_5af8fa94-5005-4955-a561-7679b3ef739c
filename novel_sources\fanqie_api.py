import requests
from lxml import etree
import json


# 加密内容解析
CODE_ST = 58344
CODE_ED = 58715
charset = ["D","在","主","特","家","军","然","表","场","4","要","只","v","和","?","6","别","还","g","现","儿","岁","?","?","此","象","月","3","出","战","工","相","o","男","直","失","世","F","都","平","文","什","V","O","将","真","T","那","当","?","会","立","些","u","是","十","张","学","气","大","爱","两","命","全","后","东","性","通","被","1","它","乐","接","而","感","车","山","公","了","常","以","何","可","话","先","p","i","叫","轻","M","士","w","着","变","尔","快","l","个","说","少","色","里","安","花","远","7","难","师","放","t","报","认","面","道","S","?","克","地","度","I","好","机","U","民","写","把","万","同","水","新","没","书","电","吃","像","斯","5","为","y","白","几","日","教","看","但","第","加","候","作","上","拉","住","有","法","r","事","应","位","利","你","声","身","国","问","马","女","他","Y","比","父","x","A","H","N","s","X","边","美","对","所","金","活","回","意","到","z","从","j","知","又","内","因","点","Q","三","定","8","R","b","正","或","夫","向","德","听","更","?","得","告","并","本","q","过","记","L","让","打","f","人","就","者","去","原","满","体","做","经","K","走","如","孩","c","G","给","使","物","?","最","笑","部","?","员","等","受","k","行","一","条","果","动","光","门","头","见","往","自","解","成","处","天","能","于","名","其","发","总","母","的","死","手","入","路","进","心","来","h","时","力","多","开","已","许","d","至","由","很","界","n","小","与","Z","想","代","么","分","生","口","再","妈","望","次","西","风","种","带","J","?","实","情","才","这","?","E","我","神","格","长","觉","间","年","眼","无","不","亲","关","结","0","友","信","下","却","重","己","老","2","音","字","m","呢","明","之","前","高","P","B","目","太","e","9","起","稜","她","也","W","用","方","子","英","每","理","便","四","数","期","中","C","外","样","a","海","们","任"] 

# 全局变量，存储默认cookies
# default_cookies = [
#   {
#     "domain": "fanqienovel.com",
#     "hostOnly": True,
#     "httpOnly": False,
#     "name": "__ac_referer",
#     "path": "/reader",
#     "sameSite": "unspecified",
#     "secure": False,
#     "session": True,
#     "storeId": "0",
#     "value": "https://fanqienovel.com/page/7180279419959774247"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": **********,
#     "hostOnly": False,
#     "httpOnly": False,
#     "name": "Hm_lvt_2667d29c8e792e6fa9182c20a3013175",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": False,
#     "session": False,
#     "storeId": "0",
#     "value": "**********,**********,**********,**********"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "hostOnly": False,
#     "httpOnly": False,
#     "name": "HMACCOUNT",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": False,
#     "session": True,
#     "storeId": "0",
#     "value": "121E883E7A9A93E0"
#   },
#   {
#     "domain": "fanqienovel.com",
#     "hostOnly": True,
#     "httpOnly": False,
#     "name": "csrf_session_id",
#     "path": "/",
#     "sameSite": "no_restriction",
#     "secure": True,
#     "session": True,
#     "storeId": "0",
#     "value": "44f7e1afe838c8a16990552261a8afbc"
#   },
#   {
#     "domain": "fanqienovel.com",
#     "expirationDate": **********,
#     "hostOnly": True,
#     "httpOnly": False,
#     "name": "s_v_web_id",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": False,
#     "session": False,
#     "storeId": "0",
#     "value": "verify_malsmgrv_Tbmle98u_pNmH_4HbS_AjTB_lb60Sq8XsAiv"
#   },
#   {
#     "domain": "fanqienovel.com",
#     "expirationDate": **********,
#     "hostOnly": True,
#     "httpOnly": False,
#     "name": "novel_web_id",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": False,
#     "session": False,
#     "storeId": "0",
#     "value": "7503365360922428965"
#   },
#   {
#     "domain": "fanqienovel.com",
#     "expirationDate": 1747356502,
#     "hostOnly": True,
#     "httpOnly": False,
#     "name": "gfkadpd",
#     "path": "/",
#     "sameSite": "no_restriction",
#     "secure": True,
#     "session": False,
#     "storeId": "0",
#     "value": "2503,36144"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1754873363,
#     "hostOnly": False,
#     "httpOnly": False,
#     "name": "serial_uuid",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": False,
#     "session": False,
#     "storeId": "0",
#     "value": "7503365360922428965"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1754873363,
#     "hostOnly": False,
#     "httpOnly": False,
#     "name": "serial_webid",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": False,
#     "session": False,
#     "storeId": "0",
#     "value": "7503365360922428965"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1752281305.257974,
#     "hostOnly": False,
#     "httpOnly": False,
#     "name": "passport_csrf_token",
#     "path": "/",
#     "sameSite": "no_restriction",
#     "secure": True,
#     "session": False,
#     "storeId": "0",
#     "value": "1fe6a2df63252174dbef500c9017de31"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1752281305.258161,
#     "hostOnly": False,
#     "httpOnly": False,
#     "name": "passport_csrf_token_default",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": False,
#     "session": False,
#     "storeId": "0",
#     "value": "1fe6a2df63252174dbef500c9017de31"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1778633363.602859,
#     "hostOnly": False,
#     "httpOnly": True,
#     "name": "odin_tt",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": False,
#     "session": False,
#     "storeId": "0",
#     "value": "51862a525a2a107e233e8e4219a55877ce428d3b86e4d6cf2d9bafb9d3469167da34061027642e1ba7f482ee0608d290644481dc393b8e78e7c8052d31604a48"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1757465363.60302,
#     "hostOnly": False,
#     "httpOnly": True,
#     "name": "n_mh",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": False,
#     "session": False,
#     "storeId": "0",
#     "value": "RpgsamB0nplRzqGsl5j2eOop-JNLImreL6J_LJhZB1E"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1778201363.603072,
#     "hostOnly": False,
#     "httpOnly": True,
#     "name": "sid_guard",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": True,
#     "session": False,
#     "storeId": "0",
#     "value": "2556b0127bd5c5bc8a37f1bac7ac4072%7C1747097363%7C5184000%7CSat%2C+12-Jul-2025+00%3A49%3A23+GMT"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1752281363.603164,
#     "hostOnly": False,
#     "httpOnly": True,
#     "name": "uid_tt",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": True,
#     "session": False,
#     "storeId": "0",
#     "value": "5a1ed322a82925c1dc73de7c5104ce8d"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1752281363.603209,
#     "hostOnly": False,
#     "httpOnly": True,
#     "name": "uid_tt_ss",
#     "path": "/",
#     "sameSite": "no_restriction",
#     "secure": True,
#     "session": False,
#     "storeId": "0",
#     "value": "5a1ed322a82925c1dc73de7c5104ce8d"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1752281363.603254,
#     "hostOnly": False,
#     "httpOnly": True,
#     "name": "sid_tt",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": True,
#     "session": False,
#     "storeId": "0",
#     "value": "2556b0127bd5c5bc8a37f1bac7ac4072"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1752281363.603298,
#     "hostOnly": False,
#     "httpOnly": True,
#     "name": "sessionid",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": True,
#     "session": False,
#     "storeId": "0",
#     "value": "2556b0127bd5c5bc8a37f1bac7ac4072"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1752281363.603342,
#     "hostOnly": False,
#     "httpOnly": True,
#     "name": "sessionid_ss",
#     "path": "/",
#     "sameSite": "no_restriction",
#     "secure": True,
#     "session": False,
#     "storeId": "0",
#     "value": "2556b0127bd5c5bc8a37f1bac7ac4072"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1752281363.603385,
#     "hostOnly": False,
#     "httpOnly": True,
#     "name": "is_staff_user",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": True,
#     "session": False,
#     "storeId": "0",
#     "value": "false"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1752281363.603427,
#     "hostOnly": False,
#     "httpOnly": True,
#     "name": "sid_ucp_v1",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": True,
#     "session": False,
#     "storeId": "0",
#     "value": "1.0.0-KDIwNDE3ZjFmODYwM2JmZDhmMWQ0YTY5ZWY2YWJiNDI5ODU5NzhiNWYKHwjA2MDp0PTzBhCTrorBBhjHEyAMMKS8w_oFOAdA9AcaAmxmIiAyNTU2YjAxMjdiZDVjNWJjOGEzN2YxYmFjN2FjNDA3Mg"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": 1752281363.603474,
#     "hostOnly": False,
#     "httpOnly": True,
#     "name": "ssid_ucp_v1",
#     "path": "/",
#     "sameSite": "no_restriction",
#     "secure": True,
#     "session": False,
#     "storeId": "0",
#     "value": "1.0.0-KDIwNDE3ZjFmODYwM2JmZDhmMWQ0YTY5ZWY2YWJiNDI5ODU5NzhiNWYKHwjA2MDp0PTzBhCTrorBBhjHEyAMMKS8w_oFOAdA9AcaAmxmIiAyNTU2YjAxMjdiZDVjNWJjOGEzN2YxYmFjN2FjNDA3Mg"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "hostOnly": False,
#     "httpOnly": False,
#     "name": "Hm_lpvt_2667d29c8e792e6fa9182c20a3013175",
#     "path": "/",
#     "sameSite": "unspecified",
#     "secure": False,
#     "session": True,
#     "storeId": "0",
#     "value": "1747097390"
#   },
#   {
#     "domain": ".fanqienovel.com",
#     "expirationDate": **********.506194,
#     "hostOnly": False,
#     "httpOnly": True,
#     "name": "ttwid",
#     "path": "/",
#     "sameSite": "no_restriction",
#     "secure": True,
#     "session": False,
#     "storeId": "0",
#     "value": "1%7CmnxCjc_ymCUD_-4onv-05CpiOS054Z6ufOPyFEndsik%7C1747097390%7C99cb343d16cf90cc84a22c8679c16a89d4dd769456cc8e6bf80f90d8cce7fb1c"
#   }
# ] 

def interpreter(cc):
    """解析章节加密内容
    
    Args:
        cc: 字符的Unicode编码
        
    Returns:
        解码后的字符
    """
    bias = cc - CODE_ST
    if charset[bias] == '?':  # 特殊处理
        return chr(cc)
    return charset[bias]


def get_fanqie_content(article_id, db=None):
    """获取番茄小说内容
    
    Args:
        article_id (str): 文章ID，例如 7479832950490530329
        db: 可选的数据库连接，用于获取自定义cookie
        
    Returns:
        str: 只包含小说内容的JSON字符串
    """
    # 获取cookies，优先使用数据库中的cookie
   
    cookies_dict = {}
    
    # 如果提供了数据库连接，尝试获取自定义cookie
    if db:
        try:
            custom_cookie_json = db.get_fanqie_cookie()
           
            if custom_cookie_json:
                try:
                    custom_cookies = json.loads(custom_cookie_json)
                    if custom_cookies and isinstance(custom_cookies, list):
                        print("使用自定义番茄小说Cookie")
                        cookies = custom_cookies
                    if custom_cookies and isinstance(custom_cookies, dict):
                        print("使用自定义番茄小说Cookie")
                        cookies = [custom_cookies]    
                except Exception as e:
                    print(f"解析自定义Cookie失败: {e}")
        except Exception as e:
            print(f"获取自定义Cookie失败: {e}")
    

    # cookies = cookies.replace('true', 'True').replace('false', 'False')
    # 转换cookies为请求库需要的格式
    for cookie in cookies:
        cookies_dict[cookie['name']] = cookie['value']

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0'
    }
    
    # 构建URL
    content_url = f'https://fanqienovel.com/reader/{article_id}?enter_from=page'
    
    # 发送请求
    response = requests.get(url=content_url, headers=headers, cookies=cookies_dict)
    
    # 检查响应
    if response.status_code != 200:
        return json.dumps([f"请求失败，状态码: {response.status_code}"])
    
    # 解析HTML
    tree = etree.HTML(response.text)
    
    # 获取标题
    title = tree.xpath('//h1[@class = "muye-reader-title"]/text()')
    if not title:
        return json.dumps(["无法获取标题"])
    
    # 获取内容
    content_tags = tree.xpath('//div[@class="muye-reader-content noselect"]/div//p/text()')
    
    # 解析内容
    content = []
    for content_tag in content_tags:
        para = ''
        for char in content_tag:
            cc = ord(char)
            if CODE_ST <= cc <= CODE_ED:
                ch = interpreter(cc)
                para += ch
            else:
                para += char
        content.append(para)

    # 返回正确格式化的文本内容，每个段落之间用换行符连接
    formatted_content = "\n".join(content) if content else ""
    
    # # 添加自定义结束语
    # if formatted_content:
    #     formatted_content += "\n谢谢收听，明日再会"
        
    return formatted_content

