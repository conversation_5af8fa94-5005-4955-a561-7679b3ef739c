.chapter-card {
    transition: all 0.2s ease;
}

.chapter-card:hover {
    background-color: #f8f9fa;
    border-color: #0d6efd;
}

.video-project-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.video-project-card .card-body {
    flex: 1;
}

.settings-preview {
    background-color: #000;
    position: relative;
    width: 100%;
    height: 200px;
    border-radius: 5px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.subtitle-preview {
    position: absolute;
    width: 100%;
    text-align: center;
    padding: 10px;
}

.subtitle-preview.position-bottom {
    bottom: 20px;
}

.subtitle-preview.position-middle {
    top: 50%;
    transform: translateY(-50%);
}

.subtitle-preview.position-top {
    top: 20px;
}

.title-preview {
    position: absolute;
    width: 100%;
    text-align: center;
    padding: 10px;
    font-weight: bold;
}

.title-preview.position-bottom {
    bottom: 60px;
}

.title-preview.position-middle {
    top: 40%;
    transform: translateY(-50%);
}

.title-preview.position-top {
    top: 20px;
}

/* 进度条相关样式 */
.progress {
    height: 20px;
    margin-bottom: 10px;
    border-radius: 5px;
}

/* 渲染历史列表样式 */
.render-history-item {
    border-left: 3px solid #6c757d;
    padding-left: 10px;
    margin-bottom: 10px;
}

.render-history-item.completed {
    border-left-color: #28a745;
}

.render-history-item.error {
    border-left-color: #dc3545;
}

.render-history-item.in-progress {
    border-left-color: #ffc107;
}

/* 表单组件样式优化 */
.form-range {
    padding: 0;
}

.form-range::-webkit-slider-thumb {
    background: #0d6efd;
}

.form-range::-moz-range-thumb {
    background: #0d6efd;
}

/* 标签页样式 */
.nav-tabs .nav-link {
    color: #495057;
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    font-weight: 500;
}

/* 文本替换表格样式 */
#replacementRules td {
    vertical-align: middle;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .settings-preview {
        height: 150px;
    }
} 