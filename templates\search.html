{% extends "base.html" %}

{% block title %}搜索小说 - 小说云{% endblock %}

{% block content %}
<div class="card shadow-sm mb-4">
    <div class="card-body">
        <h1 class="card-title h3 mb-4">搜索小说</h1>
        <form action="{{ url_for('search') }}" method="post" class="mb-0">
            <div class="input-group input-group-lg">
                <input type="text" name="keyword" class="form-control" placeholder="输入小说名称或作者" value="{{ keyword if keyword else '' }}" required>
                <button class="btn btn-primary" type="submit">
                    <i class="bi bi-search me-1"></i> 搜索
                </button>
            </div>
            {% if error %}
            <div class="text-danger mt-2">{{ error }}</div>
            {% endif %}
        </form>
    </div>
</div>

<div class="card shadow-sm mb-4">
    <div class="card-header bg-success text-white">
        <h3 class="h5 mb-0"><i class="bi bi-plus-circle me-2"></i>手动创建小说</h3>
    </div>
    <div class="card-body">
        <form action="{{ url_for('create_manual_novel') }}" method="post" class="create-manual-form">
            <div class="row">
                <div class="col-md-6">
                    <label for="manual_title" class="form-label">小说标题 <span class="text-danger">*</span></label>
                    <input type="text" name="title" id="manual_title" class="form-control" placeholder="输入小说标题" required>
                </div>
                <div class="col-md-6">
                    <label for="manual_author" class="form-label">作者</label>
                    <input type="text" name="author" id="manual_author" class="form-control" placeholder="输入作者名称（可选）">
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <label for="manual_description" class="form-label">简介</label>
                    <textarea name="description" id="manual_description" class="form-control" rows="3" placeholder="输入小说简介（可选）"></textarea>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <button type="submit" class="btn btn-success create-manual-btn">
                        <i class="bi bi-plus-circle me-1"></i>创建小说
                    </button>
                    <div class="form-text">系统会自动生成唯一的小说ID，创建后您可以手动添加章节内容</div>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card shadow-sm mb-4">
    <div class="card-header bg-primary text-white">
        <h3 class="h5 mb-0"><i class="bi bi-book-half me-2"></i>通过小说ID直接下载</h3>
    </div>
    <div class="card-body">
        <form action="{{ url_for('download_by_id') }}" method="post" class="download-by-id-form">
            <div class="row align-items-end">
                <div class="col-md-8">
                    <label for="novel_id" class="form-label">小说ID</label>
                    <input type="text" name="novel_id" id="novel_id" class="form-control" placeholder="输入番茄小说或七猫小说的小说ID" required>
                    <div class="form-text">系统会优先尝试从番茄小说下载，如果失败则会尝试从七猫小说下载</div>
                </div>
                <div class="col-md-4">
                    <button type="submit" class="btn btn-primary w-100 direct-download-btn">
                        <i class="bi bi-download me-1"></i>下载小说
                    </button>
                </div>
            </div>
            <div class="progress direct-download-progress d-none mt-3" id="direct-progress">
                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
            </div>
        </form>
    </div>
</div>

{% if results %}
<div class="mb-4">
    <h2>搜索结果: "{{ keyword }}"</h2>
</div>

{% if results.fanqie %}
<div class="card shadow-sm mb-4">
    <div class="card-header bg-success text-white">
        <h3 class="h5 mb-0"><i class="bi bi-book me-2"></i>番茄小说</h3>
    </div>
    <div class="card-body">
        <div class="row">
            {% for novel in results.fanqie %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 shadow-sm novel-search-card">
                    <div class="row g-0">
                        <div class="col-4">
                            {% if novel.cover_url %}
                            <img src="{{ novel.cover_url }}" class="img-fluid rounded-start novel-search-cover" alt="{{ novel.title }}">
                            {% else %}
                            <div class="no-cover-small rounded-start">
                                <i class="bi bi-book"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-8">
                            <div class="card-body py-2">
                                <h5 class="card-title text-truncate h6">{{ novel.title }}</h5>
                                <p class="card-text small text-muted mb-2">{{ novel.author }}</p>
                                <form action="{{ url_for('download_novel') }}" method="post" class="download-form">
                                    <input type="hidden" name="novel_id" value="{{ novel.id }}">
                                    <input type="hidden" name="source" value="fanqie">
                                    <button type="submit" class="btn btn-sm btn-outline-success download-btn" data-id="{{ novel.id }}">
                                        <i class="bi bi-download me-1"></i>下载
                                    </button>
                                </form>
                                <div class="progress download-progress d-none mt-2" id="progress-{{ novel.id }}">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

{% if results.qimao %}
<div class="card shadow-sm mb-4">
    <div class="card-header bg-info text-white">
        <h3 class="h5 mb-0"><i class="bi bi-book me-2"></i>七猫小说</h3>
    </div>
    <div class="card-body">
        <div class="row">
            {% for novel in results.qimao %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100 shadow-sm novel-search-card">
                    <div class="row g-0">
                        <div class="col-4">
                            {% if novel.cover_url %}
                            <img src="{{ novel.cover_url }}" class="img-fluid rounded-start novel-search-cover" alt="{{ novel.title }}">
                            {% else %}
                            <div class="no-cover-small rounded-start">
                                <i class="bi bi-book"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-8">
                            <div class="card-body py-2">
                                <h5 class="card-title text-truncate h6">{{ novel.title }}</h5>
                                <p class="card-text small text-muted mb-2">{{ novel.author }}</p>
                                <form action="{{ url_for('download_novel') }}" method="post" class="download-form">
                                    <input type="hidden" name="novel_id" value="{{ novel.id }}">
                                    <input type="hidden" name="source" value="qimao">
                                    <button type="submit" class="btn btn-sm btn-outline-info download-btn" data-id="{{ novel.id }}">
                                        <i class="bi bi-download me-1"></i>下载
                                    </button>
                                </form>
                                <div class="progress download-progress d-none mt-2" id="progress-{{ novel.id }}">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated bg-info" role="progressbar" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

{% if not results.fanqie and not results.qimao %}
<div class="alert alert-warning">
    <i class="bi bi-exclamation-triangle me-2"></i>没有找到相关小说，请尝试其他关键词
</div>
{% endif %}

{% endif %}
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // Handle manual novel creation form submission
    $('.create-manual-form').on('submit', function(e) {
        e.preventDefault();
        var form = $(this);
        var title = form.find('input[name="title"]').val().trim();
        var createBtn = form.find('.create-manual-btn');
        
        if (!title) {
            alert('请输入小说标题');
            return;
        }
        
        createBtn.prop('disabled', true).html('<i class="bi bi-hourglass me-1"></i>正在创建...');
        
        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: form.serialize(),
            success: function(response) {
                if (response.status === 'success') {
                    // 显示成功消息
                    form.after('<div class="alert alert-success mt-3"><i class="bi bi-check-circle me-1"></i>' + response.message + '</div>');
                    
                    // 添加查看小说的链接
                    form.after('<div class="mt-3"><a href="/novel/' + response.novel_id + '" class="btn btn-outline-primary"><i class="bi bi-book me-1"></i>查看小说</a></div>');
                    
                    // 重置表单
                    form[0].reset();
                    
                    createBtn.removeClass('btn-success').addClass('btn-success')
                        .html('<i class="bi bi-check-circle me-1"></i>创建成功').prop('disabled', true);
                    
                    setTimeout(function() {
                        createBtn.removeClass('btn-success').addClass('btn-success')
                            .html('<i class="bi bi-plus-circle me-1"></i>创建小说').prop('disabled', false);
                    }, 3000);
                } else {
                    // 显示错误消息
                    form.after('<div class="alert alert-danger mt-3"><i class="bi bi-exclamation-triangle me-1"></i>' + response.message + '</div>');
                    
                    createBtn.removeClass('btn-success').addClass('btn-danger')
                        .html('<i class="bi bi-exclamation-circle me-1"></i>创建失败').prop('disabled', false);
                    
                    setTimeout(function() {
                        createBtn.removeClass('btn-danger').addClass('btn-success')
                            .html('<i class="bi bi-plus-circle me-1"></i>创建小说').prop('disabled', false);
                    }, 3000);
                }
            },
            error: function() {
                form.after('<div class="alert alert-danger mt-3"><i class="bi bi-exclamation-triangle me-1"></i>创建失败，请重试</div>');
                
                createBtn.removeClass('btn-success').addClass('btn-danger')
                    .html('<i class="bi bi-exclamation-circle me-1"></i>创建失败').prop('disabled', false);
                
                setTimeout(function() {
                    createBtn.removeClass('btn-danger').addClass('btn-success')
                        .html('<i class="bi bi-plus-circle me-1"></i>创建小说').prop('disabled', false);
                }, 3000);
            }
        });
    });

    // Handle direct ID download form submission
    $('.download-by-id-form').on('submit', function(e) {
        e.preventDefault();
        var form = $(this);
        var novelId = form.find('input[name="novel_id"]').val();
        var downloadBtn = form.find('.direct-download-btn');
        var progressBar = $('#direct-progress');
        
        downloadBtn.prop('disabled', true).html('<i class="bi bi-hourglass me-1"></i>正在下载...');
        progressBar.removeClass('d-none');
        
        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: form.serialize(),
            success: function(response) {
                if (response.status === 'started') {
                    checkDirectProgress(novelId);
                } else if (response.status === 'exists') {
                    progressBar.addClass('d-none');
                    downloadBtn.removeClass('btn-primary').addClass('btn-secondary')
                        .html('<i class="bi bi-check-circle me-1"></i>已下载').prop('disabled', true);
                    setTimeout(function() {
                        downloadBtn.removeClass('btn-secondary').addClass('btn-primary')
                            .html('<i class="bi bi-download me-1"></i>下载小说').prop('disabled', false);
                    }, 3000);
                }
            },
            error: function() {
                progressBar.addClass('d-none');
                downloadBtn.removeClass('btn-primary').addClass('btn-danger')
                    .html('<i class="bi bi-exclamation-circle me-1"></i>下载失败').prop('disabled', false);
                setTimeout(function() {
                    downloadBtn.removeClass('btn-danger').addClass('btn-primary')
                        .html('<i class="bi bi-download me-1"></i>下载小说').prop('disabled', false);
                }, 3000);
            }
        });
    });

    function checkDirectProgress(novelId) {
        $.ajax({
            url: '/progress/' + novelId,
            method: 'GET',
            success: function(response) {
                var progressBar = $('#direct-progress .progress-bar');
                var downloadBtn = $('.direct-download-btn');
                var progressContainer = $('#direct-progress');
                
                if (response.status === 'completed') {
                    progressBar.css('width', '100%');
                    progressBar.removeClass('bg-danger').addClass('bg-success');
                    
                    var successMessage = '下载完成';
                    if (response.message) {
                        successMessage = response.message;
                    }
                    
                    setTimeout(function() {
                        progressContainer.after('<div class="alert alert-success mt-2"><i class="bi bi-check-circle me-1"></i>' + successMessage + '</div>');
                        progressContainer.addClass('d-none');
                        downloadBtn.removeClass('btn-primary').addClass('btn-success')
                            .html('<i class="bi bi-check-circle me-1"></i>下载完成').prop('disabled', true);
                        
                        // Add link to the novel if download was successful
                        var formContainer = $('.download-by-id-form');
                        formContainer.append('<div class="mt-3"><a href="/novel/' + novelId + '" class="btn btn-outline-primary"><i class="bi bi-book me-1"></i>查看小说</a></div>');
                        
                        setTimeout(function() {
                            downloadBtn.removeClass('btn-success').addClass('btn-primary')
                                .html('<i class="bi bi-download me-1"></i>下载小说').prop('disabled', false);
                        }, 5000);
                    }, 1000);
                } else if (response.status === 'error') {
                    progressBar.css('width', '100%');
                    progressBar.removeClass('bg-primary').addClass('bg-danger');
                    
                    var errorMessage = '下载失败';
                    if (response.message) {
                        errorMessage = response.message;
                    }
                    
                    progressContainer.after('<div class="alert alert-danger mt-2"><i class="bi bi-exclamation-triangle me-1"></i>' + errorMessage + '</div>');
                    progressContainer.addClass('d-none');
                    downloadBtn.removeClass('btn-primary').addClass('btn-danger')
                        .html('<i class="bi bi-exclamation-circle me-1"></i>下载失败').prop('disabled', false);
                    
                    setTimeout(function() {
                        downloadBtn.removeClass('btn-danger').addClass('btn-primary')
                            .html('<i class="bi bi-download me-1"></i>下载小说').prop('disabled', false);
                    }, 5000);
                } else {
                    var progress = 0;
                    if (response.total > 0) {
                        progress = Math.round((response.current / response.total) * 100);
                    }
                    progressBar.css('width', progress + '%');
                    progressBar.attr('aria-valuenow', progress);
                    
                    var statusMessage = '正在下载...';
                    if (response.message) {
                        statusMessage = response.message;
                    }
                    
                    // Update the button with status message
                    downloadBtn.html('<i class="bi bi-hourglass me-1"></i>' + statusMessage);
                    
                    setTimeout(function() {
                        checkDirectProgress(novelId);
                    }, 1000);
                }
            },
            error: function() {
                setTimeout(function() {
                    checkDirectProgress(novelId);
                }, 2000);
            }
        });
    }

    $('.download-form').on('submit', function(e) {
        e.preventDefault();
        var form = $(this);
        var novelId = form.find('input[name="novel_id"]').val();
        var downloadBtn = form.find('.download-btn');
        var progressBar = $('#progress-' + novelId);
        
        downloadBtn.prop('disabled', true).html('<i class="bi bi-hourglass me-1"></i>正在下载...');
        progressBar.removeClass('d-none');
        
        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: form.serialize(),
            success: function(response) {
                if (response.status === 'started') {
                    checkProgress(novelId);
                } else if (response.status === 'exists') {
                    progressBar.addClass('d-none');
                    downloadBtn.removeClass('btn-outline-success btn-outline-info').addClass('btn-outline-secondary')
                        .html('<i class="bi bi-check-circle me-1"></i>已下载').prop('disabled', true);
                }
            },
            error: function() {
                progressBar.addClass('d-none');
                downloadBtn.removeClass('btn-outline-success btn-outline-info').addClass('btn-outline-danger')
                    .html('<i class="bi bi-exclamation-circle me-1"></i>下载失败').prop('disabled', false);
            }
        });
    });

    function checkProgress(novelId) {
        $.ajax({
            url: '/progress/' + novelId,
            method: 'GET',
            success: function(response) {
                var progressBar = $('#progress-' + novelId + ' .progress-bar');
                var downloadBtn = $('[data-id="' + novelId + '"]');
                var progressContainer = $('#progress-' + novelId);
                
                if (response.status === 'completed') {
                    progressBar.css('width', '100%');
                    progressBar.removeClass('bg-danger').addClass('bg-success');
                    
                    setTimeout(function() {
                        progressContainer.addClass('d-none');
                        downloadBtn.removeClass('btn-outline-success btn-outline-info').addClass('btn-success')
                            .html('<i class="bi bi-check-circle me-1"></i>下载完成').prop('disabled', true);
                        
                        // Add view link
                        var btnContainer = downloadBtn.parent();
                        btnContainer.append('<a href="/novel/' + novelId + '" class="btn btn-sm btn-outline-primary mt-2"><i class="bi bi-book me-1"></i>查看</a>');
                    }, 1000);
                } else if (response.status === 'error') {
                    progressBar.css('width', '100%');
                    progressBar.removeClass('bg-primary bg-info').addClass('bg-danger');
                    
                    var errorMessage = '下载失败';
                    if (response.message) {
                        errorMessage = response.message;
                        // Add a tooltip with the error message
                        downloadBtn.attr('title', errorMessage);
                        downloadBtn.tooltip({placement: 'bottom'});
                    }
                    
                    progressContainer.addClass('d-none');
                    downloadBtn.removeClass('btn-outline-success btn-outline-info').addClass('btn-outline-danger')
                        .html('<i class="bi bi-exclamation-circle me-1"></i>失败').prop('disabled', false);
                } else {
                    var progress = 0;
                    if (response.total > 0) {
                        progress = Math.round((response.current / response.total) * 100);
                    }
                    progressBar.css('width', progress + '%');
                    progressBar.attr('aria-valuenow', progress);
                    
                    setTimeout(function() {
                        checkProgress(novelId);
                    }, 1000);
                }
            },
            error: function() {
                setTimeout(function() {
                    checkProgress(novelId);
                }, 2000);
            }
        });
    }
});
</script>
{% endblock %} 