# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['C:\\Users\\<USER>\\Desktop\\dev\\git\\webnovel\\main.py'],
    pathex=[],
    binaries=[],
    datas=[('C:\\Users\\<USER>\\Desktop\\dev\\git\\webnovel\\templates', 'templates'), ('C:\\Users\\<USER>\\Desktop\\dev\\git\\webnovel\\static', 'static'), ('C:\\Users\\<USER>\\Desktop\\dev\\git\\webnovel\\novels.db', '.')],
    hiddenimports=['requests', 'requests.adapters', 'requests.auth', 'requests.cookies', 'requests.exceptions', 'requests.models', 'requests.sessions', 'requests.utils', 'urllib3', 'urllib3.util', 'urllib3.util.retry', 'urllib3.exceptions', 'lxml', 'lxml.etree', 'lxml.html', 'sqlite3', 'flask', 'flask.templating', 'werkzeug', 'werkzeug.serving', 'jinja2', 'jinja2.ext', 'markupsafe', 'flask_socketio', 'socketio', 'engineio', 'novel_sources', 'novel_sources.fanqie', 'novel_sources.fanqie_api', 'novel_sources.qimao', 'database', 'tqdm', 'threading', 'json', 'datetime', 'time', 'os', 'sys', 'pathlib'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['torch', 'torchvision', 'tensorflow', 'keras', 'scipy', 'matplotlib', 'pandas', 'sklearn', 'transformers', 'datasets', 'librosa', 'onnxruntime', 'sympy'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='小说云简化版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
