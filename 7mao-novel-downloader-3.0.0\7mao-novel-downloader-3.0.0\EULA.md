# 最终用户许可协议（EULA）
版权所有 © 2023-2024 星隅(shingyu)  
最后更新日期：
2024年1月11日

## 1. 引言

本许可协议（以下简称“协议”）是您（以下简称“用户”）与本爬虫程序（以下简称“软件”）之间的法律协议。一旦安装、复制或以其他方式使用本软件，即表示同意接受本协议各项条件的约束。如果用户不同意本协议的条件，用户有权选择不使用本软件。

## 2. 许可授权

2.1 本软件是基于GPLv3开源的，用户可以在GPLv3许可证的约束下，对本软件进行复制、分发和修改。如果用户违反GPLv3许可证的的任何条款，将视为违反本协议。

2.2 附加条款 (依据 GPLv3 开源许可证第七条)：

    2.2.1 代下载服务：如果用户使用本软件提供代下载服务（无论有偿无偿），用户必须在服务开始前明确告知服务接收方本软件的开源地址，以及本软件遵循的 GPLv3 许可证和本附加条款。用户还必须保证服务接收方能够自由地获取、修改和再分发本软件的源代码，不受任何额外的限制或条件。（依据 GPLv3, 7(c)）
    
    2.2.2 版权声明：用户不得移除该程序所显示的及产生的文件内的版权声明. (依据 GPLv3, 7(b))

    2.2.3 基于此程序开发的程序，如果公开发布，为保护API稳定性，不得使请求频率超过0.25秒一次。 (依据 GPLv3, 7)

## 3. 责任免除

3.1 软件以“现状”和“现有”的基础上提供，不提供任何明示的或暗示的保证，包括但不限于对适销性、特定用途的适用性和非侵权性的保证。

3.2 用户明确了解并同意，用户使用软件的风险由用户自行承担。用户必须对因使用或无法使用本软件而导致的任何直接、间接、偶然、特殊、惩罚性或后果性损害（包括但不限于业务中断、数据丢失或利润损失）负责。

3.3 在任何情况下，对于因使用或无法使用本软件而引起的任何类型的损害，本软件的作者或版权所有者均不承担任何责任。

3.4 用户明确了解并同意，本软件是一个爬虫程序，可能会涉及到著作权等法律问题。用户在使用本软件时，必须遵守相关法律法规，尊重并保护他人的合法权益。如果用户违反相关法律法规，由用户自行承担所有后果，本软件的作者或版权所有者对此不承担任何责任。

3.5 用户明确了解并同意，此软件旨在用于与Python网络爬虫和网页处理技术相关的教育和研究目的。不应将其用于任何非法活动或侵犯他人权利的行为。用户对使用此软件引发的任何法律责任和风险负有责任，作者和项目贡献者不对因使用软件而导致的任何损失或损害承担责任。

3.6 在使用本软件之前，用户必须确保其行为符合相关法律法规以及网站的使用政策。如有任何疑问或担忧，应咨询法律顾问。用户的使用行为表明其已理解并接受这些条款。

## 4. 法律及争议解决

本协议受中华人民共和国法律管辖。如发生本软件相关争议，应友好协商解决，协商不成的，任何一方可向软件作者所在地的人民法院提起诉讼。

## 5. 协议修改

5.1 本软件的作者或版权所有者保留在任何时候对本协议进行修改的权利。修改后的协议条款将在更新后的软件中公布。

5.2 如果用户不同意修改后的协议，用户有权停止使用软件。如果用户继续使用软件，则视为用户接受修改后的协议。

## 6. 许可冲突

本协议任何条款与GPLv3许可证之间存在冲突时，以GPLv3许可证为准。

## 7. 许可终止

7.1 如果用户违反本协议的任何条款，本许可协议将自动终止，无需任何通知。

7.2 在许可终止后，用户必须立即停止使用本软件，并删除所有复制的软件及其部分。

## 8. 其他条款

本协议中的标题仅为方便而设，不具有法律或契约效果。如果本协议的任何条款由于任何原因无效或不可执行，应视为可分割的，并且不影响任何其余条款的有效性和可执行性。

## 9. 最终解释权

本协议的最终解释权归软件的作者或版权所有者所有。
